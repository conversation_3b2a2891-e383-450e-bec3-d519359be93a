{"messaging_android_notification_channel_id": "mapto-channel", "messaging_android_notification_channels": [{"id": "mapto-driver-arrived-channel", "name": "Driver Arrival Notifications", "description": "Notifications for drivers", "importance": "high", "sound": "driverarrived"}, {"id": "mapto-driver-canceled-channel", "name": "Driver Cancellation Notifications", "description": "Notifications for customers", "importance": "high", "sound": "drivercanceled"}, {"id": "mapto-no-drivers-channel", "name": "No Drivers Available Notifications", "description": "Notifications for customers", "importance": "high", "sound": "nodrivers"}, {"id": "mapto-ride-aborted-channel", "name": "Ride Aborted Notifications", "description": "Notifications for ride aborted", "importance": "high", "sound": "rideaborted"}, {"id": "mapto-ride-accepted-channel", "name": "Ride Accepted Notifications", "description": "Notifications for ride accepted", "importance": "high", "sound": "rideaccepted"}, {"id": "mapto-ride-verified-channel", "name": "Ride Verified Notifications", "description": "Notifications for ride verified", "importance": "high", "sound": "rideverified"}, {"id": "mapto-safety-check-channel", "name": "Safety Check Notifications", "description": "Notifications for safety check", "importance": "high", "sound": "safetycheck"}, {"id": "mapto-ride-completed-channel", "name": "Ride Completed Notifications", "description": "Notifications for ride completed", "importance": "high", "sound": "ridecompleted"}]}