# Use Node to resolve the path for react_native_pods.rb
ENV['RCT_NEW_ARCH_ENABLED'] = '0'

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
     '#{script}',
     {paths: [process.argv[1]]},
     )", __dir__]).strip
end

# Use it to require both react-native's and this package's scripts:
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '15.0'
prepare_react_native_project!

setup_permissions([
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  'Notifications',
])

# Enable modular headers globally
use_modular_headers!

linkage = ENV['USE_FRAMEWORKS'] || 'static'
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'mapto' do
  config = use_native_modules!

  # react-native-config
  pod 'react-native-config', :path => '../node_modules/react-native-config'
  pod 'react-native-config/Extension', :path => '../node_modules/react-native-config'
    
  
 # React Native Maps requires special handling
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'GoogleMaps', '8.4.0'
  
  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true,
    :fabric_enabled => false,
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # Add configurations for different environments
  project 'mapto', {
    'Debug' => :debug,
    'Debug-Local' => :debug,
    'Debug-Development' => :debug,
    'Release' => :release,
    'Release-Local' => :release,
    'Release-Development' => :release
  }
  
  # pod 'FirebaseCore', :modular_headers => true
  # pod 'GoogleUtilities', :modular_headers => true
  target 'maptoDev' do
    inherit! :complete
  end

  target 'maptoLocal' do
    inherit! :complete
  end
end

post_install do |installer|
  react_native_post_install(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
    end
  end
end