// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* maptoTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* maptoTests.m */; };
		0A294A741F30430DA40F41C4 /* Geist-Black.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = DB817A78011940A3AAF84002 /* Geist-Black.woff2 */; };
		0C1E87AB2DA3A45D009683FD /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0C1E87AA2DA3A45D009683FD /* GoogleService-Info.plist */; };
		0C2D227E2DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227D2DDDE6B600D5F6D3 /* safetycheck.mp3 */; };
		0C2D227F2DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227C2DDDE6B600D5F6D3 /* rideverified.mp3 */; };
		0C2D22802DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227B2DDDE6B600D5F6D3 /* ridecompleted.mp3 */; };
		0C2D22812DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D22792DDDE6B600D5F6D3 /* rideaborted.mp3 */; };
		0C2D22822DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227A2DDDE6B600D5F6D3 /* rideaccepted.mp3 */; };
		0C2D22832DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227D2DDDE6B600D5F6D3 /* safetycheck.mp3 */; };
		0C2D22842DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227C2DDDE6B600D5F6D3 /* rideverified.mp3 */; };
		0C2D22852DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227B2DDDE6B600D5F6D3 /* ridecompleted.mp3 */; };
		0C2D22862DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D22792DDDE6B600D5F6D3 /* rideaborted.mp3 */; };
		0C2D22872DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227A2DDDE6B600D5F6D3 /* rideaccepted.mp3 */; };
		0C2D22882DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227D2DDDE6B600D5F6D3 /* safetycheck.mp3 */; };
		0C2D22892DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227C2DDDE6B600D5F6D3 /* rideverified.mp3 */; };
		0C2D228A2DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227B2DDDE6B600D5F6D3 /* ridecompleted.mp3 */; };
		0C2D228B2DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D22792DDDE6B600D5F6D3 /* rideaborted.mp3 */; };
		0C2D228C2DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C2D227A2DDDE6B600D5F6D3 /* rideaccepted.mp3 */; };
		0C5898152D4354EA0058F796 /* GoogleService-Info-Dev.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0C5898132D4354EA0058F796 /* GoogleService-Info-Dev.plist */; };
		0C5898182D4354F60058F796 /* GoogleService-Info-Local.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0C5898162D4354F60058F796 /* GoogleService-Info-Local.plist */; };
		0C5C29BD2DD4ACA000AE4F36 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0C5C29BC2DD4ACA000AE4F36 /* AVFoundation.framework */; };
		0C5C29C22DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C02DD4BDA700AE4F36 /* drivercanceled.mp3 */; };
		0C5C29C32DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C12DD4BDA700AE4F36 /* nodrivers.mp3 */; };
		0C5C29C42DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29BF2DD4BDA700AE4F36 /* driverarrived.mp3 */; };
		0C5C29C52DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C02DD4BDA700AE4F36 /* drivercanceled.mp3 */; };
		0C5C29C62DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C12DD4BDA700AE4F36 /* nodrivers.mp3 */; };
		0C5C29C72DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29BF2DD4BDA700AE4F36 /* driverarrived.mp3 */; };
		0C5C29C82DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C02DD4BDA700AE4F36 /* drivercanceled.mp3 */; };
		0C5C29C92DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29C12DD4BDA700AE4F36 /* nodrivers.mp3 */; };
		0C5C29CA2DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 0C5C29BF2DD4BDA700AE4F36 /* driverarrived.mp3 */; };
		0C6CA6662D42B07900DA198B /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		0C6CA6672D42B07900DA198B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		0C6CA6692D42B07900DA198B /* GooglePlaces in Frameworks */ = {isa = PBXBuildFile; productRef = 0C6CA6622D42B07900DA198B /* GooglePlaces */; };
		0C6CA66C2D42B07900DA198B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		0C6CA66D2D42B07900DA198B /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		0C6CA66E2D42B07900DA198B /* EBGaramond-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39734626D79149659EC802E6 /* EBGaramond-Bold.ttf */; };
		0C6CA66F2D42B07900DA198B /* EBGaramond-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FA32482D0EE549C98FE203B0 /* EBGaramond-BoldItalic.ttf */; };
		0C6CA6702D42B07900DA198B /* EBGaramond-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 75BD4D9C6FE140EC8C3C03C9 /* EBGaramond-ExtraBold.ttf */; };
		0C6CA6712D42B07900DA198B /* EBGaramond-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E57039672B9645ED887008A8 /* EBGaramond-ExtraBoldItalic.ttf */; };
		0C6CA6722D42B07900DA198B /* EBGaramond-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 08CA6057124D4423B19645BB /* EBGaramond-Italic.ttf */; };
		0C6CA6732D42B07900DA198B /* EBGaramond-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DDAE4DAC0A1941F4BB4F7962 /* EBGaramond-Medium.ttf */; };
		0C6CA6742D42B07900DA198B /* EBGaramond-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C01B12DE28A04480A96C01AE /* EBGaramond-MediumItalic.ttf */; };
		0C6CA6752D42B07900DA198B /* EBGaramond-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D496D97637074CE19AD079B5 /* EBGaramond-Regular.ttf */; };
		0C6CA6762D42B07900DA198B /* EBGaramond-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F7C4DF9E800F41FA8E9B1993 /* EBGaramond-SemiBold.ttf */; };
		0C6CA6772D42B07900DA198B /* EBGaramond-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8A3F1E9E3BA24BC9836B0C7E /* EBGaramond-SemiBoldItalic.ttf */; };
		0C6CA6782D42B07900DA198B /* EBGaramond-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2EB1E37BF8364A74AEBC8AC1 /* EBGaramond-Bold.woff2 */; };
		0C6CA6792D42B07900DA198B /* EBGaramond-BoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F088DA442D6A420383FDB98E /* EBGaramond-BoldItalic.woff2 */; };
		0C6CA67A2D42B07900DA198B /* EBGaramond-ExtraBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0A743A34FC3648978DD7CEAE /* EBGaramond-ExtraBold.woff2 */; };
		0C6CA67B2D42B07900DA198B /* EBGaramond-ExtraBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = EC70C87868DF449CA7447E20 /* EBGaramond-ExtraBoldItalic.woff2 */; };
		0C6CA67C2D42B07900DA198B /* EBGaramond-Italic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 97DB8C209B934DA9B66EF7D7 /* EBGaramond-Italic.woff2 */; };
		0C6CA67D2D42B07900DA198B /* EBGaramond-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C1AB828F43904D71A3BD1F63 /* EBGaramond-Medium.woff2 */; };
		0C6CA67E2D42B07900DA198B /* EBGaramond-MediumItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 4C9AFAF7A0814B9DA13D14C8 /* EBGaramond-MediumItalic.woff2 */; };
		0C6CA67F2D42B07900DA198B /* EBGaramond-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0098168BAB0A4B349F5BEED6 /* EBGaramond-Regular.woff2 */; };
		0C6CA6802D42B07900DA198B /* EBGaramond-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = CC5A8C955E984657B74096D8 /* EBGaramond-SemiBold.woff2 */; };
		0C6CA6812D42B07900DA198B /* EBGaramond-SemiBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2680830407A74246AD42F867 /* EBGaramond-SemiBoldItalic.woff2 */; };
		0C6CA6822D42B07900DA198B /* Geist-Black.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = DB817A78011940A3AAF84002 /* Geist-Black.woff2 */; };
		0C6CA6832D42B07900DA198B /* Geist-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9FB3114A6BE64286BC23B79E /* Geist-Bold.woff2 */; };
		0C6CA6842D42B07900DA198B /* Geist-Light.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C744A32D49E8430EB51280CE /* Geist-Light.woff2 */; };
		0C6CA6852D42B07900DA198B /* Geist-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F730FB5D484045988330101D /* Geist-Medium.woff2 */; };
		0C6CA6862D42B07900DA198B /* Geist-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 81438EC70B5B47BC852CC9D6 /* Geist-Regular.woff2 */; };
		0C6CA6872D42B07900DA198B /* Geist-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9496797B55E741CFB998ACDA /* Geist-SemiBold.woff2 */; };
		0C6CA6882D42B07900DA198B /* Geist-Thin.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 963BAAB20BFA4E44881348FA /* Geist-Thin.woff2 */; };
		0C6CA68A2D42B07900DA198B /* Geist-UltraBlack.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 72E6A8719017484C92C77245 /* Geist-UltraBlack.woff2 */; };
		0C6CA68B2D42B07900DA198B /* Geist-UltraLight.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 7AF5662DD3F44CA8A93206B1 /* Geist-UltraLight.woff2 */; };
		0C6CA68C2D42B07900DA198B /* GeistVariableVF.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 3C50313B00C443EC9E0D580F /* GeistVariableVF.woff2 */; };
		0C6CA68D2D42B07900DA198B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5904FE791DE2384DABE6CA91 /* PrivacyInfo.xcprivacy */; };
		0C6CA6D22D42B1AA00DA198B /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		0C6CA6D32D42B1AA00DA198B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		0C6CA6D52D42B1AA00DA198B /* GooglePlaces in Frameworks */ = {isa = PBXBuildFile; productRef = 0C6CA6CE2D42B1AA00DA198B /* GooglePlaces */; };
		0C6CA6D82D42B1AA00DA198B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		0C6CA6DA2D42B1AA00DA198B /* EBGaramond-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39734626D79149659EC802E6 /* EBGaramond-Bold.ttf */; };
		0C6CA6DB2D42B1AA00DA198B /* EBGaramond-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FA32482D0EE549C98FE203B0 /* EBGaramond-BoldItalic.ttf */; };
		0C6CA6DC2D42B1AA00DA198B /* EBGaramond-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 75BD4D9C6FE140EC8C3C03C9 /* EBGaramond-ExtraBold.ttf */; };
		0C6CA6DD2D42B1AA00DA198B /* EBGaramond-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E57039672B9645ED887008A8 /* EBGaramond-ExtraBoldItalic.ttf */; };
		0C6CA6DE2D42B1AA00DA198B /* EBGaramond-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 08CA6057124D4423B19645BB /* EBGaramond-Italic.ttf */; };
		0C6CA6DF2D42B1AA00DA198B /* EBGaramond-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DDAE4DAC0A1941F4BB4F7962 /* EBGaramond-Medium.ttf */; };
		0C6CA6E02D42B1AA00DA198B /* EBGaramond-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C01B12DE28A04480A96C01AE /* EBGaramond-MediumItalic.ttf */; };
		0C6CA6E12D42B1AA00DA198B /* EBGaramond-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D496D97637074CE19AD079B5 /* EBGaramond-Regular.ttf */; };
		0C6CA6E22D42B1AA00DA198B /* EBGaramond-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F7C4DF9E800F41FA8E9B1993 /* EBGaramond-SemiBold.ttf */; };
		0C6CA6E32D42B1AA00DA198B /* EBGaramond-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8A3F1E9E3BA24BC9836B0C7E /* EBGaramond-SemiBoldItalic.ttf */; };
		0C6CA6E42D42B1AA00DA198B /* EBGaramond-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2EB1E37BF8364A74AEBC8AC1 /* EBGaramond-Bold.woff2 */; };
		0C6CA6E52D42B1AA00DA198B /* EBGaramond-BoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F088DA442D6A420383FDB98E /* EBGaramond-BoldItalic.woff2 */; };
		0C6CA6E62D42B1AA00DA198B /* EBGaramond-ExtraBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0A743A34FC3648978DD7CEAE /* EBGaramond-ExtraBold.woff2 */; };
		0C6CA6E72D42B1AA00DA198B /* EBGaramond-ExtraBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = EC70C87868DF449CA7447E20 /* EBGaramond-ExtraBoldItalic.woff2 */; };
		0C6CA6E82D42B1AA00DA198B /* EBGaramond-Italic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 97DB8C209B934DA9B66EF7D7 /* EBGaramond-Italic.woff2 */; };
		0C6CA6E92D42B1AA00DA198B /* EBGaramond-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C1AB828F43904D71A3BD1F63 /* EBGaramond-Medium.woff2 */; };
		0C6CA6EA2D42B1AA00DA198B /* EBGaramond-MediumItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 4C9AFAF7A0814B9DA13D14C8 /* EBGaramond-MediumItalic.woff2 */; };
		0C6CA6EB2D42B1AA00DA198B /* EBGaramond-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0098168BAB0A4B349F5BEED6 /* EBGaramond-Regular.woff2 */; };
		0C6CA6EC2D42B1AA00DA198B /* EBGaramond-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = CC5A8C955E984657B74096D8 /* EBGaramond-SemiBold.woff2 */; };
		0C6CA6ED2D42B1AA00DA198B /* EBGaramond-SemiBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2680830407A74246AD42F867 /* EBGaramond-SemiBoldItalic.woff2 */; };
		0C6CA6EE2D42B1AA00DA198B /* Geist-Black.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = DB817A78011940A3AAF84002 /* Geist-Black.woff2 */; };
		0C6CA6EF2D42B1AA00DA198B /* Geist-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9FB3114A6BE64286BC23B79E /* Geist-Bold.woff2 */; };
		0C6CA6F02D42B1AA00DA198B /* Geist-Light.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C744A32D49E8430EB51280CE /* Geist-Light.woff2 */; };
		0C6CA6F12D42B1AA00DA198B /* Geist-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F730FB5D484045988330101D /* Geist-Medium.woff2 */; };
		0C6CA6F22D42B1AA00DA198B /* Geist-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 81438EC70B5B47BC852CC9D6 /* Geist-Regular.woff2 */; };
		0C6CA6F32D42B1AA00DA198B /* Geist-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9496797B55E741CFB998ACDA /* Geist-SemiBold.woff2 */; };
		0C6CA6F42D42B1AA00DA198B /* Geist-Thin.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 963BAAB20BFA4E44881348FA /* Geist-Thin.woff2 */; };
		0C6CA6F62D42B1AA00DA198B /* Geist-UltraBlack.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 72E6A8719017484C92C77245 /* Geist-UltraBlack.woff2 */; };
		0C6CA6F72D42B1AA00DA198B /* Geist-UltraLight.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 7AF5662DD3F44CA8A93206B1 /* Geist-UltraLight.woff2 */; };
		0C6CA6F82D42B1AA00DA198B /* GeistVariableVF.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 3C50313B00C443EC9E0D580F /* GeistVariableVF.woff2 */; };
		0C6CA6F92D42B1AA00DA198B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5904FE791DE2384DABE6CA91 /* PrivacyInfo.xcprivacy */; };
		0C9171432B9990C9007E62F2 /* GooglePlaces in Frameworks */ = {isa = PBXBuildFile; productRef = 0C9171422B9990C9007E62F2 /* GooglePlaces */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1509271BA05B461F861BA7F9 /* EBGaramond-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C1AB828F43904D71A3BD1F63 /* EBGaramond-Medium.woff2 */; };
		18A2C3A62D4A5B460049AE96 /* 180.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39E2D4A5B460049AE96 /* 180.png */; };
		18A2C3A72D4A5B460049AE96 /* 40.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3862D4A5B460049AE96 /* 40.png */; };
		18A2C3A82D4A5B460049AE96 /* 512.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A22D4A5B460049AE96 /* 512.png */; };
		18A2C3A92D4A5B460049AE96 /* 152.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39B2D4A5B460049AE96 /* 152.png */; };
		18A2C3AA2D4A5B460049AE96 /* 48.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3872D4A5B460049AE96 /* 48.png */; };
		18A2C3AB2D4A5B460049AE96 /* 100.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3952D4A5B460049AE96 /* 100.png */; };
		18A2C3AC2D4A5B460049AE96 /* 88.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3932D4A5B460049AE96 /* 88.png */; };
		18A2C3AD2D4A5B460049AE96 /* 76.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3902D4A5B460049AE96 /* 76.png */; };
		18A2C3AE2D4A5B460049AE96 /* 1024.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A32D4A5B460049AE96 /* 1024.png */; };
		18A2C3AF2D4A5B460049AE96 /* 20.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3832D4A5B460049AE96 /* 20.png */; };
		18A2C3B02D4A5B460049AE96 /* 66.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38E2D4A5B460049AE96 /* 66.png */; };
		18A2C3B12D4A5B460049AE96 /* 216.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A02D4A5B460049AE96 /* 216.png */; };
		18A2C3B22D4A5B460049AE96 /* 58.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38B2D4A5B460049AE96 /* 58.png */; };
		18A2C3B32D4A5B460049AE96 /* 57.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38A2D4A5B460049AE96 /* 57.png */; };
		18A2C3B42D4A5B460049AE96 /* Contents.json in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A42D4A5B460049AE96 /* Contents.json */; };
		18A2C3B52D4A5B460049AE96 /* 92.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3942D4A5B460049AE96 /* 92.png */; };
		18A2C3B62D4A5B460049AE96 /* 80.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3912D4A5B460049AE96 /* 80.png */; };
		18A2C3B72D4A5B460049AE96 /* 144.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39A2D4A5B460049AE96 /* 144.png */; };
		18A2C3B82D4A5B460049AE96 /* 172.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39D2D4A5B460049AE96 /* 172.png */; };
		18A2C3B92D4A5B460049AE96 /* 114.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3972D4A5B460049AE96 /* 114.png */; };
		18A2C3BA2D4A5B460049AE96 /* 60.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38C2D4A5B460049AE96 /* 60.png */; };
		18A2C3BB2D4A5B460049AE96 /* 64.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38D2D4A5B460049AE96 /* 64.png */; };
		18A2C3BC2D4A5B460049AE96 /* 196.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39F2D4A5B460049AE96 /* 196.png */; };
		18A2C3BD2D4A5B460049AE96 /* 120.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3982D4A5B460049AE96 /* 120.png */; };
		18A2C3BE2D4A5B460049AE96 /* 167.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39C2D4A5B460049AE96 /* 167.png */; };
		18A2C3BF2D4A5B460049AE96 /* 29.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3842D4A5B460049AE96 /* 29.png */; };
		18A2C3C02D4A5B460049AE96 /* 72.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38F2D4A5B460049AE96 /* 72.png */; };
		18A2C3C12D4A5B460049AE96 /* 50.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3882D4A5B460049AE96 /* 50.png */; };
		18A2C3C22D4A5B460049AE96 /* 256.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A12D4A5B460049AE96 /* 256.png */; };
		18A2C3C32D4A5B460049AE96 /* 128.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3992D4A5B460049AE96 /* 128.png */; };
		18A2C3C42D4A5B460049AE96 /* 32.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3852D4A5B460049AE96 /* 32.png */; };
		18A2C3C52D4A5B460049AE96 /* 87.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3922D4A5B460049AE96 /* 87.png */; };
		18A2C3C62D4A5B460049AE96 /* 102.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3962D4A5B460049AE96 /* 102.png */; };
		18A2C3C72D4A5B460049AE96 /* 55.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3892D4A5B460049AE96 /* 55.png */; };
		18A2C3C82D4A5B460049AE96 /* 16.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3822D4A5B460049AE96 /* 16.png */; };
		18A2C3C92D4A5B460049AE96 /* 180.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39E2D4A5B460049AE96 /* 180.png */; };
		18A2C3CA2D4A5B460049AE96 /* 40.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3862D4A5B460049AE96 /* 40.png */; };
		18A2C3CB2D4A5B460049AE96 /* 512.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A22D4A5B460049AE96 /* 512.png */; };
		18A2C3CC2D4A5B460049AE96 /* 152.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39B2D4A5B460049AE96 /* 152.png */; };
		18A2C3CD2D4A5B460049AE96 /* 48.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3872D4A5B460049AE96 /* 48.png */; };
		18A2C3CE2D4A5B460049AE96 /* 100.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3952D4A5B460049AE96 /* 100.png */; };
		18A2C3CF2D4A5B460049AE96 /* 88.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3932D4A5B460049AE96 /* 88.png */; };
		18A2C3D02D4A5B460049AE96 /* 76.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3902D4A5B460049AE96 /* 76.png */; };
		18A2C3D12D4A5B460049AE96 /* 1024.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A32D4A5B460049AE96 /* 1024.png */; };
		18A2C3D22D4A5B460049AE96 /* 20.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3832D4A5B460049AE96 /* 20.png */; };
		18A2C3D32D4A5B460049AE96 /* 66.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38E2D4A5B460049AE96 /* 66.png */; };
		18A2C3D42D4A5B460049AE96 /* 216.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A02D4A5B460049AE96 /* 216.png */; };
		18A2C3D52D4A5B460049AE96 /* 58.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38B2D4A5B460049AE96 /* 58.png */; };
		18A2C3D62D4A5B460049AE96 /* 57.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38A2D4A5B460049AE96 /* 57.png */; };
		18A2C3D72D4A5B460049AE96 /* Contents.json in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A42D4A5B460049AE96 /* Contents.json */; };
		18A2C3D82D4A5B460049AE96 /* 92.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3942D4A5B460049AE96 /* 92.png */; };
		18A2C3D92D4A5B460049AE96 /* 80.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3912D4A5B460049AE96 /* 80.png */; };
		18A2C3DA2D4A5B460049AE96 /* 144.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39A2D4A5B460049AE96 /* 144.png */; };
		18A2C3DB2D4A5B460049AE96 /* 172.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39D2D4A5B460049AE96 /* 172.png */; };
		18A2C3DC2D4A5B460049AE96 /* 114.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3972D4A5B460049AE96 /* 114.png */; };
		18A2C3DD2D4A5B460049AE96 /* 60.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38C2D4A5B460049AE96 /* 60.png */; };
		18A2C3DE2D4A5B460049AE96 /* 64.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38D2D4A5B460049AE96 /* 64.png */; };
		18A2C3DF2D4A5B460049AE96 /* 196.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39F2D4A5B460049AE96 /* 196.png */; };
		18A2C3E02D4A5B460049AE96 /* 120.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3982D4A5B460049AE96 /* 120.png */; };
		18A2C3E12D4A5B460049AE96 /* 167.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39C2D4A5B460049AE96 /* 167.png */; };
		18A2C3E22D4A5B460049AE96 /* 29.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3842D4A5B460049AE96 /* 29.png */; };
		18A2C3E32D4A5B460049AE96 /* 72.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38F2D4A5B460049AE96 /* 72.png */; };
		18A2C3E42D4A5B460049AE96 /* 50.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3882D4A5B460049AE96 /* 50.png */; };
		18A2C3E52D4A5B460049AE96 /* 256.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A12D4A5B460049AE96 /* 256.png */; };
		18A2C3E62D4A5B460049AE96 /* 128.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3992D4A5B460049AE96 /* 128.png */; };
		18A2C3E72D4A5B460049AE96 /* 32.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3852D4A5B460049AE96 /* 32.png */; };
		18A2C3E82D4A5B460049AE96 /* 87.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3922D4A5B460049AE96 /* 87.png */; };
		18A2C3E92D4A5B460049AE96 /* 102.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3962D4A5B460049AE96 /* 102.png */; };
		18A2C3EA2D4A5B460049AE96 /* 55.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3892D4A5B460049AE96 /* 55.png */; };
		18A2C3EB2D4A5B460049AE96 /* 16.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3822D4A5B460049AE96 /* 16.png */; };
		18A2C3EC2D4A5B460049AE96 /* 180.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39E2D4A5B460049AE96 /* 180.png */; };
		18A2C3ED2D4A5B460049AE96 /* 40.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3862D4A5B460049AE96 /* 40.png */; };
		18A2C3EE2D4A5B460049AE96 /* 512.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A22D4A5B460049AE96 /* 512.png */; };
		18A2C3EF2D4A5B460049AE96 /* 152.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39B2D4A5B460049AE96 /* 152.png */; };
		18A2C3F02D4A5B460049AE96 /* 48.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3872D4A5B460049AE96 /* 48.png */; };
		18A2C3F12D4A5B460049AE96 /* 100.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3952D4A5B460049AE96 /* 100.png */; };
		18A2C3F22D4A5B460049AE96 /* 88.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3932D4A5B460049AE96 /* 88.png */; };
		18A2C3F32D4A5B460049AE96 /* 76.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3902D4A5B460049AE96 /* 76.png */; };
		18A2C3F42D4A5B460049AE96 /* 1024.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A32D4A5B460049AE96 /* 1024.png */; };
		18A2C3F52D4A5B460049AE96 /* 20.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3832D4A5B460049AE96 /* 20.png */; };
		18A2C3F62D4A5B460049AE96 /* 66.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38E2D4A5B460049AE96 /* 66.png */; };
		18A2C3F72D4A5B460049AE96 /* 216.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A02D4A5B460049AE96 /* 216.png */; };
		18A2C3F82D4A5B460049AE96 /* 58.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38B2D4A5B460049AE96 /* 58.png */; };
		18A2C3F92D4A5B460049AE96 /* 57.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38A2D4A5B460049AE96 /* 57.png */; };
		18A2C3FA2D4A5B460049AE96 /* Contents.json in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A42D4A5B460049AE96 /* Contents.json */; };
		18A2C3FB2D4A5B460049AE96 /* 92.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3942D4A5B460049AE96 /* 92.png */; };
		18A2C3FC2D4A5B460049AE96 /* 80.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3912D4A5B460049AE96 /* 80.png */; };
		18A2C3FD2D4A5B460049AE96 /* 144.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39A2D4A5B460049AE96 /* 144.png */; };
		18A2C3FE2D4A5B460049AE96 /* 172.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39D2D4A5B460049AE96 /* 172.png */; };
		18A2C3FF2D4A5B460049AE96 /* 114.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3972D4A5B460049AE96 /* 114.png */; };
		18A2C4002D4A5B460049AE96 /* 60.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38C2D4A5B460049AE96 /* 60.png */; };
		18A2C4012D4A5B460049AE96 /* 64.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38D2D4A5B460049AE96 /* 64.png */; };
		18A2C4022D4A5B460049AE96 /* 196.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39F2D4A5B460049AE96 /* 196.png */; };
		18A2C4032D4A5B460049AE96 /* 120.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3982D4A5B460049AE96 /* 120.png */; };
		18A2C4042D4A5B460049AE96 /* 167.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39C2D4A5B460049AE96 /* 167.png */; };
		18A2C4052D4A5B460049AE96 /* 29.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3842D4A5B460049AE96 /* 29.png */; };
		18A2C4062D4A5B460049AE96 /* 72.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38F2D4A5B460049AE96 /* 72.png */; };
		18A2C4072D4A5B460049AE96 /* 50.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3882D4A5B460049AE96 /* 50.png */; };
		18A2C4082D4A5B460049AE96 /* 256.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A12D4A5B460049AE96 /* 256.png */; };
		18A2C4092D4A5B460049AE96 /* 128.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3992D4A5B460049AE96 /* 128.png */; };
		18A2C40A2D4A5B460049AE96 /* 32.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3852D4A5B460049AE96 /* 32.png */; };
		18A2C40B2D4A5B460049AE96 /* 87.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3922D4A5B460049AE96 /* 87.png */; };
		18A2C40C2D4A5B460049AE96 /* 102.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3962D4A5B460049AE96 /* 102.png */; };
		18A2C40D2D4A5B460049AE96 /* 55.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3892D4A5B460049AE96 /* 55.png */; };
		18A2C40E2D4A5B460049AE96 /* 16.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3822D4A5B460049AE96 /* 16.png */; };
		18A2C40F2D4A5B460049AE96 /* 180.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39E2D4A5B460049AE96 /* 180.png */; };
		18A2C4102D4A5B460049AE96 /* 40.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3862D4A5B460049AE96 /* 40.png */; };
		18A2C4112D4A5B460049AE96 /* 512.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A22D4A5B460049AE96 /* 512.png */; };
		18A2C4122D4A5B460049AE96 /* 152.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39B2D4A5B460049AE96 /* 152.png */; };
		18A2C4132D4A5B460049AE96 /* 48.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3872D4A5B460049AE96 /* 48.png */; };
		18A2C4142D4A5B460049AE96 /* 100.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3952D4A5B460049AE96 /* 100.png */; };
		18A2C4152D4A5B460049AE96 /* 88.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3932D4A5B460049AE96 /* 88.png */; };
		18A2C4162D4A5B460049AE96 /* 76.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3902D4A5B460049AE96 /* 76.png */; };
		18A2C4172D4A5B460049AE96 /* 1024.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A32D4A5B460049AE96 /* 1024.png */; };
		18A2C4182D4A5B460049AE96 /* 20.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3832D4A5B460049AE96 /* 20.png */; };
		18A2C4192D4A5B460049AE96 /* 66.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38E2D4A5B460049AE96 /* 66.png */; };
		18A2C41A2D4A5B460049AE96 /* 216.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A02D4A5B460049AE96 /* 216.png */; };
		18A2C41B2D4A5B460049AE96 /* 58.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38B2D4A5B460049AE96 /* 58.png */; };
		18A2C41C2D4A5B460049AE96 /* 57.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38A2D4A5B460049AE96 /* 57.png */; };
		18A2C41D2D4A5B460049AE96 /* Contents.json in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A42D4A5B460049AE96 /* Contents.json */; };
		18A2C41E2D4A5B460049AE96 /* 92.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3942D4A5B460049AE96 /* 92.png */; };
		18A2C41F2D4A5B460049AE96 /* 80.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3912D4A5B460049AE96 /* 80.png */; };
		18A2C4202D4A5B460049AE96 /* 144.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39A2D4A5B460049AE96 /* 144.png */; };
		18A2C4212D4A5B460049AE96 /* 172.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39D2D4A5B460049AE96 /* 172.png */; };
		18A2C4222D4A5B460049AE96 /* 114.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3972D4A5B460049AE96 /* 114.png */; };
		18A2C4232D4A5B460049AE96 /* 60.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38C2D4A5B460049AE96 /* 60.png */; };
		18A2C4242D4A5B460049AE96 /* 64.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38D2D4A5B460049AE96 /* 64.png */; };
		18A2C4252D4A5B460049AE96 /* 196.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39F2D4A5B460049AE96 /* 196.png */; };
		18A2C4262D4A5B460049AE96 /* 120.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3982D4A5B460049AE96 /* 120.png */; };
		18A2C4272D4A5B460049AE96 /* 167.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C39C2D4A5B460049AE96 /* 167.png */; };
		18A2C4282D4A5B460049AE96 /* 29.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3842D4A5B460049AE96 /* 29.png */; };
		18A2C4292D4A5B460049AE96 /* 72.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C38F2D4A5B460049AE96 /* 72.png */; };
		18A2C42A2D4A5B460049AE96 /* 50.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3882D4A5B460049AE96 /* 50.png */; };
		18A2C42B2D4A5B460049AE96 /* 256.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3A12D4A5B460049AE96 /* 256.png */; };
		18A2C42C2D4A5B460049AE96 /* 128.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3992D4A5B460049AE96 /* 128.png */; };
		18A2C42D2D4A5B460049AE96 /* 32.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3852D4A5B460049AE96 /* 32.png */; };
		18A2C42E2D4A5B460049AE96 /* 87.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3922D4A5B460049AE96 /* 87.png */; };
		18A2C42F2D4A5B460049AE96 /* 102.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3962D4A5B460049AE96 /* 102.png */; };
		18A2C4302D4A5B460049AE96 /* 55.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3892D4A5B460049AE96 /* 55.png */; };
		18A2C4312D4A5B460049AE96 /* 16.png in Resources */ = {isa = PBXBuildFile; fileRef = 18A2C3822D4A5B460049AE96 /* 16.png */; };
		256E76CA139F4816AAB78A42 /* EBGaramond-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2EB1E37BF8364A74AEBC8AC1 /* EBGaramond-Bold.woff2 */; };
		32A0FC88A8BAD37DA8F60DF6 /* Pods_mapto_maptoLocal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F475FDD13EEAE342B7DDE17 /* Pods_mapto_maptoLocal.framework */; };
		33F793609799445EB19FE0D5 /* EBGaramond-ExtraBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = EC70C87868DF449CA7447E20 /* EBGaramond-ExtraBoldItalic.woff2 */; };
		3962C592464E4066ACDD6045 /* EBGaramond-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DDAE4DAC0A1941F4BB4F7962 /* EBGaramond-Medium.ttf */; };
		3B4DDE66ED5E48D0AAB46E3B /* Geist-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 81438EC70B5B47BC852CC9D6 /* Geist-Regular.woff2 */; };
		3E341C94CC714049923225CA /* EBGaramond-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8A3F1E9E3BA24BC9836B0C7E /* EBGaramond-SemiBoldItalic.ttf */; };
		45B56D8553934324922E8104 /* EBGaramond-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 08CA6057124D4423B19645BB /* EBGaramond-Italic.ttf */; };
		48F3139124B246ADAFED1EBA /* EBGaramond-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 39734626D79149659EC802E6 /* EBGaramond-Bold.ttf */; };
		5478D4A58F76459E8C4D4092 /* EBGaramond-Regular.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0098168BAB0A4B349F5BEED6 /* EBGaramond-Regular.woff2 */; };
		5579B1397F5B1419F9F0341A /* Pods_mapto_maptoDev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 52EC4F0654B5FB18D83BF154 /* Pods_mapto_maptoDev.framework */; };
		58C9FEF14F89495CA7BFE433 /* Geist-Thin.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 963BAAB20BFA4E44881348FA /* Geist-Thin.woff2 */; };
		598B99BFD270460D82080CD6 /* Geist-Bold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9FB3114A6BE64286BC23B79E /* Geist-Bold.woff2 */; };
		6118BF666B674A51A196F6F8 /* EBGaramond-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C01B12DE28A04480A96C01AE /* EBGaramond-MediumItalic.ttf */; };
		643694362258DA871EED5E65 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		6BDBD4C97DF24261937734A6 /* EBGaramond-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = CC5A8C955E984657B74096D8 /* EBGaramond-SemiBold.woff2 */; };
		703BEE6B536D4B70B51FBC74 /* Geist-Light.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = C744A32D49E8430EB51280CE /* Geist-Light.woff2 */; };
		70438A5F1BC14B32953D75BD /* Geist-Medium.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F730FB5D484045988330101D /* Geist-Medium.woff2 */; };
		7237066F3980482C9CDAAD03 /* Geist-UltraLight.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 7AF5662DD3F44CA8A93206B1 /* Geist-UltraLight.woff2 */; };
		7BAF18875C6F4095B1E2AFEF /* EBGaramond-MediumItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 4C9AFAF7A0814B9DA13D14C8 /* EBGaramond-MediumItalic.woff2 */; };
		80FF851E89B24816BF958906 /* EBGaramond-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F7C4DF9E800F41FA8E9B1993 /* EBGaramond-SemiBold.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		872B851BE0544FBA9BF06651 /* EBGaramond-BoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = F088DA442D6A420383FDB98E /* EBGaramond-BoldItalic.woff2 */; };
		89B60CAB6AB195EDC8CE5EA1 /* Pods_mapto.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0CF7000B9ACADA940E64F07A /* Pods_mapto.framework */; };
		8C805498991246A3BA6E305F /* Geist-UltraBlack.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 72E6A8719017484C92C77245 /* Geist-UltraBlack.woff2 */; };
		95F82252F25F4E438375FFB7 /* EBGaramond-ExtraBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 0A743A34FC3648978DD7CEAE /* EBGaramond-ExtraBold.woff2 */; };
		9BCB77BF7F0341C7A99F8712 /* EBGaramond-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 75BD4D9C6FE140EC8C3C03C9 /* EBGaramond-ExtraBold.ttf */; };
		B34B6E2613D045F5934C5E93 /* EBGaramond-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D496D97637074CE19AD079B5 /* EBGaramond-Regular.ttf */; };
		BA9F9204F669458B831C944F /* EBGaramond-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E57039672B9645ED887008A8 /* EBGaramond-ExtraBoldItalic.ttf */; };
		D609BE5ED4D64B6BA011F882 /* Geist-SemiBold.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 9496797B55E741CFB998ACDA /* Geist-SemiBold.woff2 */; };
		D7A6448F346945FBAD1EF64B /* EBGaramond-Italic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 97DB8C209B934DA9B66EF7D7 /* EBGaramond-Italic.woff2 */; };
		DAB22DC67D3A490EBA453065 /* EBGaramond-SemiBoldItalic.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 2680830407A74246AD42F867 /* EBGaramond-SemiBoldItalic.woff2 */; };
		E63ACEA5117641F18BF48B26 /* GeistVariableVF.woff2 in Resources */ = {isa = PBXBuildFile; fileRef = 3C50313B00C443EC9E0D580F /* GeistVariableVF.woff2 */; };
		ECE0B6FCBF1B5D003C3C219B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5904FE791DE2384DABE6CA91 /* PrivacyInfo.xcprivacy */; };
		FE4EA7483A044453BB6B64E1 /* EBGaramond-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FA32482D0EE549C98FE203B0 /* EBGaramond-BoldItalic.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = mapto;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0098168BAB0A4B349F5BEED6 /* EBGaramond-Regular.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Regular.woff2"; path = "../assets/fonts/EBGaramond-Regular.woff2"; sourceTree = "<group>"; };
		00E356EE1AD99517003FC87E /* maptoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = maptoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* maptoTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = maptoTests.m; sourceTree = "<group>"; };
		08CA6057124D4423B19645BB /* EBGaramond-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Italic.ttf"; path = "../assets/fonts/EBGaramond-Italic.ttf"; sourceTree = "<group>"; };
		0A743A34FC3648978DD7CEAE /* EBGaramond-ExtraBold.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-ExtraBold.woff2"; path = "../assets/fonts/EBGaramond-ExtraBold.woff2"; sourceTree = "<group>"; };
		0C1E87AA2DA3A45D009683FD /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		0C2D22792DDDE6B600D5F6D3 /* rideaborted.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = rideaborted.mp3; sourceTree = "<group>"; };
		0C2D227A2DDDE6B600D5F6D3 /* rideaccepted.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = rideaccepted.mp3; sourceTree = "<group>"; };
		0C2D227B2DDDE6B600D5F6D3 /* ridecompleted.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = ridecompleted.mp3; sourceTree = "<group>"; };
		0C2D227C2DDDE6B600D5F6D3 /* rideverified.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = rideverified.mp3; sourceTree = "<group>"; };
		0C2D227D2DDDE6B600D5F6D3 /* safetycheck.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = safetycheck.mp3; sourceTree = "<group>"; };
		0C5898132D4354EA0058F796 /* GoogleService-Info-Dev.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info-Dev.plist"; sourceTree = "<group>"; };
		0C5898162D4354F60058F796 /* GoogleService-Info-Local.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info-Local.plist"; sourceTree = "<group>"; };
		0C5C29BC2DD4ACA000AE4F36 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		0C5C29BF2DD4BDA700AE4F36 /* driverarrived.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = driverarrived.mp3; sourceTree = "<group>"; };
		0C5C29C02DD4BDA700AE4F36 /* drivercanceled.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = drivercanceled.mp3; sourceTree = "<group>"; };
		0C5C29C12DD4BDA700AE4F36 /* nodrivers.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = nodrivers.mp3; sourceTree = "<group>"; };
		0C6CA6952D42B07900DA198B /* maptoDev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = maptoDev.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0C6CA6962D42B07900DA198B /* maptoDev-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "maptoDev-Info.plist"; path = "/Users/<USER>/Documents/fuel/ios/maptoDev-Info.plist"; sourceTree = "<absolute>"; };
		0C6CA7012D42B1AA00DA198B /* maptoLocal.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = maptoLocal.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0C6CA7022D42B1AA00DA198B /* maptoLocal-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "maptoLocal-Info.plist"; path = "/Users/<USER>/Documents/fuel/ios/maptoLocal-Info.plist"; sourceTree = "<absolute>"; };
		0CF7000B9ACADA940E64F07A /* Pods_mapto.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_mapto.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07F961A680F5B00A75B9A /* mapto.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = mapto.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = mapto/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = mapto/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = mapto/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = mapto/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = mapto/main.m; sourceTree = "<group>"; };
		180807372D4271A5000F3C2B /* mapto.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = mapto.entitlements; path = mapto/mapto.entitlements; sourceTree = "<group>"; };
		18A2C3822D4A5B460049AE96 /* 16.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 16.png; sourceTree = "<group>"; };
		18A2C3832D4A5B460049AE96 /* 20.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 20.png; sourceTree = "<group>"; };
		18A2C3842D4A5B460049AE96 /* 29.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 29.png; sourceTree = "<group>"; };
		18A2C3852D4A5B460049AE96 /* 32.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 32.png; sourceTree = "<group>"; };
		18A2C3862D4A5B460049AE96 /* 40.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 40.png; sourceTree = "<group>"; };
		18A2C3872D4A5B460049AE96 /* 48.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 48.png; sourceTree = "<group>"; };
		18A2C3882D4A5B460049AE96 /* 50.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 50.png; sourceTree = "<group>"; };
		18A2C3892D4A5B460049AE96 /* 55.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 55.png; sourceTree = "<group>"; };
		18A2C38A2D4A5B460049AE96 /* 57.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 57.png; sourceTree = "<group>"; };
		18A2C38B2D4A5B460049AE96 /* 58.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 58.png; sourceTree = "<group>"; };
		18A2C38C2D4A5B460049AE96 /* 60.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 60.png; sourceTree = "<group>"; };
		18A2C38D2D4A5B460049AE96 /* 64.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 64.png; sourceTree = "<group>"; };
		18A2C38E2D4A5B460049AE96 /* 66.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 66.png; sourceTree = "<group>"; };
		18A2C38F2D4A5B460049AE96 /* 72.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 72.png; sourceTree = "<group>"; };
		18A2C3902D4A5B460049AE96 /* 76.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 76.png; sourceTree = "<group>"; };
		18A2C3912D4A5B460049AE96 /* 80.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 80.png; sourceTree = "<group>"; };
		18A2C3922D4A5B460049AE96 /* 87.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 87.png; sourceTree = "<group>"; };
		18A2C3932D4A5B460049AE96 /* 88.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 88.png; sourceTree = "<group>"; };
		18A2C3942D4A5B460049AE96 /* 92.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 92.png; sourceTree = "<group>"; };
		18A2C3952D4A5B460049AE96 /* 100.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 100.png; sourceTree = "<group>"; };
		18A2C3962D4A5B460049AE96 /* 102.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 102.png; sourceTree = "<group>"; };
		18A2C3972D4A5B460049AE96 /* 114.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 114.png; sourceTree = "<group>"; };
		18A2C3982D4A5B460049AE96 /* 120.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 120.png; sourceTree = "<group>"; };
		18A2C3992D4A5B460049AE96 /* 128.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 128.png; sourceTree = "<group>"; };
		18A2C39A2D4A5B460049AE96 /* 144.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 144.png; sourceTree = "<group>"; };
		18A2C39B2D4A5B460049AE96 /* 152.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 152.png; sourceTree = "<group>"; };
		18A2C39C2D4A5B460049AE96 /* 167.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 167.png; sourceTree = "<group>"; };
		18A2C39D2D4A5B460049AE96 /* 172.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 172.png; sourceTree = "<group>"; };
		18A2C39E2D4A5B460049AE96 /* 180.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 180.png; sourceTree = "<group>"; };
		18A2C39F2D4A5B460049AE96 /* 196.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 196.png; sourceTree = "<group>"; };
		18A2C3A02D4A5B460049AE96 /* 216.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 216.png; sourceTree = "<group>"; };
		18A2C3A12D4A5B460049AE96 /* 256.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 256.png; sourceTree = "<group>"; };
		18A2C3A22D4A5B460049AE96 /* 512.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 512.png; sourceTree = "<group>"; };
		18A2C3A32D4A5B460049AE96 /* 1024.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = 1024.png; sourceTree = "<group>"; };
		18A2C3A42D4A5B460049AE96 /* Contents.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = Contents.json; sourceTree = "<group>"; };
		2680830407A74246AD42F867 /* EBGaramond-SemiBoldItalic.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-SemiBoldItalic.woff2"; path = "../assets/fonts/EBGaramond-SemiBoldItalic.woff2"; sourceTree = "<group>"; };
		27FF18D05E8B8E663DEC40BE /* Pods-mapto.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto.debug.xcconfig"; path = "Target Support Files/Pods-mapto/Pods-mapto.debug.xcconfig"; sourceTree = "<group>"; };
		2EB1E37BF8364A74AEBC8AC1 /* EBGaramond-Bold.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Bold.woff2"; path = "../assets/fonts/EBGaramond-Bold.woff2"; sourceTree = "<group>"; };
		39734626D79149659EC802E6 /* EBGaramond-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Bold.ttf"; path = "../assets/fonts/EBGaramond-Bold.ttf"; sourceTree = "<group>"; };
		3C50313B00C443EC9E0D580F /* GeistVariableVF.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = GeistVariableVF.woff2; path = ../assets/fonts/GeistVariableVF.woff2; sourceTree = "<group>"; };
		3F475FDD13EEAE342B7DDE17 /* Pods_mapto_maptoLocal.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_mapto_maptoLocal.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4B2BA4029CA026D2BD9B5FE0 /* Pods-mapto-maptoDev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto-maptoDev.release.xcconfig"; path = "Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev.release.xcconfig"; sourceTree = "<group>"; };
		4C9AFAF7A0814B9DA13D14C8 /* EBGaramond-MediumItalic.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-MediumItalic.woff2"; path = "../assets/fonts/EBGaramond-MediumItalic.woff2"; sourceTree = "<group>"; };
		52EC4F0654B5FB18D83BF154 /* Pods_mapto_maptoDev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_mapto_maptoDev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5904FE791DE2384DABE6CA91 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = mapto/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		72E6A8719017484C92C77245 /* Geist-UltraBlack.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-UltraBlack.woff2"; path = "../assets/fonts/Geist-UltraBlack.woff2"; sourceTree = "<group>"; };
		75BD4D9C6FE140EC8C3C03C9 /* EBGaramond-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-ExtraBold.ttf"; path = "../assets/fonts/EBGaramond-ExtraBold.ttf"; sourceTree = "<group>"; };
		7AF5662DD3F44CA8A93206B1 /* Geist-UltraLight.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-UltraLight.woff2"; path = "../assets/fonts/Geist-UltraLight.woff2"; sourceTree = "<group>"; };
		81438EC70B5B47BC852CC9D6 /* Geist-Regular.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Regular.woff2"; path = "../assets/fonts/Geist-Regular.woff2"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = mapto/LaunchScreen.storyboard; sourceTree = "<group>"; };
		82DF057C8947D9316C6971CC /* Pods-mapto-maptoLocal.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto-maptoLocal.debug.xcconfig"; path = "Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal.debug.xcconfig"; sourceTree = "<group>"; };
		8A3F1E9E3BA24BC9836B0C7E /* EBGaramond-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-SemiBoldItalic.ttf"; path = "../assets/fonts/EBGaramond-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		8DA1C5CC66D1F9B6B607469E /* Pods-mapto-maptoDev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto-maptoDev.debug.xcconfig"; path = "Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev.debug.xcconfig"; sourceTree = "<group>"; };
		9496797B55E741CFB998ACDA /* Geist-SemiBold.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-SemiBold.woff2"; path = "../assets/fonts/Geist-SemiBold.woff2"; sourceTree = "<group>"; };
		963BAAB20BFA4E44881348FA /* Geist-Thin.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Thin.woff2"; path = "../assets/fonts/Geist-Thin.woff2"; sourceTree = "<group>"; };
		97DB8C209B934DA9B66EF7D7 /* EBGaramond-Italic.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Italic.woff2"; path = "../assets/fonts/EBGaramond-Italic.woff2"; sourceTree = "<group>"; };
		9FB3114A6BE64286BC23B79E /* Geist-Bold.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Bold.woff2"; path = "../assets/fonts/Geist-Bold.woff2"; sourceTree = "<group>"; };
		C01B12DE28A04480A96C01AE /* EBGaramond-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-MediumItalic.ttf"; path = "../assets/fonts/EBGaramond-MediumItalic.ttf"; sourceTree = "<group>"; };
		C1AB828F43904D71A3BD1F63 /* EBGaramond-Medium.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Medium.woff2"; path = "../assets/fonts/EBGaramond-Medium.woff2"; sourceTree = "<group>"; };
		C744A32D49E8430EB51280CE /* Geist-Light.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Light.woff2"; path = "../assets/fonts/Geist-Light.woff2"; sourceTree = "<group>"; };
		C7A6CD0CA3591C9FEC4E2FBE /* Pods-mapto-maptoLocal.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto-maptoLocal.release.xcconfig"; path = "Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal.release.xcconfig"; sourceTree = "<group>"; };
		CC5A8C955E984657B74096D8 /* EBGaramond-SemiBold.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-SemiBold.woff2"; path = "../assets/fonts/EBGaramond-SemiBold.woff2"; sourceTree = "<group>"; };
		D496D97637074CE19AD079B5 /* EBGaramond-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Regular.ttf"; path = "../assets/fonts/EBGaramond-Regular.ttf"; sourceTree = "<group>"; };
		DB6CD4750414A82BD86B971E /* Pods-mapto.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-mapto.release.xcconfig"; path = "Target Support Files/Pods-mapto/Pods-mapto.release.xcconfig"; sourceTree = "<group>"; };
		DB817A78011940A3AAF84002 /* Geist-Black.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Black.woff2"; path = "../assets/fonts/Geist-Black.woff2"; sourceTree = "<group>"; };
		DDAE4DAC0A1941F4BB4F7962 /* EBGaramond-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-Medium.ttf"; path = "../assets/fonts/EBGaramond-Medium.ttf"; sourceTree = "<group>"; };
		E57039672B9645ED887008A8 /* EBGaramond-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-ExtraBoldItalic.ttf"; path = "../assets/fonts/EBGaramond-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		EC70C87868DF449CA7447E20 /* EBGaramond-ExtraBoldItalic.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-ExtraBoldItalic.woff2"; path = "../assets/fonts/EBGaramond-ExtraBoldItalic.woff2"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F088DA442D6A420383FDB98E /* EBGaramond-BoldItalic.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-BoldItalic.woff2"; path = "../assets/fonts/EBGaramond-BoldItalic.woff2"; sourceTree = "<group>"; };
		F730FB5D484045988330101D /* Geist-Medium.woff2 */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Geist-Medium.woff2"; path = "../assets/fonts/Geist-Medium.woff2"; sourceTree = "<group>"; };
		F7C4DF9E800F41FA8E9B1993 /* EBGaramond-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-SemiBold.ttf"; path = "../assets/fonts/EBGaramond-SemiBold.ttf"; sourceTree = "<group>"; };
		FA32482D0EE549C98FE203B0 /* EBGaramond-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "EBGaramond-BoldItalic.ttf"; path = "../assets/fonts/EBGaramond-BoldItalic.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA6682D42B07900DA198B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA6692D42B07900DA198B /* GooglePlaces in Frameworks */,
				5579B1397F5B1419F9F0341A /* Pods_mapto_maptoDev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA6D42D42B1AA00DA198B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA6D52D42B1AA00DA198B /* GooglePlaces in Frameworks */,
				32A0FC88A8BAD37DA8F60DF6 /* Pods_mapto_maptoLocal.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0C5C29BD2DD4ACA000AE4F36 /* AVFoundation.framework in Frameworks */,
				0C9171432B9990C9007E62F2 /* GooglePlaces in Frameworks */,
				643694362258DA871EED5E65 /* (null) in Frameworks */,
				89B60CAB6AB195EDC8CE5EA1 /* Pods_mapto.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* maptoTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* maptoTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = maptoTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0C6CA7042D42B9B600DA198B /* GoogleService */ = {
			isa = PBXGroup;
			children = (
				0C6CA7072D42B9D600DA198B /* development */,
				0C6CA7062D42B9CF00DA198B /* local */,
				0C6CA7052D42B9C200DA198B /* production */,
			);
			path = GoogleService;
			sourceTree = "<group>";
		};
		0C6CA7052D42B9C200DA198B /* production */ = {
			isa = PBXGroup;
			children = (
				0C1E87AA2DA3A45D009683FD /* GoogleService-Info.plist */,
			);
			path = production;
			sourceTree = "<group>";
		};
		0C6CA7062D42B9CF00DA198B /* local */ = {
			isa = PBXGroup;
			children = (
				0C5898162D4354F60058F796 /* GoogleService-Info-Local.plist */,
			);
			path = local;
			sourceTree = "<group>";
		};
		0C6CA7072D42B9D600DA198B /* development */ = {
			isa = PBXGroup;
			children = (
				0C5898132D4354EA0058F796 /* GoogleService-Info-Dev.plist */,
			);
			path = development;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* mapto */ = {
			isa = PBXGroup;
			children = (
				180807372D4271A5000F3C2B /* mapto.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				5904FE791DE2384DABE6CA91 /* PrivacyInfo.xcprivacy */,
			);
			name = mapto;
			sourceTree = "<group>";
		};
		18A2C3A52D4A5B460049AE96 /* AppIcon.appiconset */ = {
			isa = PBXGroup;
			children = (
				18A2C3822D4A5B460049AE96 /* 16.png */,
				18A2C3832D4A5B460049AE96 /* 20.png */,
				18A2C3842D4A5B460049AE96 /* 29.png */,
				18A2C3852D4A5B460049AE96 /* 32.png */,
				18A2C3862D4A5B460049AE96 /* 40.png */,
				18A2C3872D4A5B460049AE96 /* 48.png */,
				18A2C3882D4A5B460049AE96 /* 50.png */,
				18A2C3892D4A5B460049AE96 /* 55.png */,
				18A2C38A2D4A5B460049AE96 /* 57.png */,
				18A2C38B2D4A5B460049AE96 /* 58.png */,
				18A2C38C2D4A5B460049AE96 /* 60.png */,
				18A2C38D2D4A5B460049AE96 /* 64.png */,
				18A2C38E2D4A5B460049AE96 /* 66.png */,
				18A2C38F2D4A5B460049AE96 /* 72.png */,
				18A2C3902D4A5B460049AE96 /* 76.png */,
				18A2C3912D4A5B460049AE96 /* 80.png */,
				18A2C3922D4A5B460049AE96 /* 87.png */,
				18A2C3932D4A5B460049AE96 /* 88.png */,
				18A2C3942D4A5B460049AE96 /* 92.png */,
				18A2C3952D4A5B460049AE96 /* 100.png */,
				18A2C3962D4A5B460049AE96 /* 102.png */,
				18A2C3972D4A5B460049AE96 /* 114.png */,
				18A2C3982D4A5B460049AE96 /* 120.png */,
				18A2C3992D4A5B460049AE96 /* 128.png */,
				18A2C39A2D4A5B460049AE96 /* 144.png */,
				18A2C39B2D4A5B460049AE96 /* 152.png */,
				18A2C39C2D4A5B460049AE96 /* 167.png */,
				18A2C39D2D4A5B460049AE96 /* 172.png */,
				18A2C39E2D4A5B460049AE96 /* 180.png */,
				18A2C39F2D4A5B460049AE96 /* 196.png */,
				18A2C3A02D4A5B460049AE96 /* 216.png */,
				18A2C3A12D4A5B460049AE96 /* 256.png */,
				18A2C3A22D4A5B460049AE96 /* 512.png */,
				18A2C3A32D4A5B460049AE96 /* 1024.png */,
				18A2C3A42D4A5B460049AE96 /* Contents.json */,
			);
			path = AppIcon.appiconset;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0C5C29BC2DD4ACA000AE4F36 /* AVFoundation.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				0CF7000B9ACADA940E64F07A /* Pods_mapto.framework */,
				52EC4F0654B5FB18D83BF154 /* Pods_mapto_maptoDev.framework */,
				3F475FDD13EEAE342B7DDE17 /* Pods_mapto_maptoLocal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				0C2D22792DDDE6B600D5F6D3 /* rideaborted.mp3 */,
				0C2D227A2DDDE6B600D5F6D3 /* rideaccepted.mp3 */,
				0C2D227B2DDDE6B600D5F6D3 /* ridecompleted.mp3 */,
				0C2D227C2DDDE6B600D5F6D3 /* rideverified.mp3 */,
				0C2D227D2DDDE6B600D5F6D3 /* safetycheck.mp3 */,
				0C5C29BF2DD4BDA700AE4F36 /* driverarrived.mp3 */,
				0C5C29C02DD4BDA700AE4F36 /* drivercanceled.mp3 */,
				0C5C29C12DD4BDA700AE4F36 /* nodrivers.mp3 */,
				0C6CA7042D42B9B600DA198B /* GoogleService */,
				13B07FAE1A68108700A75B9A /* mapto */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* maptoTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				9C17FAEFD24443688478A055 /* Resources */,
				0C6CA6962D42B07900DA198B /* maptoDev-Info.plist */,
				0C6CA7022D42B1AA00DA198B /* maptoLocal-Info.plist */,
				18A2C3A52D4A5B460049AE96 /* AppIcon.appiconset */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* mapto.app */,
				00E356EE1AD99517003FC87E /* maptoTests.xctest */,
				0C6CA6952D42B07900DA198B /* maptoDev.app */,
				0C6CA7012D42B1AA00DA198B /* maptoLocal.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9C17FAEFD24443688478A055 /* Resources */ = {
			isa = PBXGroup;
			children = (
				39734626D79149659EC802E6 /* EBGaramond-Bold.ttf */,
				FA32482D0EE549C98FE203B0 /* EBGaramond-BoldItalic.ttf */,
				75BD4D9C6FE140EC8C3C03C9 /* EBGaramond-ExtraBold.ttf */,
				E57039672B9645ED887008A8 /* EBGaramond-ExtraBoldItalic.ttf */,
				08CA6057124D4423B19645BB /* EBGaramond-Italic.ttf */,
				DDAE4DAC0A1941F4BB4F7962 /* EBGaramond-Medium.ttf */,
				C01B12DE28A04480A96C01AE /* EBGaramond-MediumItalic.ttf */,
				D496D97637074CE19AD079B5 /* EBGaramond-Regular.ttf */,
				F7C4DF9E800F41FA8E9B1993 /* EBGaramond-SemiBold.ttf */,
				8A3F1E9E3BA24BC9836B0C7E /* EBGaramond-SemiBoldItalic.ttf */,
				2EB1E37BF8364A74AEBC8AC1 /* EBGaramond-Bold.woff2 */,
				F088DA442D6A420383FDB98E /* EBGaramond-BoldItalic.woff2 */,
				0A743A34FC3648978DD7CEAE /* EBGaramond-ExtraBold.woff2 */,
				EC70C87868DF449CA7447E20 /* EBGaramond-ExtraBoldItalic.woff2 */,
				97DB8C209B934DA9B66EF7D7 /* EBGaramond-Italic.woff2 */,
				C1AB828F43904D71A3BD1F63 /* EBGaramond-Medium.woff2 */,
				4C9AFAF7A0814B9DA13D14C8 /* EBGaramond-MediumItalic.woff2 */,
				0098168BAB0A4B349F5BEED6 /* EBGaramond-Regular.woff2 */,
				CC5A8C955E984657B74096D8 /* EBGaramond-SemiBold.woff2 */,
				2680830407A74246AD42F867 /* EBGaramond-SemiBoldItalic.woff2 */,
				DB817A78011940A3AAF84002 /* Geist-Black.woff2 */,
				9FB3114A6BE64286BC23B79E /* Geist-Bold.woff2 */,
				C744A32D49E8430EB51280CE /* Geist-Light.woff2 */,
				F730FB5D484045988330101D /* Geist-Medium.woff2 */,
				81438EC70B5B47BC852CC9D6 /* Geist-Regular.woff2 */,
				9496797B55E741CFB998ACDA /* Geist-SemiBold.woff2 */,
				963BAAB20BFA4E44881348FA /* Geist-Thin.woff2 */,
				72E6A8719017484C92C77245 /* Geist-UltraBlack.woff2 */,
				7AF5662DD3F44CA8A93206B1 /* Geist-UltraLight.woff2 */,
				3C50313B00C443EC9E0D580F /* GeistVariableVF.woff2 */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				27FF18D05E8B8E663DEC40BE /* Pods-mapto.debug.xcconfig */,
				DB6CD4750414A82BD86B971E /* Pods-mapto.release.xcconfig */,
				8DA1C5CC66D1F9B6B607469E /* Pods-mapto-maptoDev.debug.xcconfig */,
				4B2BA4029CA026D2BD9B5FE0 /* Pods-mapto-maptoDev.release.xcconfig */,
				82DF057C8947D9316C6971CC /* Pods-mapto-maptoLocal.debug.xcconfig */,
				C7A6CD0CA3591C9FEC4E2FBE /* Pods-mapto-maptoLocal.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* maptoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "maptoTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = maptoTests;
			productName = maptoTests;
			productReference = 00E356EE1AD99517003FC87E /* maptoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		0C6CA6612D42B07900DA198B /* maptoDev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0C6CA6922D42B07900DA198B /* Build configuration list for PBXNativeTarget "maptoDev" */;
			buildPhases = (
				CFE4B35DA40D5F8CA79C2522 /* [CP] Check Pods Manifest.lock */,
				0C6CA6652D42B07900DA198B /* Sources */,
				0C6CA6682D42B07900DA198B /* Frameworks */,
				0C6CA66B2D42B07900DA198B /* Resources */,
				0C6CA68E2D42B07900DA198B /* Bundle React Native code and images */,
				051026920CEFB17C3072F21F /* [CP] Embed Pods Frameworks */,
				31AB09DA74ED33ABC67370B2 /* [CP] Copy Pods Resources */,
				0C0BFAC32D4365E500D51F3E /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = maptoDev;
			packageProductDependencies = (
				0C6CA6622D42B07900DA198B /* GooglePlaces */,
			);
			productName = mapto;
			productReference = 0C6CA6952D42B07900DA198B /* maptoDev.app */;
			productType = "com.apple.product-type.application";
		};
		0C6CA6CD2D42B1AA00DA198B /* maptoLocal */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0C6CA6FE2D42B1AA00DA198B /* Build configuration list for PBXNativeTarget "maptoLocal" */;
			buildPhases = (
				10F90033E64A969EA50A9ADC /* [CP] Check Pods Manifest.lock */,
				0C6CA6D12D42B1AA00DA198B /* Sources */,
				0C6CA6D42D42B1AA00DA198B /* Frameworks */,
				0C6CA6D72D42B1AA00DA198B /* Resources */,
				0C6CA6FA2D42B1AA00DA198B /* Bundle React Native code and images */,
				DF38ED82B848A9751F436849 /* [CP] Embed Pods Frameworks */,
				362D9E406AF77D066E3BA46D /* [CP] Copy Pods Resources */,
				0C0BFAC42D4365F100D51F3E /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = maptoLocal;
			packageProductDependencies = (
				0C6CA6CE2D42B1AA00DA198B /* GooglePlaces */,
			);
			productName = mapto;
			productReference = 0C6CA7012D42B1AA00DA198B /* maptoLocal.app */;
			productType = "com.apple.product-type.application";
		};
		13B07F861A680F5B00A75B9A /* mapto */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "mapto" */;
			buildPhases = (
				001AD6F6FE5F4FDA5C30238C /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				0C6CA7142D42BDBE00DA198B /* Run Script */,
				9078EE9233441F9192907CB4 /* [CP] Embed Pods Frameworks */,
				9595AD919DFB3271AB7F9FFA /* [CP] Copy Pods Resources */,
				D78E429561A11B690273192E /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = mapto;
			packageProductDependencies = (
				0C9171422B9990C9007E62F2 /* GooglePlaces */,
			);
			productName = mapto;
			productReference = 13B07F961A680F5B00A75B9A /* mapto.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "mapto" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			packageReferences = (
				0C9171412B9990C9007E62F2 /* XCRemoteSwiftPackageReference "ios-places-sdk" */,
			);
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* mapto */,
				00E356ED1AD99517003FC87E /* maptoTests */,
				0C6CA6612D42B07900DA198B /* maptoDev */,
				0C6CA6CD2D42B1AA00DA198B /* maptoLocal */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				18A2C40F2D4A5B460049AE96 /* 180.png in Resources */,
				18A2C4102D4A5B460049AE96 /* 40.png in Resources */,
				18A2C4112D4A5B460049AE96 /* 512.png in Resources */,
				18A2C4122D4A5B460049AE96 /* 152.png in Resources */,
				18A2C4132D4A5B460049AE96 /* 48.png in Resources */,
				18A2C4142D4A5B460049AE96 /* 100.png in Resources */,
				18A2C4152D4A5B460049AE96 /* 88.png in Resources */,
				18A2C4162D4A5B460049AE96 /* 76.png in Resources */,
				18A2C4172D4A5B460049AE96 /* 1024.png in Resources */,
				18A2C4182D4A5B460049AE96 /* 20.png in Resources */,
				18A2C4192D4A5B460049AE96 /* 66.png in Resources */,
				18A2C41A2D4A5B460049AE96 /* 216.png in Resources */,
				18A2C41B2D4A5B460049AE96 /* 58.png in Resources */,
				18A2C41C2D4A5B460049AE96 /* 57.png in Resources */,
				18A2C41D2D4A5B460049AE96 /* Contents.json in Resources */,
				18A2C41E2D4A5B460049AE96 /* 92.png in Resources */,
				18A2C41F2D4A5B460049AE96 /* 80.png in Resources */,
				18A2C4202D4A5B460049AE96 /* 144.png in Resources */,
				18A2C4212D4A5B460049AE96 /* 172.png in Resources */,
				18A2C4222D4A5B460049AE96 /* 114.png in Resources */,
				18A2C4232D4A5B460049AE96 /* 60.png in Resources */,
				18A2C4242D4A5B460049AE96 /* 64.png in Resources */,
				18A2C4252D4A5B460049AE96 /* 196.png in Resources */,
				18A2C4262D4A5B460049AE96 /* 120.png in Resources */,
				18A2C4272D4A5B460049AE96 /* 167.png in Resources */,
				18A2C4282D4A5B460049AE96 /* 29.png in Resources */,
				18A2C4292D4A5B460049AE96 /* 72.png in Resources */,
				18A2C42A2D4A5B460049AE96 /* 50.png in Resources */,
				18A2C42B2D4A5B460049AE96 /* 256.png in Resources */,
				18A2C42C2D4A5B460049AE96 /* 128.png in Resources */,
				18A2C42D2D4A5B460049AE96 /* 32.png in Resources */,
				18A2C42E2D4A5B460049AE96 /* 87.png in Resources */,
				18A2C42F2D4A5B460049AE96 /* 102.png in Resources */,
				18A2C4302D4A5B460049AE96 /* 55.png in Resources */,
				18A2C4312D4A5B460049AE96 /* 16.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA66B2D42B07900DA198B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA66C2D42B07900DA198B /* LaunchScreen.storyboard in Resources */,
				18A2C3C92D4A5B460049AE96 /* 180.png in Resources */,
				18A2C3CA2D4A5B460049AE96 /* 40.png in Resources */,
				18A2C3CB2D4A5B460049AE96 /* 512.png in Resources */,
				18A2C3CC2D4A5B460049AE96 /* 152.png in Resources */,
				18A2C3CD2D4A5B460049AE96 /* 48.png in Resources */,
				18A2C3CE2D4A5B460049AE96 /* 100.png in Resources */,
				18A2C3CF2D4A5B460049AE96 /* 88.png in Resources */,
				18A2C3D02D4A5B460049AE96 /* 76.png in Resources */,
				18A2C3D12D4A5B460049AE96 /* 1024.png in Resources */,
				18A2C3D22D4A5B460049AE96 /* 20.png in Resources */,
				18A2C3D32D4A5B460049AE96 /* 66.png in Resources */,
				18A2C3D42D4A5B460049AE96 /* 216.png in Resources */,
				18A2C3D52D4A5B460049AE96 /* 58.png in Resources */,
				18A2C3D62D4A5B460049AE96 /* 57.png in Resources */,
				18A2C3D82D4A5B460049AE96 /* 92.png in Resources */,
				18A2C3D92D4A5B460049AE96 /* 80.png in Resources */,
				18A2C3DA2D4A5B460049AE96 /* 144.png in Resources */,
				18A2C3DB2D4A5B460049AE96 /* 172.png in Resources */,
				18A2C3DC2D4A5B460049AE96 /* 114.png in Resources */,
				18A2C3DD2D4A5B460049AE96 /* 60.png in Resources */,
				0C1E87AB2DA3A45D009683FD /* GoogleService-Info.plist in Resources */,
				18A2C3DE2D4A5B460049AE96 /* 64.png in Resources */,
				18A2C3DF2D4A5B460049AE96 /* 196.png in Resources */,
				18A2C3E02D4A5B460049AE96 /* 120.png in Resources */,
				18A2C3E12D4A5B460049AE96 /* 167.png in Resources */,
				18A2C3E22D4A5B460049AE96 /* 29.png in Resources */,
				18A2C3E32D4A5B460049AE96 /* 72.png in Resources */,
				18A2C3E42D4A5B460049AE96 /* 50.png in Resources */,
				18A2C3E52D4A5B460049AE96 /* 256.png in Resources */,
				18A2C3E62D4A5B460049AE96 /* 128.png in Resources */,
				18A2C3E72D4A5B460049AE96 /* 32.png in Resources */,
				0C2D22882DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */,
				0C2D22892DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */,
				0C2D228A2DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */,
				0C2D228B2DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */,
				0C2D228C2DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */,
				18A2C3E82D4A5B460049AE96 /* 87.png in Resources */,
				18A2C3E92D4A5B460049AE96 /* 102.png in Resources */,
				18A2C3EA2D4A5B460049AE96 /* 55.png in Resources */,
				18A2C3EB2D4A5B460049AE96 /* 16.png in Resources */,
				18A2C3D72D4A5B460049AE96 /* Contents.json in Resources */,
				0C5898152D4354EA0058F796 /* GoogleService-Info-Dev.plist in Resources */,
				0C6CA66D2D42B07900DA198B /* Images.xcassets in Resources */,
				0C6CA66E2D42B07900DA198B /* EBGaramond-Bold.ttf in Resources */,
				0C6CA66F2D42B07900DA198B /* EBGaramond-BoldItalic.ttf in Resources */,
				0C6CA6702D42B07900DA198B /* EBGaramond-ExtraBold.ttf in Resources */,
				0C6CA6712D42B07900DA198B /* EBGaramond-ExtraBoldItalic.ttf in Resources */,
				0C6CA6722D42B07900DA198B /* EBGaramond-Italic.ttf in Resources */,
				0C6CA6732D42B07900DA198B /* EBGaramond-Medium.ttf in Resources */,
				0C5C29C82DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */,
				0C5C29C92DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */,
				0C5C29CA2DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */,
				0C6CA6742D42B07900DA198B /* EBGaramond-MediumItalic.ttf in Resources */,
				0C6CA6752D42B07900DA198B /* EBGaramond-Regular.ttf in Resources */,
				0C6CA6762D42B07900DA198B /* EBGaramond-SemiBold.ttf in Resources */,
				0C6CA6772D42B07900DA198B /* EBGaramond-SemiBoldItalic.ttf in Resources */,
				0C6CA6782D42B07900DA198B /* EBGaramond-Bold.woff2 in Resources */,
				0C6CA6792D42B07900DA198B /* EBGaramond-BoldItalic.woff2 in Resources */,
				0C6CA67A2D42B07900DA198B /* EBGaramond-ExtraBold.woff2 in Resources */,
				0C6CA67B2D42B07900DA198B /* EBGaramond-ExtraBoldItalic.woff2 in Resources */,
				0C6CA67C2D42B07900DA198B /* EBGaramond-Italic.woff2 in Resources */,
				0C6CA67D2D42B07900DA198B /* EBGaramond-Medium.woff2 in Resources */,
				0C6CA67E2D42B07900DA198B /* EBGaramond-MediumItalic.woff2 in Resources */,
				0C6CA67F2D42B07900DA198B /* EBGaramond-Regular.woff2 in Resources */,
				0C6CA6802D42B07900DA198B /* EBGaramond-SemiBold.woff2 in Resources */,
				0C6CA6812D42B07900DA198B /* EBGaramond-SemiBoldItalic.woff2 in Resources */,
				0C6CA6822D42B07900DA198B /* Geist-Black.woff2 in Resources */,
				0C6CA6832D42B07900DA198B /* Geist-Bold.woff2 in Resources */,
				0C6CA6842D42B07900DA198B /* Geist-Light.woff2 in Resources */,
				0C6CA6852D42B07900DA198B /* Geist-Medium.woff2 in Resources */,
				0C6CA6862D42B07900DA198B /* Geist-Regular.woff2 in Resources */,
				0C6CA6872D42B07900DA198B /* Geist-SemiBold.woff2 in Resources */,
				0C6CA6882D42B07900DA198B /* Geist-Thin.woff2 in Resources */,
				0C6CA68A2D42B07900DA198B /* Geist-UltraBlack.woff2 in Resources */,
				0C6CA68B2D42B07900DA198B /* Geist-UltraLight.woff2 in Resources */,
				0C6CA68C2D42B07900DA198B /* GeistVariableVF.woff2 in Resources */,
				0C6CA68D2D42B07900DA198B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA6D72D42B1AA00DA198B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA6D82D42B1AA00DA198B /* LaunchScreen.storyboard in Resources */,
				0C6CA6DA2D42B1AA00DA198B /* EBGaramond-Bold.ttf in Resources */,
				0C6CA6DB2D42B1AA00DA198B /* EBGaramond-BoldItalic.ttf in Resources */,
				0C6CA6DC2D42B1AA00DA198B /* EBGaramond-ExtraBold.ttf in Resources */,
				0C6CA6DD2D42B1AA00DA198B /* EBGaramond-ExtraBoldItalic.ttf in Resources */,
				0C6CA6DE2D42B1AA00DA198B /* EBGaramond-Italic.ttf in Resources */,
				0C6CA6DF2D42B1AA00DA198B /* EBGaramond-Medium.ttf in Resources */,
				0C6CA6E02D42B1AA00DA198B /* EBGaramond-MediumItalic.ttf in Resources */,
				0C6CA6E12D42B1AA00DA198B /* EBGaramond-Regular.ttf in Resources */,
				0C6CA6E22D42B1AA00DA198B /* EBGaramond-SemiBold.ttf in Resources */,
				18A2C3A62D4A5B460049AE96 /* 180.png in Resources */,
				18A2C3A72D4A5B460049AE96 /* 40.png in Resources */,
				18A2C3A82D4A5B460049AE96 /* 512.png in Resources */,
				18A2C3A92D4A5B460049AE96 /* 152.png in Resources */,
				18A2C3AA2D4A5B460049AE96 /* 48.png in Resources */,
				18A2C3AB2D4A5B460049AE96 /* 100.png in Resources */,
				18A2C3AC2D4A5B460049AE96 /* 88.png in Resources */,
				18A2C3AD2D4A5B460049AE96 /* 76.png in Resources */,
				18A2C3AE2D4A5B460049AE96 /* 1024.png in Resources */,
				18A2C3AF2D4A5B460049AE96 /* 20.png in Resources */,
				18A2C3B02D4A5B460049AE96 /* 66.png in Resources */,
				18A2C3B12D4A5B460049AE96 /* 216.png in Resources */,
				18A2C3B22D4A5B460049AE96 /* 58.png in Resources */,
				18A2C3B32D4A5B460049AE96 /* 57.png in Resources */,
				18A2C3B42D4A5B460049AE96 /* Contents.json in Resources */,
				18A2C3B52D4A5B460049AE96 /* 92.png in Resources */,
				18A2C3B62D4A5B460049AE96 /* 80.png in Resources */,
				18A2C3B72D4A5B460049AE96 /* 144.png in Resources */,
				18A2C3B82D4A5B460049AE96 /* 172.png in Resources */,
				18A2C3B92D4A5B460049AE96 /* 114.png in Resources */,
				18A2C3BA2D4A5B460049AE96 /* 60.png in Resources */,
				18A2C3BB2D4A5B460049AE96 /* 64.png in Resources */,
				18A2C3BC2D4A5B460049AE96 /* 196.png in Resources */,
				18A2C3BD2D4A5B460049AE96 /* 120.png in Resources */,
				18A2C3BE2D4A5B460049AE96 /* 167.png in Resources */,
				18A2C3BF2D4A5B460049AE96 /* 29.png in Resources */,
				18A2C3C02D4A5B460049AE96 /* 72.png in Resources */,
				18A2C3C12D4A5B460049AE96 /* 50.png in Resources */,
				18A2C3C22D4A5B460049AE96 /* 256.png in Resources */,
				18A2C3C32D4A5B460049AE96 /* 128.png in Resources */,
				18A2C3C42D4A5B460049AE96 /* 32.png in Resources */,
				18A2C3C52D4A5B460049AE96 /* 87.png in Resources */,
				18A2C3C62D4A5B460049AE96 /* 102.png in Resources */,
				18A2C3C72D4A5B460049AE96 /* 55.png in Resources */,
				18A2C3C82D4A5B460049AE96 /* 16.png in Resources */,
				0C6CA6E32D42B1AA00DA198B /* EBGaramond-SemiBoldItalic.ttf in Resources */,
				0C6CA6E42D42B1AA00DA198B /* EBGaramond-Bold.woff2 in Resources */,
				0C6CA6E52D42B1AA00DA198B /* EBGaramond-BoldItalic.woff2 in Resources */,
				0C6CA6E62D42B1AA00DA198B /* EBGaramond-ExtraBold.woff2 in Resources */,
				0C6CA6E72D42B1AA00DA198B /* EBGaramond-ExtraBoldItalic.woff2 in Resources */,
				0C6CA6E82D42B1AA00DA198B /* EBGaramond-Italic.woff2 in Resources */,
				0C6CA6E92D42B1AA00DA198B /* EBGaramond-Medium.woff2 in Resources */,
				0C6CA6EA2D42B1AA00DA198B /* EBGaramond-MediumItalic.woff2 in Resources */,
				0C6CA6EB2D42B1AA00DA198B /* EBGaramond-Regular.woff2 in Resources */,
				0C6CA6EC2D42B1AA00DA198B /* EBGaramond-SemiBold.woff2 in Resources */,
				0C5C29C52DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */,
				0C5C29C62DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */,
				0C5C29C72DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */,
				0C5898182D4354F60058F796 /* GoogleService-Info-Local.plist in Resources */,
				0C6CA6ED2D42B1AA00DA198B /* EBGaramond-SemiBoldItalic.woff2 in Resources */,
				0C2D227E2DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */,
				0C2D227F2DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */,
				0C2D22802DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */,
				0C2D22812DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */,
				0C2D22822DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */,
				0C6CA6EE2D42B1AA00DA198B /* Geist-Black.woff2 in Resources */,
				0C6CA6EF2D42B1AA00DA198B /* Geist-Bold.woff2 in Resources */,
				0C6CA6F02D42B1AA00DA198B /* Geist-Light.woff2 in Resources */,
				0C6CA6F12D42B1AA00DA198B /* Geist-Medium.woff2 in Resources */,
				0C6CA6F22D42B1AA00DA198B /* Geist-Regular.woff2 in Resources */,
				0C6CA6F32D42B1AA00DA198B /* Geist-SemiBold.woff2 in Resources */,
				0C6CA6F42D42B1AA00DA198B /* Geist-Thin.woff2 in Resources */,
				0C6CA6F62D42B1AA00DA198B /* Geist-UltraBlack.woff2 in Resources */,
				0C6CA6F72D42B1AA00DA198B /* Geist-UltraLight.woff2 in Resources */,
				0C6CA6F82D42B1AA00DA198B /* GeistVariableVF.woff2 in Resources */,
				0C6CA6F92D42B1AA00DA198B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				48F3139124B246ADAFED1EBA /* EBGaramond-Bold.ttf in Resources */,
				FE4EA7483A044453BB6B64E1 /* EBGaramond-BoldItalic.ttf in Resources */,
				9BCB77BF7F0341C7A99F8712 /* EBGaramond-ExtraBold.ttf in Resources */,
				BA9F9204F669458B831C944F /* EBGaramond-ExtraBoldItalic.ttf in Resources */,
				45B56D8553934324922E8104 /* EBGaramond-Italic.ttf in Resources */,
				3962C592464E4066ACDD6045 /* EBGaramond-Medium.ttf in Resources */,
				6118BF666B674A51A196F6F8 /* EBGaramond-MediumItalic.ttf in Resources */,
				B34B6E2613D045F5934C5E93 /* EBGaramond-Regular.ttf in Resources */,
				18A2C3EC2D4A5B460049AE96 /* 180.png in Resources */,
				18A2C3ED2D4A5B460049AE96 /* 40.png in Resources */,
				18A2C3EE2D4A5B460049AE96 /* 512.png in Resources */,
				18A2C3EF2D4A5B460049AE96 /* 152.png in Resources */,
				18A2C3F02D4A5B460049AE96 /* 48.png in Resources */,
				18A2C3F12D4A5B460049AE96 /* 100.png in Resources */,
				18A2C3F22D4A5B460049AE96 /* 88.png in Resources */,
				18A2C3F32D4A5B460049AE96 /* 76.png in Resources */,
				18A2C3F42D4A5B460049AE96 /* 1024.png in Resources */,
				18A2C3F52D4A5B460049AE96 /* 20.png in Resources */,
				18A2C3F62D4A5B460049AE96 /* 66.png in Resources */,
				18A2C3F72D4A5B460049AE96 /* 216.png in Resources */,
				18A2C3F82D4A5B460049AE96 /* 58.png in Resources */,
				18A2C3F92D4A5B460049AE96 /* 57.png in Resources */,
				18A2C3FA2D4A5B460049AE96 /* Contents.json in Resources */,
				18A2C3FB2D4A5B460049AE96 /* 92.png in Resources */,
				18A2C3FC2D4A5B460049AE96 /* 80.png in Resources */,
				18A2C3FD2D4A5B460049AE96 /* 144.png in Resources */,
				18A2C3FE2D4A5B460049AE96 /* 172.png in Resources */,
				18A2C3FF2D4A5B460049AE96 /* 114.png in Resources */,
				18A2C4002D4A5B460049AE96 /* 60.png in Resources */,
				18A2C4012D4A5B460049AE96 /* 64.png in Resources */,
				18A2C4022D4A5B460049AE96 /* 196.png in Resources */,
				18A2C4032D4A5B460049AE96 /* 120.png in Resources */,
				18A2C4042D4A5B460049AE96 /* 167.png in Resources */,
				18A2C4052D4A5B460049AE96 /* 29.png in Resources */,
				18A2C4062D4A5B460049AE96 /* 72.png in Resources */,
				18A2C4072D4A5B460049AE96 /* 50.png in Resources */,
				18A2C4082D4A5B460049AE96 /* 256.png in Resources */,
				18A2C4092D4A5B460049AE96 /* 128.png in Resources */,
				18A2C40A2D4A5B460049AE96 /* 32.png in Resources */,
				18A2C40B2D4A5B460049AE96 /* 87.png in Resources */,
				18A2C40C2D4A5B460049AE96 /* 102.png in Resources */,
				18A2C40D2D4A5B460049AE96 /* 55.png in Resources */,
				18A2C40E2D4A5B460049AE96 /* 16.png in Resources */,
				80FF851E89B24816BF958906 /* EBGaramond-SemiBold.ttf in Resources */,
				3E341C94CC714049923225CA /* EBGaramond-SemiBoldItalic.ttf in Resources */,
				256E76CA139F4816AAB78A42 /* EBGaramond-Bold.woff2 in Resources */,
				872B851BE0544FBA9BF06651 /* EBGaramond-BoldItalic.woff2 in Resources */,
				95F82252F25F4E438375FFB7 /* EBGaramond-ExtraBold.woff2 in Resources */,
				33F793609799445EB19FE0D5 /* EBGaramond-ExtraBoldItalic.woff2 in Resources */,
				D7A6448F346945FBAD1EF64B /* EBGaramond-Italic.woff2 in Resources */,
				1509271BA05B461F861BA7F9 /* EBGaramond-Medium.woff2 in Resources */,
				7BAF18875C6F4095B1E2AFEF /* EBGaramond-MediumItalic.woff2 in Resources */,
				5478D4A58F76459E8C4D4092 /* EBGaramond-Regular.woff2 in Resources */,
				0C5C29C22DD4BDA700AE4F36 /* drivercanceled.mp3 in Resources */,
				0C5C29C32DD4BDA700AE4F36 /* nodrivers.mp3 in Resources */,
				0C5C29C42DD4BDA700AE4F36 /* driverarrived.mp3 in Resources */,
				6BDBD4C97DF24261937734A6 /* EBGaramond-SemiBold.woff2 in Resources */,
				DAB22DC67D3A490EBA453065 /* EBGaramond-SemiBoldItalic.woff2 in Resources */,
				0C2D22832DDDE6B600D5F6D3 /* safetycheck.mp3 in Resources */,
				0C2D22842DDDE6B600D5F6D3 /* rideverified.mp3 in Resources */,
				0C2D22852DDDE6B600D5F6D3 /* ridecompleted.mp3 in Resources */,
				0C2D22862DDDE6B600D5F6D3 /* rideaborted.mp3 in Resources */,
				0C2D22872DDDE6B600D5F6D3 /* rideaccepted.mp3 in Resources */,
				0A294A741F30430DA40F41C4 /* Geist-Black.woff2 in Resources */,
				598B99BFD270460D82080CD6 /* Geist-Bold.woff2 in Resources */,
				703BEE6B536D4B70B51FBC74 /* Geist-Light.woff2 in Resources */,
				70438A5F1BC14B32953D75BD /* Geist-Medium.woff2 in Resources */,
				3B4DDE66ED5E48D0AAB46E3B /* Geist-Regular.woff2 in Resources */,
				D609BE5ED4D64B6BA011F882 /* Geist-SemiBold.woff2 in Resources */,
				58C9FEF14F89495CA7BFE433 /* Geist-Thin.woff2 in Resources */,
				8C805498991246A3BA6E305F /* Geist-UltraBlack.woff2 in Resources */,
				7237066F3980482C9CDAAD03 /* Geist-UltraLight.woff2 in Resources */,
				E63ACEA5117641F18BF48B26 /* GeistVariableVF.woff2 in Resources */,
				ECE0B6FCBF1B5D003C3C219B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		001AD6F6FE5F4FDA5C30238C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-mapto-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nexport ENTRY_FILE=index.ts\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		051026920CEFB17C3072F21F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		0C0BFAC32D4365E500D51F3E /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\nset -e\nenvironment=\"${CONFIGURATION}\"\n# Paths\nGOOGLE_SERVICE_INFO_PLIST=\"GoogleService/production/GoogleService-Info.plist\"\nGOOGLE_SERVICE_INFO_DEV_PLIST=\"GoogleService/development/GoogleService-Info-Dev.plist\"\nGOOGLE_SERVICE_INFO_LOCAL_PLIST=\"GoogleService/local/GoogleService-Info-Local.plist\"\nPLIST_DESTINATION=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\necho \"Environment: ${environment}\"\necho \"Destination Path: ${PLIST_DESTINATION}\"\n# Function to check file existence\ncheck_file() {\n    if [ ! -f \"${PROJECT_DIR}/$1\" ]; then\n        echo \"Error: $1 not found\"\n        exit 1\n    fi\n}\n# Copy with environment check - fixed to handle exact configuration names\ncase \"${environment}\" in\n    \"Release\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Debug\" | \"Debug-Development\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Debug-Local\" )\n        echo \"Using Local GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    * )\n        echo \"Error: Configuration '${environment}' not supported\"\n        exit 1\n        ;;\nesac\n";
		};
		0C0BFAC42D4365F100D51F3E /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\nset -e\n\nenvironment=\"${CONFIGURATION}\"\n\n# Paths\nGOOGLE_SERVICE_INFO_PLIST=\"GoogleService/production/GoogleService-Info.plist\"\nGOOGLE_SERVICE_INFO_DEV_PLIST=\"GoogleService/development/GoogleService-Info-Dev.plist\"\nGOOGLE_SERVICE_INFO_LOCAL_PLIST=\"GoogleService/local/GoogleService-Info-Local.plist\"\nPLIST_DESTINATION=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\n\necho \"Environment: ${environment}\"\necho \"Destination Path: ${PLIST_DESTINATION}\"\n\n# Function to check file existence\ncheck_file() {\n    if [ ! -f \"${PROJECT_DIR}/$1\" ]; then\n        echo \"Error: $1 not found\"\n        exit 1\n    fi\n}\n\n# Copy with environment check\ncase \"${environment}\" in\n    \"Release\" )\n        echo \"Using Production GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Release-Development\"|\"Debug\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Release-Local\"|\"Debug-Local\" )\n        echo \"Using Local GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Debug-Development\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    * )\n        echo \"Error: Configuration '${environment}' not supported\"\n        exit 1\n        ;;\nesac\n";
		};
		0C6CA68E2D42B07900DA198B /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nexport ENTRY_FILE=index.ts\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		0C6CA6FA2D42B1AA00DA198B /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\nexport ENTRY_FILE=index.ts\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		0C6CA7142D42BDBE00DA198B /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\nset -e\n\nenvironment=\"${CONFIGURATION}\"\n\n# Paths\nGOOGLE_SERVICE_INFO_PLIST=\"GoogleService/production/GoogleService-Info.plist\"\nGOOGLE_SERVICE_INFO_DEV_PLIST=\"GoogleService/development/GoogleService-Info-Dev.plist\"\nGOOGLE_SERVICE_INFO_LOCAL_PLIST=\"GoogleService/local/GoogleService-Info-Local.plist\"\nPLIST_DESTINATION=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\n\necho \"Environment: ${environment}\"\necho \"Destination Path: ${PLIST_DESTINATION}\"\n\n# Function to check file existence\ncheck_file() {\n    if [ ! -f \"${PROJECT_DIR}/$1\" ]; then\n        echo \"Error: $1 not found\"\n        exit 1\n    fi\n}\n\n# Copy with environment check\ncase \"${environment}\" in\n    \"Release\" )\n        echo \"Using Production GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n        \n    \"Release-Development\"|\"Debug\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Release-Local\"|\"Debug-Local\" )\n        echo \"Using Local GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_LOCAL_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    \"Debug-Development\" )\n        echo \"Using Development GoogleService-Info.plist\"\n        check_file \"${GOOGLE_SERVICE_INFO_DEV_PLIST}\"\n        cp \"${PROJECT_DIR}/${GOOGLE_SERVICE_INFO_DEV_PLIST}\" \"${PLIST_DESTINATION}\"\n        ;;\n    * )\n        echo \"Error: Configuration '${environment}' not supported\"\n        exit 1\n        ;;\nesac\n";
		};
		10F90033E64A969EA50A9ADC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-mapto-maptoLocal-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		31AB09DA74ED33ABC67370B2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoDev/Pods-mapto-maptoDev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		362D9E406AF77D066E3BA46D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9078EE9233441F9192907CB4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9595AD919DFB3271AB7F9FFA /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto/Pods-mapto-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CFE4B35DA40D5F8CA79C2522 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-mapto-maptoDev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D78E429561A11B690273192E /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		DF38ED82B848A9751F436849 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-mapto-maptoLocal/Pods-mapto-maptoLocal-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* maptoTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA6652D42B07900DA198B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA6662D42B07900DA198B /* AppDelegate.mm in Sources */,
				0C6CA6672D42B07900DA198B /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0C6CA6D12D42B1AA00DA198B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0C6CA6D22D42B1AA00DA198B /* AppDelegate.mm in Sources */,
				0C6CA6D32D42B1AA00DA198B /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* mapto */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = maptoTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.mapto.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/mapto.app/mapto";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = maptoTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.mapto.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/mapto.app/mapto";
			};
			name = Release;
		};
		0C6CA6932D42B07900DA198B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8DA1C5CC66D1F9B6B607469E /* Pods-mapto-maptoDev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "maptoDev-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto.development;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		0C6CA6942D42B07900DA198B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B2BA4029CA026D2BD9B5FE0 /* Pods-mapto-maptoDev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				INFOPLIST_FILE = "maptoDev-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto.development;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		0C6CA6FF2D42B1AA00DA198B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 82DF057C8947D9316C6971CC /* Pods-mapto-maptoLocal.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "maptoLocal-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto.development;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		0C6CA7002D42B1AA00DA198B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7A6CD0CA3591C9FEC4E2FBE /* Pods-mapto-maptoLocal.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				INFOPLIST_FILE = "maptoLocal-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto.local;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 27FF18D05E8B8E663DEC40BE /* Pods-mapto.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = mapto/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto;
				PRODUCT_NAME = mapto;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DB6CD4750414A82BD86B971E /* Pods-mapto.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = mapto/mapto.entitlements;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_TEAM = 7L9SZMBW6K;
				INFOPLIST_FILE = mapto/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mapto;
				PRODUCT_NAME = mapto;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "maptoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0C6CA6922D42B07900DA198B /* Build configuration list for PBXNativeTarget "maptoDev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0C6CA6932D42B07900DA198B /* Debug */,
				0C6CA6942D42B07900DA198B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0C6CA6FE2D42B1AA00DA198B /* Build configuration list for PBXNativeTarget "maptoLocal" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0C6CA6FF2D42B1AA00DA198B /* Debug */,
				0C6CA7002D42B1AA00DA198B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "mapto" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "mapto" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		0C6CA6632D42B07900DA198B /* XCRemoteSwiftPackageReference "ios-places-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/ios-places-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.0;
			};
		};
		0C6CA6CF2D42B1AA00DA198B /* XCRemoteSwiftPackageReference "ios-places-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/ios-places-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.0;
			};
		};
		0C9171412B9990C9007E62F2 /* XCRemoteSwiftPackageReference "ios-places-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/ios-places-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		0C6CA6622D42B07900DA198B /* GooglePlaces */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0C6CA6632D42B07900DA198B /* XCRemoteSwiftPackageReference "ios-places-sdk" */;
			productName = GooglePlaces;
		};
		0C6CA6CE2D42B1AA00DA198B /* GooglePlaces */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0C6CA6CF2D42B1AA00DA198B /* XCRemoteSwiftPackageReference "ios-places-sdk" */;
			productName = GooglePlaces;
		};
		0C9171422B9990C9007E62F2 /* GooglePlaces */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0C9171412B9990C9007E62F2 /* XCRemoteSwiftPackageReference "ios-places-sdk" */;
			productName = GooglePlaces;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
