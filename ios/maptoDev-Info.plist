<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>mapto</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow app to use</string>
	<key>NSMicrophoneUsageDescription</key>
   <string>We need microphone access to make calls</string>
   <key>NSPhoneUsageDescription</key>
    <string>We need phone access to initiate calls.</string>
	<key>UIAppFonts</key>
	<array>
		<string>EBGaramond-Bold.ttf</string>
		<string>EBGaramond-BoldItalic.ttf</string>
		<string>EBGaramond-ExtraBold.ttf</string>
		<string>EBGaramond-ExtraBoldItalic.ttf</string>
		<string>EBGaramond-Italic.ttf</string>
		<string>EBGaramond-Medium.ttf</string>
		<string>EBGaramond-MediumItalic.ttf</string>
		<string>EBGaramond-Regular.ttf</string>
		<string>EBGaramond-SemiBold.ttf</string>
		<string>EBGaramond-SemiBoldItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
		<string>audio</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	 <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
    </array>
	<key>UIBackgroundModes</key>
    <array>
    <string>remote-notification</string>
    <string>fetch</string>
    <string>audio</string>
    </array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to your photo library to allow you to upload profile pictures and share images.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app requires access to your location to find nearby drivers and provide accurate ride navigation, even when the app is in the background.</string>
</dict>
</plist>
