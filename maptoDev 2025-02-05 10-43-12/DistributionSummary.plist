<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>maptoDev.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>buildNumber</key>
			<string>1</string>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>C35E9A3BDF51538226D62FEB685F7C61323FD193</string>
				<key>dateExpires</key>
				<string>25/01/26</string>
				<key>type</key>
				<string>Apple Development</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>0.12.0</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>C35E9A3BDF51538226D62FEB685F7C61323FD193</string>
						<key>dateExpires</key>
						<string>25/01/26</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>hermes.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>7L9SZMBW6K</string>
						<key>name</key>
						<string>LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED</string>
					</dict>
					<key>versionNumber</key>
					<string>0.12.0</string>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>7L9SZMBW6K.com.mapto.development</string>
				<key>aps-environment</key>
				<string>development</string>
				<key>com.apple.developer.team-identifier</key>
				<string>7L9SZMBW6K</string>
				<key>get-task-allow</key>
				<true/>
			</dict>
			<key>name</key>
			<string>maptoDev.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>2e9c7938-e0b0-440f-b744-cb46d7c4899f</string>
				<key>dateExpires</key>
				<string>25/01/26</string>
				<key>name</key>
				<string>iOS Team Provisioning Profile: com.mapto.development</string>
			</dict>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>7L9SZMBW6K</string>
				<key>name</key>
				<string>LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED</string>
			</dict>
			<key>versionNumber</key>
			<string>1.0</string>
		</dict>
	</array>
</dict>
</plist>
