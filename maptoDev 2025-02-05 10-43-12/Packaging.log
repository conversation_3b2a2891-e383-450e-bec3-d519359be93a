2025-02-05 05:13:04 +0000  Initial pipeline context: <IDEDistributionProcessingPipelineContext: 0x7fa275cf0da0; archive(resolved)="<IDEArchive: 0x600014a70690>", distributionTask(resolved)="2", distributionDestination(resolved)="1", distributionMethod(resolved)="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team(resolved)="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	Chain (15, self inclusive):
	<IDEDistributionProcessingPipelineContext: 0x7fa275cf0da0; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionProcessingPipelineContext: 0x7fa275cc4470; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa276ed3300; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa2744d63e0; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa2743c0ed0; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa272cd5540; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa274f41200; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa274f5bd00; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa275e84a60; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="<IDEProvisioningDisambiguatableBasicTeam: 0x600003f4fa40; teamID='7L9SZMBW6K', teamName='LAAN GLOBAL TECHNOLOGY SERVICES PRIVATE LIMITED', teamType='Company', username='<EMAIL>', isFreeProvisioningTeam='0'>">
	<IDEDistributionContext: 0x7fa272fad7c0; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="(null)">
	<IDEDistributionContext: 0x7fa27392fb90; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="(null)">
	<IDEDistributionContext: 0x7fa2739718a0; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="(null)">
	<IDEDistributionContext: 0x7fa29e0c8a00; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="<IDEDistributionMethodDevelopmentSigned: 0x600006308090>", team="(null)">
	<IDEDistributionContext: 0x7fa273926890; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="(null)", team="(null)">
	<IDEDistributionContext: 0x7fa278693a70; archive = "<IDEArchive: 0x600014a70690>", distributionMethod="(null)", team="(null)">
</IDEDistributionProcessingPipelineContext: 0x7fa275cf0da0>
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionCreateDestRootStep
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionCopyItemStep
2025-02-05 05:13:04 +0000  Running /usr/bin/ditto '-V' '/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app' '/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app'
2025-02-05 05:13:04 +0000  >>> Copying /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app 
2025-02-05 05:13:04 +0000  copying file ./_CodeSignature/CodeResources ... 
2025-02-05 05:13:04 +0000  147546 bytes for ./_CodeSignature/CodeResources
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-BoldItalic.woff2 ... 
2025-02-05 05:13:04 +0000  203368 bytes for ./EBGaramond-BoldItalic.woff2
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-Regular.ttf ... 
2025-02-05 05:13:04 +0000  560884 bytes for ./EBGaramond-Regular.ttf
2025-02-05 05:13:04 +0000  copying file ./FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  855 bytes for ./FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./FirebaseCore_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  792 bytes for ./FirebaseCore_Privacy.bundle/Info.plist
copying file ./EBGaramond-Bold.ttf ... 
2025-02-05 05:13:04 +0000  563708 bytes for ./EBGaramond-Bold.ttf
copying file ./Geist-Light.woff2 ... 
2025-02-05 05:13:04 +0000  40336 bytes for ./Geist-Light.woff2
2025-02-05 05:13:04 +0000  copying file ./Geist-Medium.woff2 ... 
2025-02-05 05:13:04 +0000  41100 bytes for ./Geist-Medium.woff2
copying file ./EBGaramond-Italic.ttf ... 
2025-02-05 05:13:04 +0000  493056 bytes for ./EBGaramond-Italic.ttf
copying file ./EBGaramond-SemiBoldItalic.woff2 ... 
2025-02-05 05:13:04 +0000  201344 bytes for ./EBGaramond-SemiBoldItalic.woff2
2025-02-05 05:13:04 +0000  copying file ./FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  373 bytes for ./FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./FBLPromises_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  787 bytes for ./FBLPromises_Privacy.bundle/Info.plist
copying file ./EBGaramond-Regular.woff2 ... 
2025-02-05 05:13:04 +0000  185668 bytes for ./EBGaramond-Regular.woff2
2025-02-05 05:13:04 +0000  copying file ./maptoDev ... 
2025-02-05 05:13:04 +0000  17811312 bytes for ./maptoDev
copying file ./EBGaramond-BoldItalic.ttf ... 
2025-02-05 05:13:04 +0000  496724 bytes for ./EBGaramond-BoldItalic.ttf
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsPrivacy.bundle/GoogleMapsPrivacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  3204 bytes for ./GoogleMapsPrivacy.bundle/GoogleMapsPrivacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsPrivacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  783 bytes for ./GoogleMapsPrivacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-MediumItalic.woff2 ... 
2025-02-05 05:13:04 +0000  205056 bytes for ./EBGaramond-MediumItalic.woff2
copying file ./EBGaramond-Medium.woff2 ... 
2025-02-05 05:13:04 +0000  201288 bytes for ./EBGaramond-Medium.woff2
2025-02-05 05:13:04 +0000  copying file ./FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  855 bytes for ./FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./FirebaseCoreInternal_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  808 bytes for ./FirebaseCoreInternal_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  1105 bytes for ./GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./GoogleDataTransport_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  803 bytes for ./GoogleDataTransport_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-SemiBoldItalic.ttf ... 
2025-02-05 05:13:04 +0000  496896 bytes for ./EBGaramond-SemiBoldItalic.ttf
2025-02-05 05:13:04 +0000  copying file ./nanopb_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  374 bytes for ./nanopb_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./nanopb_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  779 bytes for ./nanopb_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  2357 bytes for ./FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./FirebaseMessaging_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  802 bytes for ./FirebaseMessaging_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GoogleService-Info.plist ... 
2025-02-05 05:13:04 +0000  859 bytes for ./GoogleService-Info.plist
2025-02-05 05:13:04 +0000  copying file ./FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  478 bytes for ./FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./FirebaseCoreExtension_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  810 bytes for ./FirebaseCoreExtension_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./Geist-Black.woff2 ... 
2025-02-05 05:13:04 +0000  41868 bytes for ./Geist-Black.woff2
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-Bold.woff2 ... 
2025-02-05 05:13:04 +0000  201504 bytes for ./EBGaramond-Bold.woff2
copying file ./EBGaramond-SemiBold.ttf ... 
2025-02-05 05:13:04 +0000  564048 bytes for ./EBGaramond-SemiBold.ttf
copying file ./Geist-UltraLight.woff2 ... 
2025-02-05 05:13:04 +0000  40200 bytes for ./Geist-UltraLight.woff2
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-SemiBold.woff2 ... 
2025-02-05 05:13:04 +0000  200864 bytes for ./EBGaramond-SemiBold.woff2
2025-02-05 05:13:04 +0000  copying file ./FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  1105 bytes for ./FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./FirebaseInstallations_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  810 bytes for ./FirebaseInstallations_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  1289 bytes for ./GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./GoogleUtilities_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  796 bytes for ./GoogleUtilities_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./main.jsbundle ... 
2025-02-05 05:13:04 +0000  4500236 bytes for ./main.jsbundle
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
64 bytes for ./RCTI18nStrings.bundle/de.lproj/Localizable.strings
copying file ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1312 bytes for ./RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/he.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1444 bytes for ./RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ar.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1500 bytes for ./RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/el.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1600 bytes for ./RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1436 bytes for ./RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/en.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1296 bytes for ./RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/uk.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1564 bytes for ./RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/nb.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1296 bytes for ./RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1268 bytes for ./RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/es.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1364 bytes for ./RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/da.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1300 bytes for ./RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/it.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1388 bytes for ./RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/sk.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1356 bytes for ./RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1368 bytes for ./RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ms.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1292 bytes for ./RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/sv.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1300 bytes for ./RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/cs.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1368 bytes for ./RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ko.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1332 bytes for ./RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1308 bytes for ./RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/hu.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1348 bytes for ./RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/tr.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1360 bytes for ./RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/pl.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1340 bytes for ./RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/vi.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1352 bytes for ./RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ru.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1576 bytes for ./RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/fr.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1360 bytes for ./RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/fi.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1340 bytes for ./RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/id.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1300 bytes for ./RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/nl.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1324 bytes for ./RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/th.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1784 bytes for ./RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/pt.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1356 bytes for ./RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/zu.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  2076 bytes for ./RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1372 bytes for ./RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/ro.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1344 bytes for ./RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  777 bytes for ./RCTI18nStrings.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/hr.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1352 bytes for ./RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2025-02-05 05:13:04 +0000  64 bytes for ./RCTI18nStrings.bundle/hi.lproj/Localizable.strings
2025-02-05 05:13:04 +0000  copying file ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:04 +0000  1640 bytes for ./RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
2025-02-05 05:13:04 +0000  copying file ./PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  1042 bytes for ./PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-MediumItalic.ttf ... 
2025-02-05 05:13:04 +0000  496176 bytes for ./EBGaramond-MediumItalic.ttf
2025-02-05 05:13:04 +0000  copying file ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
2025-02-05 05:13:04 +0000  3813 bytes for ./LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
2025-02-05 05:13:04 +0000  copying file ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
2025-02-05 05:13:04 +0000  924 bytes for ./LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2025-02-05 05:13:04 +0000  copying file ./LaunchScreen.storyboardc/Info.plist ... 
2025-02-05 05:13:04 +0000  258 bytes for ./LaunchScreen.storyboardc/Info.plist
2025-02-05 05:13:04 +0000  copying file ./RNSVGFilters.bundle/RNSVGArithmeticFilter.iphoneos.metallib ... 
2025-02-05 05:13:04 +0000  3993 bytes for ./RNSVGFilters.bundle/RNSVGArithmeticFilter.iphoneos.metallib
2025-02-05 05:13:04 +0000  copying file ./RNSVGFilters.bundle/RNSVGCompositeXor.iphoneos.metallib ... 
2025-02-05 05:13:04 +0000  3509 bytes for ./RNSVGFilters.bundle/RNSVGCompositeXor.iphoneos.metallib
copying file ./RNSVGFilters.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  774 bytes for ./RNSVGFilters.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./Geist-UltraBlack.woff2 ... 
2025-02-05 05:13:04 +0000  42076 bytes for ./Geist-UltraBlack.woff2
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/de.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4417 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/de.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/he.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5000 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/he.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_AU.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4101 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_AU.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ar.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5106 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ar.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/el.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5896 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/el.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ja.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  3949 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ja.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sq.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4848 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sq.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4114 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uk.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5569 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uk.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_419.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5076 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_419.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_MX.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5076 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_MX.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_CN.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  3634 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_CN.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nb.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4273 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nb.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5170 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sw.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4554 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sw.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_BR.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4963 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_BR.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/da.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4363 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/da.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/it.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4506 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/it.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/oss_licenses_places.txt.gz ... 
2025-02-05 05:13:04 +0000  28140 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/oss_licenses_places.txt.gz
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1654 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sk.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5419 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sk.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  3701 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_PT.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5255 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_PT.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5722 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sr.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ms.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4371 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ms.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sv.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4496 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sv.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/cs.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5251 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/cs.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_IN.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4103 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_IN.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ko.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  3809 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ko.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hy.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  6019 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hy.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2419 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2428 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/my.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  6451 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/my.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hu.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5096 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hu.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_HK.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  3667 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_HK.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ka.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5849 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ka.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1859 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/tr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4839 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/tr.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pl.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4885 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pl.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_TW.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  3671 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_TW.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_GB.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4101 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_GB.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2738 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/vi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5789 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/vi.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lv.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5605 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lv.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lt.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5587 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lt.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ru.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5723 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ru.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr_CA.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5082 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr_CA.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uz.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4981 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uz.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5119 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4545 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fi.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/id.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4265 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/id.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nl.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4122 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nl.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/th.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5514 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/th.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/az.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5399 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/az.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4963 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  4372 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2861 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ro.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5210 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ro.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  343 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5025 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hr.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  5731 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hi.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ca.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:04 +0000  4942 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ca.lproj/GooglePlaces.strings
2025-02-05 05:13:04 +0000  copying file ./GooglePlaces_GooglePlacesTarget.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  712 bytes for ./GooglePlaces_GooglePlacesTarget.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./ReactNativeMapsPrivacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  1209 bytes for ./ReactNativeMapsPrivacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./ReactNativeMapsPrivacy.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  795 bytes for ./ReactNativeMapsPrivacy.bundle/Info.plist
copying file ./GoogleService-Info-Dev.plist ... 
2025-02-05 05:13:04 +0000  479 bytes for ./GoogleService-Info-Dev.plist
2025-02-05 05:13:04 +0000  copying file ./Geist-Thin.woff2 ... 
2025-02-05 05:13:04 +0000  38336 bytes for ./Geist-Thin.woff2
2025-02-05 05:13:04 +0000  copying file ./Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2025-02-05 05:13:04 +0000  1798 bytes for ./Frameworks/hermes.framework/_CodeSignature/CodeResources
2025-02-05 05:13:04 +0000  copying file ./Frameworks/hermes.framework/hermes ... 
2025-02-05 05:13:04 +0000  4444032 bytes for ./Frameworks/hermes.framework/hermes
copying file ./Frameworks/hermes.framework/Info.plist ... 
2025-02-05 05:13:04 +0000  815 bytes for ./Frameworks/hermes.framework/Info.plist
copying file ./EBGaramond-ExtraBold.woff2 ... 
2025-02-05 05:13:04 +0000  192080 bytes for ./EBGaramond-ExtraBold.woff2
2025-02-05 05:13:04 +0000  copying file ./Geist-Regular.woff2 ... 
2025-02-05 05:13:04 +0000  40016 bytes for ./Geist-Regular.woff2
2025-02-05 05:13:04 +0000  copying file ./embedded.mobileprovision ... 
2025-02-05 05:13:04 +0000  21048 bytes for ./embedded.mobileprovision
2025-02-05 05:13:04 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-05 05:13:04 +0000  761 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-05 05:13:04 +0000  405 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
2025-02-05 05:13:04 +0000  290 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
2025-02-05 05:13:04 +0000  copying file ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2025-02-05 05:13:04 +0000  913 bytes for ./assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/info.png ... 
2025-02-05 05:13:04 +0000  552 bytes for ./assets/assets/images/info.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/my-location.png ... 
2025-02-05 05:13:04 +0000  529 bytes for ./assets/assets/images/my-location.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/loader.png ... 
2025-02-05 05:13:04 +0000  159 bytes for ./assets/assets/images/loader.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/auto.png ... 
2025-02-05 05:13:04 +0000  3155 bytes for ./assets/assets/images/auto.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/activePickup.png ... 
2025-02-05 05:13:04 +0000  197 bytes for ./assets/assets/images/activePickup.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/mapPin.png ... 
2025-02-05 05:13:04 +0000  2795 bytes for ./assets/assets/images/mapPin.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/pickup.png ... 
2025-02-05 05:13:04 +0000  254 bytes for ./assets/assets/images/pickup.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/diamondActive.png ... 
2025-02-05 05:13:04 +0000  265 bytes for ./assets/assets/images/diamondActive.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/Ellipse.png ... 
2025-02-05 05:13:04 +0000  175 bytes for ./assets/assets/images/Ellipse.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/suv.png ... 
2025-02-05 05:13:04 +0000  3124 bytes for ./assets/assets/images/suv.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/handler.png ... 
2025-02-05 05:13:04 +0000  141 bytes for ./assets/assets/images/handler.png
copying file ./assets/assets/images/dot.png ... 
2025-02-05 05:13:04 +0000  168 bytes for ./assets/assets/images/dot.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/hatch.png ... 
2025-02-05 05:13:04 +0000  3189 bytes for ./assets/assets/images/hatch.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/clear.png ... 
2025-02-05 05:13:04 +0000  477 bytes for ./assets/assets/images/clear.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/dropMarker.png ... 
2025-02-05 05:13:04 +0000  598 bytes for ./assets/assets/images/dropMarker.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/divider.png ... 
2025-02-05 05:13:04 +0000  347 bytes for ./assets/assets/images/divider.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/drag.png ... 
2025-02-05 05:13:04 +0000  12961 bytes for ./assets/assets/images/drag.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/map.png ... 
2025-02-05 05:13:04 +0000  36199 bytes for ./assets/assets/images/map.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/pickupSpots.png ... 
2025-02-05 05:13:04 +0000  969 bytes for ./assets/assets/images/pickupSpots.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/chevron-down.png ... 
2025-02-05 05:13:04 +0000  336 bytes for ./assets/assets/images/chevron-down.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/home.png ... 
2025-02-05 05:13:04 +0000  491 bytes for ./assets/assets/images/home.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/user.png ... 
2025-02-05 05:13:04 +0000  13642 bytes for ./assets/assets/images/user.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/mapto.png ... 
2025-02-05 05:13:04 +0000  4214 bytes for ./assets/assets/images/mapto.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/activeDrop.png ... 
2025-02-05 05:13:04 +0000  281 bytes for ./assets/assets/images/activeDrop.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/verticalLine.png ... 
2025-02-05 05:13:04 +0000  145 bytes for ./assets/assets/images/verticalLine.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/background.png ... 
2025-02-05 05:13:04 +0000  193468 bytes for ./assets/assets/images/background.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/drop.png ... 
2025-02-05 05:13:04 +0000  343 bytes for ./assets/assets/images/drop.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/pickupMarker.png ... 
2025-02-05 05:13:04 +0000  303 bytes for ./assets/assets/images/pickupMarker.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/aboutImage.png ... 
2025-02-05 05:13:04 +0000  123970 bytes for ./assets/assets/images/aboutImage.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/profile.png ... 
2025-02-05 05:13:04 +0000  730 bytes for ./assets/assets/images/profile.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/diamondGrey.png ... 
2025-02-05 05:13:04 +0000  271 bytes for ./assets/assets/images/diamondGrey.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/location.png ... 
2025-02-05 05:13:04 +0000  580 bytes for ./assets/assets/images/location.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/rides.png ... 
2025-02-05 05:13:04 +0000  452 bytes for ./assets/assets/images/rides.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/activePoints.png ... 
2025-02-05 05:13:04 +0000  17713 bytes for ./assets/assets/images/activePoints.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/diamond.png ... 
2025-02-05 05:13:04 +0000  318 bytes for ./assets/assets/images/diamond.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/star.png ... 
2025-02-05 05:13:04 +0000  334 bytes for ./assets/assets/images/star.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/line.png ... 
2025-02-05 05:13:04 +0000  316 bytes for ./assets/assets/images/line.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/more.png ... 
2025-02-05 05:13:04 +0000  173 bytes for ./assets/assets/images/more.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/fav.png ... 
2025-02-05 05:13:04 +0000  633 bytes for ./assets/assets/images/fav.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/setLocation.png ... 
2025-02-05 05:13:04 +0000  330 bytes for ./assets/assets/images/setLocation.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/suvLuxe.png ... 
2025-02-05 05:13:04 +0000  3561 bytes for ./assets/assets/images/suvLuxe.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/back.png ... 
2025-02-05 05:13:04 +0000  288 bytes for ./assets/assets/images/back.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/start.png ... 
2025-02-05 05:13:04 +0000  114799 bytes for ./assets/assets/images/start.png
2025-02-05 05:13:04 +0000  copying file ./assets/assets/images/bg.png ... 
2025-02-05 05:13:04 +0000  296564 bytes for ./assets/assets/images/bg.png
copying file ./assets/assets/images/close.png ... 
2025-02-05 05:13:04 +0000  283 bytes for ./assets/assets/images/close.png
2025-02-05 05:13:04 +0000  copying file ./assets/src/icons/LOADER.gif ... 
2025-02-05 05:13:04 +0000  111086 bytes for ./assets/src/icons/LOADER.gif
copying file ./assets/src/icons/mapto.gif ... 
2025-02-05 05:13:04 +0000  273445 bytes for ./assets/src/icons/mapto.gif
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-ExtraBoldItalic.woff2 ... 
2025-02-05 05:13:04 +0000  193908 bytes for ./EBGaramond-ExtraBoldItalic.woff2
2025-02-05 05:13:04 +0000  copying file ./Geist-SemiBold.woff2 ... 
2025-02-05 05:13:04 +0000  41952 bytes for ./Geist-SemiBold.woff2
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2386 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png ... 
2025-02-05 05:13:04 +0000  451 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1055 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2374 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png ... 
2025-02-05 05:13:04 +0000  236 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1051 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4308 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4436 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4078 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  943 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  5230 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4694 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3880 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png ... 
2025-02-05 05:13:04 +0000  602 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4410 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  663 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4061 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png ... 
2025-02-05 05:13:04 +0000  422 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4963 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png ... 
2025-02-05 05:13:04 +0000  362 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4376 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png ... 
2025-02-05 05:13:04 +0000  13906 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2446 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  3304 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4376 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png ... 
2025-02-05 05:13:04 +0000  1114 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3871 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1240 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf ... 
2025-02-05 05:13:04 +0000  392092 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4211 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4377 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4171 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1203 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4388 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4321 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1577 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4115 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4441 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png ... 
2025-02-05 05:13:04 +0000  20924 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4389 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png ... 
2025-02-05 05:13:04 +0000  720 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png ... 
2025-02-05 05:13:04 +0000  492 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png ... 
2025-02-05 05:13:04 +0000  127 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4754 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4033 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png ... 
2025-02-05 05:13:04 +0000  1934 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png ... 
2025-02-05 05:13:04 +0000  631 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4451 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png ... 
2025-02-05 05:13:04 +0000  93 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png ... 
2025-02-05 05:13:04 +0000  494 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4667 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png ... 
2025-02-05 05:13:04 +0000  334 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png ... 
2025-02-05 05:13:04 +0000  3393 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1229 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4078 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3904 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png ... 
2025-02-05 05:13:04 +0000  13321 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4312 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  860 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4524 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1373 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png ... 
2025-02-05 05:13:04 +0000  5684 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf ... 
2025-02-05 05:13:04 +0000  353228 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4195 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3832 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png ... 
2025-02-05 05:13:04 +0000  74 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4322 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  433 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png ... 
2025-02-05 05:13:04 +0000  742 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  992 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car ... 
2025-02-05 05:13:04 +0000  48575 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4175 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4491 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3857 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1643 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png ... 
2025-02-05 05:13:04 +0000  7660 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png ... 
2025-02-05 05:13:04 +0000  12519 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1210 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4078 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png ... 
2025-02-05 05:13:04 +0000  315 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png ... 
2025-02-05 05:13:04 +0000  84 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4232 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  515 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png ... 
2025-02-05 05:13:04 +0000  223 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4617 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4321 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4771 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib ... 
2025-02-05 05:13:04 +0000  305549 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png ... 
2025-02-05 05:13:04 +0000  7485 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1826 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png ... 
2025-02-05 05:13:04 +0000  435 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4335 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1841 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1360 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png ... 
2025-02-05 05:13:04 +0000  1029 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4071 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png ... 
2025-02-05 05:13:04 +0000  441 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4633 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4286 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  3954 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4144 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4157 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1066 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4228 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png ... 
2025-02-05 05:13:04 +0000  425 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png ... 
2025-02-05 05:13:04 +0000  61437 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png ... 
2025-02-05 05:13:04 +0000  7433 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png ... 
2025-02-05 05:13:04 +0000  483 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4388 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib ... 
2025-02-05 05:13:04 +0000  307182 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1012 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1069 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png ... 
2025-02-05 05:13:04 +0000  95 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png ... 
2025-02-05 05:13:04 +0000  498 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1353 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1472 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4491 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  359 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png ... 
2025-02-05 05:13:04 +0000  40485 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png ... 
2025-02-05 05:13:04 +0000  382 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png ... 
2025-02-05 05:13:04 +0000  601 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  825 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2221 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  2560 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4264 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4242 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings ... 
2025-02-05 05:13:04 +0000  4274 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1962 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  1380 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  743 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz ... 
2025-02-05 05:13:04 +0000  173531 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car ... 
2025-02-05 05:13:04 +0000  43967 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png ... 
2025-02-05 05:13:04 +0000  455 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:04 +0000  454 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom ... 
2025-02-05 05:13:04 +0000  4696 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist ... 
2025-02-05 05:13:04 +0000  1197 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist
copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom ... 
2025-02-05 05:13:04 +0000  5292 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom ... 
2025-02-05 05:13:04 +0000  4802 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  363 bytes for ./GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./GoogleMapsResources.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  788 bytes for ./GoogleMapsResources.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./Info.plist ... 
2025-02-05 05:13:04 +0000  1638 bytes for ./Info.plist
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-Italic.woff2 ... 
2025-02-05 05:13:04 +0000  188220 bytes for ./EBGaramond-Italic.woff2
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-ExtraBoldItalic.ttf ... 
2025-02-05 05:13:04 +0000  495860 bytes for ./EBGaramond-ExtraBoldItalic.ttf
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-ExtraBold.ttf ... 
2025-02-05 05:13:04 +0000  563204 bytes for ./EBGaramond-ExtraBold.ttf
2025-02-05 05:13:04 +0000  copying file ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:04 +0000  512 bytes for ./RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:04 +0000  copying file ./RNCAsyncStorage_resources.bundle/Info.plist ... 
2025-02-05 05:13:04 +0000  800 bytes for ./RNCAsyncStorage_resources.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./PkgInfo ... 
2025-02-05 05:13:04 +0000  8 bytes for ./PkgInfo
2025-02-05 05:13:04 +0000  copying file ./EBGaramond-Medium.ttf ... 
2025-02-05 05:13:04 +0000  563752 bytes for ./EBGaramond-Medium.ttf
copying file ./Geist-Bold.woff2 ... 
2025-02-05 05:13:04 +0000  41640 bytes for ./Geist-Bold.woff2
2025-02-05 05:13:04 +0000  copying file ./GeistVariableVF.woff2 ... 
2025-02-05 05:13:04 +0000  53444 bytes for ./GeistVariableVF.woff2
2025-02-05 05:13:04 +0000  /usr/bin/ditto exited with 0
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionEmbedProfileStep
2025-02-05 05:13:04 +0000  Skipping profile for item: <IDEDistributionItem: 0x600014475ce0; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x600014a775d0:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60001817a2b0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009bb8d80; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='(null)', teamID='7L9SZMBW6K', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x6000144a0720:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionInfoPlistStep
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionInfoPlistStep because it said so
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionInfoPlistStep because it said so
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionAppThinningPlistStep
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionAppThinningPlistStep because it said so
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionSymbolsStep
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionSymbolsStep because it said so
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionAppThinningStep
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionAppThinningStep because it said so
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionArchThinningStep
2025-02-05 05:13:04 +0000  Item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-02-05 05:13:04 +0000  Archs to thin for item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework are ["arm64e"]
2025-02-05 05:13:04 +0000  Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/Frameworks/hermes.framework/hermes' '-verify_arch' 'arm64e'
2025-02-05 05:13:04 +0000  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-02-05 05:13:04 +0000  Skipping architecture thinning for item "hermes" because arch "arm64e" wasn't found
2025-02-05 05:13:04 +0000  Item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-02-05 05:13:04 +0000  Archs to thin for item /Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app are ["arm64e"]
2025-02-05 05:13:04 +0000  Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/maptoDev' '-verify_arch' 'arm64e'
2025-02-05 05:13:04 +0000  /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-02-05 05:13:04 +0000  Skipping architecture thinning for item "maptoDev" because arch "arm64e" wasn't found
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionODRStep
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionStripXattrsStep
2025-02-05 05:13:04 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2025-02-05 05:13:04 +0000  Skipping stripping extended attributes because the codesign step will strip them.
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionCodesignStep
2025-02-05 05:13:04 +0000  Entitlements for <IDEDistributionItem: 0x600014475ce0; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x600014a775d0:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60001817a2b0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009ba8540; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='(null)', teamID='7L9SZMBW6K', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x6000144a0720:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'>: {
}
2025-02-05 05:13:04 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-02-05 05:13:04 +0000  Entitlements for <IDEDistributionItem: 0x600014475ce0; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x600014a775d0:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60001817a2b0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009ba5980; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='(null)', teamID='7L9SZMBW6K', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x6000144a0720:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> are: {
}
2025-02-05 05:13:04 +0000  Writing entitlements for <IDEDistributionItem: 0x600014475ce0; bundleID='dev.hermesengine.iphoneos', path='<DVTFilePath:0x600014a775d0:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x60001817a2b0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009ba4480; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='(null)', teamID='7L9SZMBW6K', identifier='dev.hermesengine.iphoneos', executablePath='<DVTFilePath:0x6000144a0720:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/Frameworks/hermes.framework/hermes'>', hardenedRuntime='0'>'> to: /var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/entitlements~~~VRRSt2
2025-02-05 05:13:04 +0000  invoking codesign: <NSConcreteTask: 0x600018182620; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    C35E9A3BDF51538226D62FEB685F7C61323FD193,
    "--entitlements",
    "/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/entitlements~~~VRRSt2",
    "--preserve-metadata=identifier,flags,runtime",
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/Frameworks/hermes.framework"
)'>
2025-02-05 05:13:04 +0000  codesign output: /var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/Frameworks/hermes.framework: replacing existing signature
/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/Frameworks/hermes.framework: signed bundle with Mach-O thin (arm64) [dev.hermesengine.iphoneos]
2025-02-05 05:13:04 +0000  Entitlements for <IDEDistributionItem: 0x60001446d3e0; bundleID='com.mapto.development', path='<DVTFilePath:0x6000145baf40:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x600018138280; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009bb8d80; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='{
    "application-identifier" = "7L9SZMBW6K.com.mapto.development";
    "aps-environment" = development;
    "com.apple.developer.team-identifier" = 7L9SZMBW6K;
    "get-task-allow" = 1;
}', teamID='7L9SZMBW6K', identifier='com.mapto.development', executablePath='<DVTFilePath:0x6000144f0900:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/maptoDev'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "7L9SZMBW6K.com.mapto.development";
    "aps-environment" = development;
    "com.apple.developer.team-identifier" = 7L9SZMBW6K;
    "get-task-allow" = 1;
}
2025-02-05 05:13:04 +0000  Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-02-05 05:13:04 +0000  Entitlements for <IDEDistributionItem: 0x60001446d3e0; bundleID='com.mapto.development', path='<DVTFilePath:0x6000145baf40:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x600018138280; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009ba8540; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='{
    "application-identifier" = "7L9SZMBW6K.com.mapto.development";
    "aps-environment" = development;
    "com.apple.developer.team-identifier" = 7L9SZMBW6K;
    "get-task-allow" = 1;
}', teamID='7L9SZMBW6K', identifier='com.mapto.development', executablePath='<DVTFilePath:0x6000144f0900:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/maptoDev'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "7L9SZMBW6K.com.mapto.development";
    "aps-environment" = development;
    "com.apple.developer.team-identifier" = 7L9SZMBW6K;
    "get-task-allow" = 1;
}
2025-02-05 05:13:04 +0000  Writing entitlements for <IDEDistributionItem: 0x60001446d3e0; bundleID='com.mapto.development', path='<DVTFilePath:0x6000145baf40:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x600018138280; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600009bbb0c0; name='Apple Development: Nakendra Kumar S (RRQ52FFH97)', hash='C35E9A3BDF51538226D62FEB685F7C61323FD193', serialNumber='4F91815171040C08C49F6080BA78A643', certificateKinds='(
    "1.2.840.113635.**********",
    "1.2.840.113635.*********"
), issueDate='2025-01-25 05:01:50 +0000''>', entitlements='{
    "application-identifier" = "7L9SZMBW6K.com.mapto.development";
    "aps-environment" = development;
    "com.apple.developer.team-identifier" = 7L9SZMBW6K;
    "get-task-allow" = 1;
}', teamID='7L9SZMBW6K', identifier='com.mapto.development', executablePath='<DVTFilePath:0x6000144f0900:'/Users/<USER>/Library/Developer/Xcode/Archives/2025-02-05/maptoDev 05-02-25, 10.41 AM.xcarchive/Products/Applications/maptoDev.app/maptoDev'>', hardenedRuntime='0'>'> to: /var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/entitlements~~~CTwUDm
2025-02-05 05:13:04 +0000  invoking codesign: <NSConcreteTask: 0x600018182850; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    C35E9A3BDF51538226D62FEB685F7C61323FD193,
    "--entitlements",
    "/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/entitlements~~~CTwUDm",
    "--preserve-metadata=identifier,flags,runtime",
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app"
)'>
2025-02-05 05:13:04 +0000  codesign output: /var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app: replacing existing signature
/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app: signed app bundle with Mach-O thin (arm64) [com.mapto.development]
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionZipODRItemStep
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2025-02-05 05:13:04 +0000  Skipping step: IDEDistributionZipODRItemStep because it said so
2025-02-05 05:13:04 +0000  Processing step: IDEDistributionCreateIPAStep
2025-02-05 05:13:04 +0000  Running /usr/bin/ditto '-V' '-c' '-k' '--norsrc' '/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root' '/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Packages/maptoDev.ipa'
2025-02-05 05:13:04 +0000  >>> Copying /var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root 
2025-02-05 05:13:04 +0000  copying file ./Payload/maptoDev.app/_CodeSignature/CodeResources ... 
2025-02-05 05:13:04 +0000  147546 bytes for ./Payload/maptoDev.app/_CodeSignature/CodeResources
copying file ./Payload/maptoDev.app/EBGaramond-BoldItalic.woff2 ... 
2025-02-05 05:13:04 +0000  203368 bytes for ./Payload/maptoDev.app/EBGaramond-BoldItalic.woff2
copying file ./Payload/maptoDev.app/EBGaramond-Regular.ttf ... 
2025-02-05 05:13:04 +0000  560884 bytes for ./Payload/maptoDev.app/EBGaramond-Regular.ttf
2025-02-05 05:13:04 +0000  copying file ./Payload/maptoDev.app/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy ... 
855 bytes for ./Payload/maptoDev.app/FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FirebaseCore_Privacy.bundle/Info.plist ... 
792 bytes for ./Payload/maptoDev.app/FirebaseCore_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/EBGaramond-Bold.ttf ... 
2025-02-05 05:13:04 +0000  563708 bytes for ./Payload/maptoDev.app/EBGaramond-Bold.ttf
copying file ./Payload/maptoDev.app/Geist-Light.woff2 ... 
2025-02-05 05:13:04 +0000  40336 bytes for ./Payload/maptoDev.app/Geist-Light.woff2
copying file ./Payload/maptoDev.app/Geist-Medium.woff2 ... 
2025-02-05 05:13:04 +0000  41100 bytes for ./Payload/maptoDev.app/Geist-Medium.woff2
copying file ./Payload/maptoDev.app/EBGaramond-Italic.ttf ... 
2025-02-05 05:13:04 +0000  493056 bytes for ./Payload/maptoDev.app/EBGaramond-Italic.ttf
copying file ./Payload/maptoDev.app/EBGaramond-SemiBoldItalic.woff2 ... 
2025-02-05 05:13:04 +0000  201344 bytes for ./Payload/maptoDev.app/EBGaramond-SemiBoldItalic.woff2
2025-02-05 05:13:04 +0000  copying file ./Payload/maptoDev.app/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy ... 
373 bytes for ./Payload/maptoDev.app/FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FBLPromises_Privacy.bundle/Info.plist ... 
787 bytes for ./Payload/maptoDev.app/FBLPromises_Privacy.bundle/Info.plist
2025-02-05 05:13:04 +0000  copying file ./Payload/maptoDev.app/EBGaramond-Regular.woff2 ... 
2025-02-05 05:13:04 +0000  185668 bytes for ./Payload/maptoDev.app/EBGaramond-Regular.woff2
copying file ./Payload/maptoDev.app/maptoDev ... 
2025-02-05 05:13:05 +0000  17811312 bytes for ./Payload/maptoDev.app/maptoDev
copying file ./Payload/maptoDev.app/EBGaramond-BoldItalic.ttf ... 
2025-02-05 05:13:05 +0000  496724 bytes for ./Payload/maptoDev.app/EBGaramond-BoldItalic.ttf
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GoogleMapsPrivacy.bundle/GoogleMapsPrivacy.bundle/PrivacyInfo.xcprivacy ... 
3204 bytes for ./Payload/maptoDev.app/GoogleMapsPrivacy.bundle/GoogleMapsPrivacy.bundle/PrivacyInfo.xcprivacy
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GoogleMapsPrivacy.bundle/Info.plist ... 
783 bytes for ./Payload/maptoDev.app/GoogleMapsPrivacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/EBGaramond-MediumItalic.woff2 ... 
2025-02-05 05:13:05 +0000  205056 bytes for ./Payload/maptoDev.app/EBGaramond-MediumItalic.woff2
copying file ./Payload/maptoDev.app/EBGaramond-Medium.woff2 ... 
2025-02-05 05:13:05 +0000  201288 bytes for ./Payload/maptoDev.app/EBGaramond-Medium.woff2
copying file ./Payload/maptoDev.app/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:05 +0000  855 bytes for ./Payload/maptoDev.app/FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FirebaseCoreInternal_Privacy.bundle/Info.plist ... 
808 bytes for ./Payload/maptoDev.app/FirebaseCoreInternal_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:05 +0000  1105 bytes for ./Payload/maptoDev.app/GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/GoogleDataTransport_Privacy.bundle/Info.plist ... 
803 bytes for ./Payload/maptoDev.app/GoogleDataTransport_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/EBGaramond-SemiBoldItalic.ttf ... 
2025-02-05 05:13:05 +0000  496896 bytes for ./Payload/maptoDev.app/EBGaramond-SemiBoldItalic.ttf
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy ... 
374 bytes for ./Payload/maptoDev.app/nanopb_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/nanopb_Privacy.bundle/Info.plist ... 
779 bytes for ./Payload/maptoDev.app/nanopb_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy ... 
2357 bytes for ./Payload/maptoDev.app/FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FirebaseMessaging_Privacy.bundle/Info.plist ... 
802 bytes for ./Payload/maptoDev.app/FirebaseMessaging_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/GoogleService-Info.plist ... 
859 bytes for ./Payload/maptoDev.app/GoogleService-Info.plist
copying file ./Payload/maptoDev.app/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy ... 
478 bytes for ./Payload/maptoDev.app/FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FirebaseCoreExtension_Privacy.bundle/Info.plist ... 
810 bytes for ./Payload/maptoDev.app/FirebaseCoreExtension_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/Geist-Black.woff2 ... 
41868 bytes for ./Payload/maptoDev.app/Geist-Black.woff2
copying file ./Payload/maptoDev.app/EBGaramond-Bold.woff2 ... 
2025-02-05 05:13:05 +0000  201504 bytes for ./Payload/maptoDev.app/EBGaramond-Bold.woff2
copying file ./Payload/maptoDev.app/EBGaramond-SemiBold.ttf ... 
2025-02-05 05:13:05 +0000  564048 bytes for ./Payload/maptoDev.app/EBGaramond-SemiBold.ttf
copying file ./Payload/maptoDev.app/Geist-UltraLight.woff2 ... 
2025-02-05 05:13:05 +0000  40200 bytes for ./Payload/maptoDev.app/Geist-UltraLight.woff2
copying file ./Payload/maptoDev.app/EBGaramond-SemiBold.woff2 ... 
2025-02-05 05:13:05 +0000  200864 bytes for ./Payload/maptoDev.app/EBGaramond-SemiBold.woff2
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy ... 
1105 bytes for ./Payload/maptoDev.app/FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/FirebaseInstallations_Privacy.bundle/Info.plist ... 
810 bytes for ./Payload/maptoDev.app/FirebaseInstallations_Privacy.bundle/Info.plist
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy ... 
1289 bytes for ./Payload/maptoDev.app/GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/GoogleUtilities_Privacy.bundle/Info.plist ... 
2025-02-05 05:13:05 +0000  796 bytes for ./Payload/maptoDev.app/GoogleUtilities_Privacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/main.jsbundle ... 
2025-02-05 05:13:05 +0000  4500236 bytes for ./Payload/maptoDev.app/main.jsbundle
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/de.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin ... 
1312 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/de.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/he.lproj/Localizable.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin ... 
1444 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/he.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ar.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1500 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ar.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/el.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1600 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/el.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hans.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1296 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hans.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ja.lproj/Localizable.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1436 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ja.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/en.lproj/Localizable.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1296 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant-HK.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/uk.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1564 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/uk.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/nb.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin ... 
1296 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/nb.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/en-GB.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin ... 
1268 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/en-GB.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/es.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin ... 
1364 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/es.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/da.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1300 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/da.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/it.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1388 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/it.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/sk.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1356 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/sk.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/es-ES.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1368 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/es-ES.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ms.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1292 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ms.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/sv.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1300 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/sv.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/cs.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin ... 
1368 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/cs.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ko.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin ... 
1332 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ko.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin ... 
1308 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zh-Hant.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hu.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1348 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hu.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/tr.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1360 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/tr.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pl.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1340 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pl.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/vi.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1352 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/vi.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ru.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1576 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ru.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/fr.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1360 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/fr.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/fi.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1340 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/fi.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/id.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin ... 
1300 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/id.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/nl.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin ... 
1324 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/nl.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/th.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin ... 
1784 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/th.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin ... 
1356 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt.lproj/fbt_language_pack.bin
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zu.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  2076 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/zu.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt-PT.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1372 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/pt-PT.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings ... 
64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ro.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin ... 
2025-02-05 05:13:05 +0000  1344 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/ro.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/Info.plist ... 
777 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/Info.plist
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hr.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin ... 
1352 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hr.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings ... 
2025-02-05 05:13:05 +0000  64 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hi.lproj/Localizable.strings
copying file ./Payload/maptoDev.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin ... 
1640 bytes for ./Payload/maptoDev.app/RCTI18nStrings.bundle/hi.lproj/fbt_language_pack.bin
copying file ./Payload/maptoDev.app/PrivacyInfo.xcprivacy ... 
2025-02-05 05:13:05 +0000  1042 bytes for ./Payload/maptoDev.app/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/EBGaramond-MediumItalic.ttf ... 
2025-02-05 05:13:05 +0000  496176 bytes for ./Payload/maptoDev.app/EBGaramond-MediumItalic.ttf
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib ... 
3813 bytes for ./Payload/maptoDev.app/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib
copying file ./Payload/maptoDev.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib ... 
924 bytes for ./Payload/maptoDev.app/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/LaunchScreen.storyboardc/Info.plist ... 
258 bytes for ./Payload/maptoDev.app/LaunchScreen.storyboardc/Info.plist
copying file ./Payload/maptoDev.app/RNSVGFilters.bundle/RNSVGArithmeticFilter.iphoneos.metallib ... 
2025-02-05 05:13:05 +0000  3993 bytes for ./Payload/maptoDev.app/RNSVGFilters.bundle/RNSVGArithmeticFilter.iphoneos.metallib
copying file ./Payload/maptoDev.app/RNSVGFilters.bundle/RNSVGCompositeXor.iphoneos.metallib ... 
2025-02-05 05:13:05 +0000  3509 bytes for ./Payload/maptoDev.app/RNSVGFilters.bundle/RNSVGCompositeXor.iphoneos.metallib
copying file ./Payload/maptoDev.app/RNSVGFilters.bundle/Info.plist ... 
774 bytes for ./Payload/maptoDev.app/RNSVGFilters.bundle/Info.plist
copying file ./Payload/maptoDev.app/Geist-UltraBlack.woff2 ... 
2025-02-05 05:13:05 +0000  42076 bytes for ./Payload/maptoDev.app/Geist-UltraBlack.woff2
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/de.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4417 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/de.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/he.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5000 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/he.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_AU.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4101 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_AU.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ar.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5106 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ar.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/el.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5896 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/el.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ja.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  3949 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ja.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sq.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4848 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sq.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4114 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uk.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5569 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uk.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_419.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5076 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_419.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_MX.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5076 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es_MX.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_CN.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  3634 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_CN.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nb.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4273 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nb.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5170 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/es.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sw.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4554 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sw.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_BR.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4963 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_BR.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/da.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4363 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/da.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/it.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4506 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/it.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/oss_licenses_places.txt.gz ... 
2025-02-05 05:13:05 +0000  28140 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/oss_licenses_places.txt.gz
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:05 +0000  1654 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sk.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5419 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sk.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:05 +0000  3701 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_PT.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5255 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt_PT.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5722 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sr.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ms.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4371 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ms.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sv.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4496 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/sv.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/cs.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5251 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/cs.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_IN.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4103 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_IN.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ko.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  3809 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ko.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hy.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  6019 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hy.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:05 +0000  2419 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2428 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/my.lproj/GooglePlaces.strings ... 
6451 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/my.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hu.lproj/GooglePlaces.strings ... 
5096 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hu.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_HK.lproj/GooglePlaces.strings ... 
3667 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_HK.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ka.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5849 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ka.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
1859 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/tr.lproj/GooglePlaces.strings ... 
4839 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/tr.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pl.lproj/GooglePlaces.strings ... 
4885 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pl.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_TW.lproj/GooglePlaces.strings ... 
3671 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/zh_TW.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_GB.lproj/GooglePlaces.strings ... 
4101 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/en_GB.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2738 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/vi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5789 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/vi.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lv.lproj/GooglePlaces.strings ... 
5605 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lv.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lt.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5587 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/lt.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ru.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5723 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ru.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr_CA.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5082 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr_CA.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uz.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4981 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/uz.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5119 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fr.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4545 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/fi.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/id.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4265 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/id.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nl.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4122 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/nl.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/th.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5514 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/th.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/az.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5399 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/az.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4963 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/pt.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
4372 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL> ... 
2025-02-05 05:13:05 +0000  2861 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ro.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5210 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ro.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/Info.plist ... 
2025-02-05 05:13:05 +0000  343 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/Info.plist
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hr.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5025 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hr.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hi.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  5731 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/hi.lproj/GooglePlaces.strings
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ca.lproj/GooglePlaces.strings ... 
2025-02-05 05:13:05 +0000  4942 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/GooglePlaces.bundle/ca.lproj/GooglePlaces.strings
copying file ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/Info.plist ... 
2025-02-05 05:13:05 +0000  712 bytes for ./Payload/maptoDev.app/GooglePlaces_GooglePlacesTarget.bundle/Info.plist
copying file ./Payload/maptoDev.app/ReactNativeMapsPrivacy.bundle/PrivacyInfo.xcprivacy ... 
1209 bytes for ./Payload/maptoDev.app/ReactNativeMapsPrivacy.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/ReactNativeMapsPrivacy.bundle/Info.plist ... 
2025-02-05 05:13:05 +0000  795 bytes for ./Payload/maptoDev.app/ReactNativeMapsPrivacy.bundle/Info.plist
copying file ./Payload/maptoDev.app/GoogleService-Info-Dev.plist ... 
2025-02-05 05:13:05 +0000  479 bytes for ./Payload/maptoDev.app/GoogleService-Info-Dev.plist
copying file ./Payload/maptoDev.app/Geist-Thin.woff2 ... 
2025-02-05 05:13:05 +0000  38336 bytes for ./Payload/maptoDev.app/Geist-Thin.woff2
2025-02-05 05:13:05 +0000  copying file ./Payload/maptoDev.app/Frameworks/hermes.framework/_CodeSignature/CodeResources ... 
2025-02-05 05:13:05 +0000  1798 bytes for ./Payload/maptoDev.app/Frameworks/hermes.framework/_CodeSignature/CodeResources
copying file ./Payload/maptoDev.app/Frameworks/hermes.framework/hermes ... 
2025-02-05 05:13:06 +0000  4444032 bytes for ./Payload/maptoDev.app/Frameworks/hermes.framework/hermes
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/Frameworks/hermes.framework/Info.plist ... 
815 bytes for ./Payload/maptoDev.app/Frameworks/hermes.framework/Info.plist
copying file ./Payload/maptoDev.app/EBGaramond-ExtraBold.woff2 ... 
2025-02-05 05:13:06 +0000  192080 bytes for ./Payload/maptoDev.app/EBGaramond-ExtraBold.woff2
copying file ./Payload/maptoDev.app/Geist-Regular.woff2 ... 
2025-02-05 05:13:06 +0000  40016 bytes for ./Payload/maptoDev.app/Geist-Regular.woff2
copying file ./Payload/maptoDev.app/embedded.mobileprovision ... 
2025-02-05 05:13:06 +0000  21048 bytes for ./Payload/maptoDev.app/embedded.mobileprovision
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-05 05:13:06 +0000  761 bytes for ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
copying file ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL> ... 
2025-02-05 05:13:06 +0000  405 bytes for ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/<EMAIL>
copying file ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png ... 
290 bytes for ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon.png
copying file ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png ... 
2025-02-05 05:13:06 +0000  913 bytes for ./Payload/maptoDev.app/assets/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png
copying file ./Payload/maptoDev.app/assets/assets/images/info.png ... 
2025-02-05 05:13:06 +0000  552 bytes for ./Payload/maptoDev.app/assets/assets/images/info.png
copying file ./Payload/maptoDev.app/assets/assets/images/my-location.png ... 
529 bytes for ./Payload/maptoDev.app/assets/assets/images/my-location.png
copying file ./Payload/maptoDev.app/assets/assets/images/loader.png ... 
159 bytes for ./Payload/maptoDev.app/assets/assets/images/loader.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/assets/images/auto.png ... 
3155 bytes for ./Payload/maptoDev.app/assets/assets/images/auto.png
copying file ./Payload/maptoDev.app/assets/assets/images/activePickup.png ... 
197 bytes for ./Payload/maptoDev.app/assets/assets/images/activePickup.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/assets/images/mapPin.png ... 
2795 bytes for ./Payload/maptoDev.app/assets/assets/images/mapPin.png
copying file ./Payload/maptoDev.app/assets/assets/images/pickup.png ... 
2025-02-05 05:13:06 +0000  254 bytes for ./Payload/maptoDev.app/assets/assets/images/pickup.png
copying file ./Payload/maptoDev.app/assets/assets/images/diamondActive.png ... 
265 bytes for ./Payload/maptoDev.app/assets/assets/images/diamondActive.png
copying file ./Payload/maptoDev.app/assets/assets/images/Ellipse.png ... 
175 bytes for ./Payload/maptoDev.app/assets/assets/images/Ellipse.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/assets/images/suv.png ... 
3124 bytes for ./Payload/maptoDev.app/assets/assets/images/suv.png
copying file ./Payload/maptoDev.app/assets/assets/images/handler.png ... 
141 bytes for ./Payload/maptoDev.app/assets/assets/images/handler.png
copying file ./Payload/maptoDev.app/assets/assets/images/dot.png ... 
2025-02-05 05:13:06 +0000  168 bytes for ./Payload/maptoDev.app/assets/assets/images/dot.png
copying file ./Payload/maptoDev.app/assets/assets/images/hatch.png ... 
3189 bytes for ./Payload/maptoDev.app/assets/assets/images/hatch.png
copying file ./Payload/maptoDev.app/assets/assets/images/clear.png ... 
2025-02-05 05:13:06 +0000  477 bytes for ./Payload/maptoDev.app/assets/assets/images/clear.png
copying file ./Payload/maptoDev.app/assets/assets/images/dropMarker.png ... 
598 bytes for ./Payload/maptoDev.app/assets/assets/images/dropMarker.png
copying file ./Payload/maptoDev.app/assets/assets/images/divider.png ... 
2025-02-05 05:13:06 +0000  347 bytes for ./Payload/maptoDev.app/assets/assets/images/divider.png
copying file ./Payload/maptoDev.app/assets/assets/images/drag.png ... 
2025-02-05 05:13:06 +0000  12961 bytes for ./Payload/maptoDev.app/assets/assets/images/drag.png
copying file ./Payload/maptoDev.app/assets/assets/images/map.png ... 
2025-02-05 05:13:06 +0000  36199 bytes for ./Payload/maptoDev.app/assets/assets/images/map.png
copying file ./Payload/maptoDev.app/assets/assets/images/pickupSpots.png ... 
2025-02-05 05:13:06 +0000  969 bytes for ./Payload/maptoDev.app/assets/assets/images/pickupSpots.png
copying file ./Payload/maptoDev.app/assets/assets/images/chevron-down.png ... 
2025-02-05 05:13:06 +0000  336 bytes for ./Payload/maptoDev.app/assets/assets/images/chevron-down.png
copying file ./Payload/maptoDev.app/assets/assets/images/home.png ... 
491 bytes for ./Payload/maptoDev.app/assets/assets/images/home.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/assets/images/user.png ... 
2025-02-05 05:13:06 +0000  13642 bytes for ./Payload/maptoDev.app/assets/assets/images/user.png
copying file ./Payload/maptoDev.app/assets/assets/images/mapto.png ... 
2025-02-05 05:13:06 +0000  4214 bytes for ./Payload/maptoDev.app/assets/assets/images/mapto.png
copying file ./Payload/maptoDev.app/assets/assets/images/activeDrop.png ... 
2025-02-05 05:13:06 +0000  281 bytes for ./Payload/maptoDev.app/assets/assets/images/activeDrop.png
copying file ./Payload/maptoDev.app/assets/assets/images/verticalLine.png ... 
145 bytes for ./Payload/maptoDev.app/assets/assets/images/verticalLine.png
copying file ./Payload/maptoDev.app/assets/assets/images/background.png ... 
2025-02-05 05:13:06 +0000  193468 bytes for ./Payload/maptoDev.app/assets/assets/images/background.png
copying file ./Payload/maptoDev.app/assets/assets/images/drop.png ... 
2025-02-05 05:13:06 +0000  343 bytes for ./Payload/maptoDev.app/assets/assets/images/drop.png
copying file ./Payload/maptoDev.app/assets/assets/images/pickupMarker.png ... 
303 bytes for ./Payload/maptoDev.app/assets/assets/images/pickupMarker.png
copying file ./Payload/maptoDev.app/assets/assets/images/aboutImage.png ... 
2025-02-05 05:13:06 +0000  123970 bytes for ./Payload/maptoDev.app/assets/assets/images/aboutImage.png
copying file ./Payload/maptoDev.app/assets/assets/images/profile.png ... 
2025-02-05 05:13:06 +0000  730 bytes for ./Payload/maptoDev.app/assets/assets/images/profile.png
copying file ./Payload/maptoDev.app/assets/assets/images/diamondGrey.png ... 
271 bytes for ./Payload/maptoDev.app/assets/assets/images/diamondGrey.png
copying file ./Payload/maptoDev.app/assets/assets/images/location.png ... 
2025-02-05 05:13:06 +0000  580 bytes for ./Payload/maptoDev.app/assets/assets/images/location.png
copying file ./Payload/maptoDev.app/assets/assets/images/rides.png ... 
452 bytes for ./Payload/maptoDev.app/assets/assets/images/rides.png
copying file ./Payload/maptoDev.app/assets/assets/images/activePoints.png ... 
2025-02-05 05:13:06 +0000  17713 bytes for ./Payload/maptoDev.app/assets/assets/images/activePoints.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/assets/assets/images/diamond.png ... 
318 bytes for ./Payload/maptoDev.app/assets/assets/images/diamond.png
copying file ./Payload/maptoDev.app/assets/assets/images/star.png ... 
2025-02-05 05:13:06 +0000  334 bytes for ./Payload/maptoDev.app/assets/assets/images/star.png
copying file ./Payload/maptoDev.app/assets/assets/images/line.png ... 
316 bytes for ./Payload/maptoDev.app/assets/assets/images/line.png
copying file ./Payload/maptoDev.app/assets/assets/images/more.png ... 
2025-02-05 05:13:06 +0000  173 bytes for ./Payload/maptoDev.app/assets/assets/images/more.png
copying file ./Payload/maptoDev.app/assets/assets/images/fav.png ... 
633 bytes for ./Payload/maptoDev.app/assets/assets/images/fav.png
copying file ./Payload/maptoDev.app/assets/assets/images/setLocation.png ... 
2025-02-05 05:13:06 +0000  330 bytes for ./Payload/maptoDev.app/assets/assets/images/setLocation.png
copying file ./Payload/maptoDev.app/assets/assets/images/suvLuxe.png ... 
3561 bytes for ./Payload/maptoDev.app/assets/assets/images/suvLuxe.png
copying file ./Payload/maptoDev.app/assets/assets/images/back.png ... 
2025-02-05 05:13:06 +0000  288 bytes for ./Payload/maptoDev.app/assets/assets/images/back.png
copying file ./Payload/maptoDev.app/assets/assets/images/start.png ... 
2025-02-05 05:13:06 +0000  114799 bytes for ./Payload/maptoDev.app/assets/assets/images/start.png
copying file ./Payload/maptoDev.app/assets/assets/images/bg.png ... 
2025-02-05 05:13:06 +0000  296564 bytes for ./Payload/maptoDev.app/assets/assets/images/bg.png
copying file ./Payload/maptoDev.app/assets/assets/images/close.png ... 
2025-02-05 05:13:06 +0000  283 bytes for ./Payload/maptoDev.app/assets/assets/images/close.png
copying file ./Payload/maptoDev.app/assets/src/icons/LOADER.gif ... 
2025-02-05 05:13:06 +0000  111086 bytes for ./Payload/maptoDev.app/assets/src/icons/LOADER.gif
copying file ./Payload/maptoDev.app/assets/src/icons/mapto.gif ... 
2025-02-05 05:13:06 +0000  273445 bytes for ./Payload/maptoDev.app/assets/src/icons/mapto.gif
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/EBGaramond-ExtraBoldItalic.woff2 ... 
2025-02-05 05:13:06 +0000  193908 bytes for ./Payload/maptoDev.app/EBGaramond-ExtraBoldItalic.woff2
copying file ./Payload/maptoDev.app/Geist-SemiBold.woff2 ... 
2025-02-05 05:13:06 +0000  41952 bytes for ./Payload/maptoDev.app/Geist-SemiBold.woff2
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2386 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png ... 
2025-02-05 05:13:06 +0000  451 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_left.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
1055 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  2374 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png ... 
236 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/ic_error.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1051 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4308 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/de.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4436 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/he.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings ... 
4078 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_AU.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  943 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings ... 
5230 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ar.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings ... 
4694 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/el.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  3880 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ja.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png ... 
602 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_night_32pt.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4410 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sq.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
663 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings ... 
4061 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png ... 
2025-02-05 05:13:06 +0000  422 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_border_waypoint_alert_32pt.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4963 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uk.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png ... 
362 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_qu_direction_mylocation.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4376 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_419.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png ... 
2025-02-05 05:13:06 +0000  13906 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-1x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  2446 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
3304 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings ... 
4376 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es_MX.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png ... 
1114 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_large.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  3871 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_CN.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1240 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf ... 
2025-02-05 05:13:06 +0000  392092 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/DroidSansMerged-Regular.ttf
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings ... 
4211 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nb.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4377 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/es.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4171 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sw.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1203 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings ... 
4388 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_BR.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings ... 
4321 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/da.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1577 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4115 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/it.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4441 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sk.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png ... 
2025-02-05 05:13:06 +0000  20924 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-3x.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings ... 
4389 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt_PT.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png ... 
2025-02-05 05:13:06 +0000  720 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass_night.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png ... 
492 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_my_location.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png ... 
2025-02-05 05:13:06 +0000  127 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_16-4.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4754 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sr.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings ... 
4033 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ms.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png ... 
1934 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_256-64.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png ... 
2025-02-05 05:13:06 +0000  631 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_compass.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4451 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/sv.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png ... 
93 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_8-2.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png ... 
494 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_32pt.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings ... 
4667 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/cs.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png ... 
2025-02-05 05:13:06 +0000  334 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/button_background.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png ... 
2025-02-05 05:13:06 +0000  3393 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-1x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1229 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings ... 
4078 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_IN.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  3904 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ko.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png ... 
2025-02-05 05:13:06 +0000  13321 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-2x.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings ... 
4312 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hy.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
860 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4524 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/my.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1373 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png ... 
2025-02-05 05:13:06 +0000  5684 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavSprites-0-1x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf ... 
2025-02-05 05:13:06 +0000  353228 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Tharlon-Regular.ttf
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4195 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hu.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  3832 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_HK.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png ... 
2025-02-05 05:13:06 +0000  74 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_4-1.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4322 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ka.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  433 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png ... 
2025-02-05 05:13:06 +0000  742 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle_32pt.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  992 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car ... 
2025-02-05 05:13:06 +0000  48575 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Assets.car
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4175 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/tr.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings ... 
4491 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pl.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings ... 
3857 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/zh_TW.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1643 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png ... 
2025-02-05 05:13:06 +0000  7660 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-2x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png ... 
2025-02-05 05:13:06 +0000  12519 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSNavNightModeSprites-0-3x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1210 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings ... 
4078 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/en_GB.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png ... 
315 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/dav_one_way_16_256.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png ... 
84 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_2-1.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4232 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/vi.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
515 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png ... 
2025-02-05 05:13:06 +0000  223 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_32-8.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4617 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lv.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4321 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/lt.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4771 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ru.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib ... 
2025-02-05 05:13:06 +0000  305549 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShaders.metallib
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png ... 
7485 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture_dim.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1826 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png ... 
2025-02-05 05:13:06 +0000  435 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_closed_place_waypoint_alert_night_32pt.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4335 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr_CA.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1841 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1360 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png ... 
1029 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_128-32.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings ... 
4071 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/uz.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png ... 
2025-02-05 05:13:06 +0000  441 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_32pt.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4633 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fr.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4286 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/fi.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  3954 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/id.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4144 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/nl.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4157 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/th.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1066 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4228 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/az.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png ... 
425 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_64-16.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png ... 
2025-02-05 05:13:06 +0000  61437 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-3x.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png ... 
7433 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/polyline_colors_texture.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png ... 
483 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_compass_needle.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4388 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/pt.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib ... 
2025-02-05 05:13:06 +0000  307182 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSShadersSim.metallib
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1012 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1069 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png ... 
2025-02-05 05:13:06 +0000  95 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/road_1-1.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png ... 
498 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_location_off.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1353 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1472 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings ... 
2025-02-05 05:13:06 +0000  4491 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ro.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist ... 
2025-02-05 05:13:06 +0000  359 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/Info.plist
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png ... 
2025-02-05 05:13:06 +0000  40485 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/GMSSprites-0-2x.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png ... 
2025-02-05 05:13:06 +0000  382 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_medical_waypoint_alert_night_32pt.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png ... 
601 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ic_covid_checkpoint_waypoint_alert_32pt.png
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
825 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  2221 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2560 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings ... 
4264 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hr.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings ... 
4242 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/hi.lproj/GMSCore.strings
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings ... 
4274 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/ca.lproj/GMSCore.strings
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  1962 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL> ... 
1380 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCoreResources.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
2025-02-05 05:13:06 +0000  743 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz ... 
2025-02-05 05:13:06 +0000  173531 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/oss_licenses_maps.txt.gz
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car ... 
2025-02-05 05:13:06 +0000  43967 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Assets.car
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png ... 
2025-02-05 05:13:06 +0000  455 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/bubble_right.png
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL> ... 
454 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/<EMAIL>
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom ... 
4696 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/Storage.mom
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist ... 
1197 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/VersionInfo.plist
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom ... 
2025-02-05 05:13:06 +0000  5292 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileProto.mom
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom ... 
2025-02-05 05:13:06 +0000  4802 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/GMSCacheStorage.momd/StorageWithTileVersionID.mom
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist ... 
363 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/GoogleMaps.bundle/Info.plist
copying file ./Payload/maptoDev.app/GoogleMapsResources.bundle/Info.plist ... 
2025-02-05 05:13:06 +0000  788 bytes for ./Payload/maptoDev.app/GoogleMapsResources.bundle/Info.plist
copying file ./Payload/maptoDev.app/Info.plist ... 
1638 bytes for ./Payload/maptoDev.app/Info.plist
copying file ./Payload/maptoDev.app/EBGaramond-Italic.woff2 ... 
2025-02-05 05:13:06 +0000  188220 bytes for ./Payload/maptoDev.app/EBGaramond-Italic.woff2
copying file ./Payload/maptoDev.app/EBGaramond-ExtraBoldItalic.ttf ... 
2025-02-05 05:13:06 +0000  495860 bytes for ./Payload/maptoDev.app/EBGaramond-ExtraBoldItalic.ttf
copying file ./Payload/maptoDev.app/EBGaramond-ExtraBold.ttf ... 
2025-02-05 05:13:06 +0000  563204 bytes for ./Payload/maptoDev.app/EBGaramond-ExtraBold.ttf
2025-02-05 05:13:06 +0000  copying file ./Payload/maptoDev.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy ... 
512 bytes for ./Payload/maptoDev.app/RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy
copying file ./Payload/maptoDev.app/RNCAsyncStorage_resources.bundle/Info.plist ... 
800 bytes for ./Payload/maptoDev.app/RNCAsyncStorage_resources.bundle/Info.plist
copying file ./Payload/maptoDev.app/PkgInfo ... 
2025-02-05 05:13:06 +0000  8 bytes for ./Payload/maptoDev.app/PkgInfo
copying file ./Payload/maptoDev.app/EBGaramond-Medium.ttf ... 
2025-02-05 05:13:06 +0000  563752 bytes for ./Payload/maptoDev.app/EBGaramond-Medium.ttf
copying file ./Payload/maptoDev.app/Geist-Bold.woff2 ... 
2025-02-05 05:13:06 +0000  41640 bytes for ./Payload/maptoDev.app/Geist-Bold.woff2
copying file ./Payload/maptoDev.app/GeistVariableVF.woff2 ... 
2025-02-05 05:13:06 +0000  53444 bytes for ./Payload/maptoDev.app/GeistVariableVF.woff2
2025-02-05 05:13:06 +0000  /usr/bin/ditto exited with 0
2025-02-05 05:13:06 +0000  Processing step: IDEDistributionAppStoreInformationStep
2025-02-05 05:13:06 +0000  Skipping step: IDEDistributionAppStoreInformationStep because it said so
2025-02-05 05:13:06 +0000  Processing step: IDEDistributionGenerateProcessedDistributionItems
2025-02-05 05:13:06 +0000  IDEDistributionItem init <DVTFilePath:0x6000148e2bc0:'/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app/Frameworks/hermes.framework'>
2025-02-05 05:13:06 +0000  IDEDistributionItem init <DVTFilePath:0x6000145eb360:'/var/folders/_f/5hysy7p57kzf26ql1ytbm9b40000gn/T/XcodeDistPipeline.~~~GS2KLB/Root/Payload/maptoDev.app'>
2025-02-05 05:13:06 +0000  Processing step: IDEDistributionCreateManifestStep
