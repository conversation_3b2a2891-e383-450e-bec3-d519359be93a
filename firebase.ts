import messaging from '@react-native-firebase/messaging';
import {STATUS_CODE} from './src/constants/constants';
import AuthService from './src/services/AuthService';

export const getFcmToken = async () => {
  try {
    const fcmToken = await messaging().getToken();
    if (fcmToken) {
      await sendTokenToBackend(fcmToken);
    } else {
      console.log('Failed to get FCM token');
    }
  } catch (error) {
    console.error('Error getting FCM token:', error);
  }
};

const sendTokenToBackend = async (token: string) => {
  try {
    const response = await AuthService.updateFcmToken(token);
    if (response.status === STATUS_CODE.created) {
    }
  } catch (err: any) {
    const status = err?.response?.status;
    const message = err?.response?.data?.message;
    if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
      return;
    }
  }
};

// Listen for token refresh
export const setupTokenRefreshListener = () => {
  messaging().onTokenRefresh(async newToken => {
    console.log('FCM Token refreshed:', newToken);
    await sendTokenToBackend(newToken);
  });
};

export const initializeFcm = async () => {
  await getFcmToken();
  setupTokenRefreshListener();
};
