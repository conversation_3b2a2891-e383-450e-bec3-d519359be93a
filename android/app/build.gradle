apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

project.ext.envConfigFiles = [
    productiondebug: ".env.production",
    productionrelease: ".env.production",
    bundleproductionrelease: ".env.production",
    developmentrelease: ".env.development",
    developmentdebug: ".env.development",
    localdebug: ".env.local",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

import com.android.build.OutputFile

/**
 * This is the configuration block to customize your React Native Android app.
 * By default, you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    entryFile = file("../../index.ts")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.mapto"
    defaultConfig {
        applicationId "com.mapto"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
    }

       flavorDimensions "default"
    productFlavors {
        production {
            dimension "default"
            applicationId "com.mapto"
            versionCode 10
            versionName "1.0.10"
            resValue "string", "build_config_package", "com.mapto"
        }
        development {
            dimension "default"
            applicationId "com.mapto.development"
            versionCode 10
            versionName "1.0.10"
            resValue "string", "build_config_package", "com.mapto"
        }
        local {
            dimension "default"
            applicationId "com.mapto.development"
            versionCode 10
            versionName "1.0.10"
            resValue "string", "build_config_package", "com.mapto"
        }
    }

        signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        production {
            storeFile file("keystore/production.jks")
            storePassword "Mapto123Laan"
            keyAlias "mapto"
            keyPassword "Mapto123Laan"
        }
    }

  sourceSets {
        production {
            resources.srcDirs = ['src/google-services/production']
        }
        development {
            resources.srcDirs = ['src/google-services/development']
        }
        local {
            resources.srcDirs = ['src/google-services/development']
        }
    }

   applicationVariants.all { variant ->
    def flavor = variant.flavorName
    variant.preBuild.doFirst {
        def sourceFile = file("src/google-services/${flavor}/google-services.json")
        def targetFile = file("${projectDir}/google-services.json")

        if (sourceFile.exists()) {
            copy {
                from sourceFile
                into projectDir
            }
        } else {
            throw new GradleException("Missing google-services.json for flavor: ${flavor}")
        }
    }
}


    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.production
            minifyEnabled false
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation "com.google.android.gms:play-services-base:18.5.0"
    implementation('com.google.android.gms:play-services-location:21.3.0')
    implementation("com.facebook.react:react-android")
    implementation project(':react-native-linear-gradient')
    implementation 'com.google.firebase:firebase-messaging'
    implementation(platform("com.google.firebase:firebase-bom:33.1.2"))
    implementation("com.google.firebase:firebase-analytics")
    implementation 'com.facebook.fresco:animated-base-support:1.3.0'

    // For animated GIF support
    implementation 'com.facebook.fresco:animated-gif:3.1.3'

    // For WebP support, including animated WebP
    implementation 'com.facebook.fresco:animated-webp:3.1.3'
    implementation 'com.facebook.fresco:webpsupport:3.1.3'

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// Load native modules
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesAppBuildGradle(project)
