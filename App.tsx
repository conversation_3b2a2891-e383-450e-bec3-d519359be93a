import React, {useEffect, useState} from 'react';
import AppRouter from './src/router/AppRouter';
import {ToastProvider} from './src/components/Toast/Toast';
import {UserLocationProvider} from './src/utils/userLocationContext';
import {LocationProvider} from './src/utils/LocationContext';
import {StatusBar} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {I18nextProvider} from 'react-i18next';
import i18n from './src/i18n/i18n';
import {UserProvider} from './src/hooks/useUser';
import {LoaderProvider} from './src/hooks/useLoader';
import {AuthProvider} from './src/hooks/useAuth';
import {GeofenceProvider} from './src/hooks/useGeofence';
import {RideDetailsProvider} from './src/hooks/useRideDetails';
import {NavigationContainer} from '@react-navigation/native';
import {navigationRef} from './src/router/navigationService';
import {Text} from 'react-native';

function App(): JSX.Element {
  if (Text.defaultProps == null) {
    Text.defaultProps = {};
  }
  Text.defaultProps.allowFontScaling = false;
  
  return (
    <I18nextProvider i18n={i18n}>
      <GestureHandlerRootView style={{flex: 1}}>
        <StatusBar translucent={true} backgroundColor="transparent" />
        <LoaderProvider>
          <ToastProvider>
            <UserProvider>
              <UserLocationProvider>
                <LocationProvider>
                  <AuthProvider>
                    <NavigationContainer
                      theme={{colors: {background: '#000'}}}
                      ref={navigationRef}>
                      <RideDetailsProvider>
                        <GeofenceProvider>
                          <AppRouter />
                        </GeofenceProvider>
                      </RideDetailsProvider>
                    </NavigationContainer>
                  </AuthProvider>
                </LocationProvider>
              </UserLocationProvider>
            </UserProvider>
          </ToastProvider>
        </LoaderProvider>
      </GestureHandlerRootView>
    </I18nextProvider>
  );
}

export default App;
