import { useState, useEffect } from 'react';

type Location = {
  latitude: number;
  longitude: number;
  address?: string;
};

const useIPLocation = () => {
  const [location, setLocation] = useState<Location | null>(null);

  const getFreeLocation = async () => {
    try {
      const response = await fetch('https://get.geojs.io/v1/ip/geo.json');
      const data = await response.json();

      if (data.latitude && data.longitude) {
        setLocation({
          latitude: parseFloat(data.latitude),
          longitude: parseFloat(data.longitude),
          address: `${data.city}, ${data.region}, ${data.country}`,
        });
      }
    } catch (error) {
      console.error('Location fetch error:', error);
    }
  };

  useEffect(() => {
    getFreeLocation();
  }, []);

  return location;
};

export default useIPLocation;
