import {useEffect, useRef} from 'react';
import {socket} from '../socket/socket';
import { useNavigation } from '@react-navigation/native';

const useSocket = (
  event: string,
  handler: (data: any) => void,
  dependencies: any[],
) => {
  const navigationState = useNavigation();
  const isMounted = useRef(true);

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (isMounted.current) {
      socket.on(event, handler);
      return () => {
        socket.off(event, handler);
      };
    }
  }, [navigationState, ...dependencies]);
};

export default useSocket;
