import React, {createContext, useContext, useState, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {initializeFcm} from '../../firebase.ts';
import TokenService from '../services/TokenService';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  checkAuthentication: () => Promise<boolean>;
  refreshTokenIfNeeded: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const initAuth = async () => {
      const authenticated = await checkAuthentication();
      if (authenticated) {
        await TokenService.initTokenManagement();
      }
    };
    
    initAuth();

    return () => {
      TokenService.cleanupTokenManagement();
    };
  }, []);

  const refreshTokenIfNeeded = async (): Promise<boolean> => {
    try {
      const refreshNeeded = await TokenService.checkTokenRefreshNeeded();
      if (refreshNeeded) {
        return await TokenService.refreshTokens();
      }
      return true;
    } catch (error) {
      console.error('Error in refreshTokenIfNeeded:', error);
      return false;
    }
  };

  const checkAuthentication = async () => {
    setIsLoading(true);
    try {
      const accessToken = await AsyncStorage.getItem('accessToken');
      
      if (accessToken) {
        const refreshNeeded = await TokenService.checkTokenRefreshNeeded();
        
        if (refreshNeeded) {
          const refreshed = await TokenService.refreshTokens();
          setIsAuthenticated(refreshed);
          
          if (refreshed) {
            await initializeFcm();
          }
          
          return refreshed;
        }
        
        setIsAuthenticated(true);
        await initializeFcm();
        return true;
      }
      
      setIsAuthenticated(false);
      return false;
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        setIsAuthenticated,
        setIsLoading,
        checkAuthentication,
        refreshTokenIfNeeded,
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
