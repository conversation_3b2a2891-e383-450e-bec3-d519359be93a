import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
} from 'react';
import {Platform} from 'react-native';
import AuthService from '../services/AuthService';
import {User} from '../types/user';
import DeviceInfo, {getBuildNumber, getVersion} from 'react-native-device-info';
import {t} from 'i18next';
import {STATUS_CODE} from '../constants/constants';
import {navigationRef} from '../router/navigationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface UserContextType {
  id: string;
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  fetchUser: () => Promise<User>;
  isVerified: boolean;
  setIsVerified: Dispatch<SetStateAction<boolean>>;
  forceUpdate: boolean;
  setForceUpdate: Dispatch<SetStateAction<boolean>>;
  unreadSupportMsg: boolean;
  setUnreadSupportMsg: Dispatch<SetStateAction<boolean>>;
  upi: string;
  setUpi: Dispatch<SetStateAction<string>>;
  systemPauseReason: string;
  isSystemPaused: boolean;
  showPickupRange: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [forceUpdate, setForceUpdate] = useState<boolean>(false);
  const [unreadSupportMsg, setUnreadSupportMsg] = useState(false);
  const [upi, setUpi] = useState<string>('');
  const [isSystemPaused, setIsSystemPaused] = useState<boolean>(false);
  const [systemPauseReason, setSystemPauseReason] = useState<string>('');

  const checkSuspensionStatus = async () => {
    try {
      const os = Platform.OS.toString();
      const currentVersion = await getBuildNumber().toString();

      const response = await AuthService.getUser(os, currentVersion);
      if (response.status === STATUS_CODE.ok) {
        const userData = response.data?.data;
        setUser(userData);
        setForceUpdate(userData.forceUpdate);
        console.log('user', userData);
        if (userData.id) {
          await AsyncStorage.setItem('userId', userData.id.toString());
        }
        if (userData.status && userData.status.status === 'SystemPause') {
          setIsSystemPaused(true);
          setSystemPauseReason(userData.status.message);
        } else {
          setIsSystemPaused(false);
          setSystemPauseReason('');
        }
        return userData;
      }
    } catch (err: any) {
      console.log(err.response?.status);
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        navigationRef.current?.reset('Fallback');
        return false;
      }
    }
    return false;
  };

  const fetchUser = async () => {
    try {
      const os = Platform.OS.toString();
      const currentVersion = getBuildNumber().toString();
      const appVersion = DeviceInfo.getVersion();
      const buildNumber = DeviceInfo.getBuildNumber();
      console.log(
        `OS: ${os}, App Version: ${appVersion}, Build Number: ${buildNumber}`,
      );
      const response = await AuthService.getUser(os, currentVersion);
      const userData = response.data?.data;
      console.log(userData, 'fetcihng user');

      if (userData) {
        setUser(userData);
        setForceUpdate(userData.forceUpdate);
        return userData;
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        fetchUser: checkSuspensionStatus,
        isVerified,
        setIsVerified,
        forceUpdate,
        setForceUpdate,
        unreadSupportMsg,
        setUnreadSupportMsg,
        upi,
        setUpi,
        isSystemPaused,
        systemPauseReason,
      }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useDriver must be used within a DriverProvider');
  }
  return context;
};
