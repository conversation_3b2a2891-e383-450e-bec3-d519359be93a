import React, {createContext, useContext, useState, useRef} from 'react';
import {useToast} from '../components/Toast/Toast';
import {useAuth} from './useAuth';
import Geolocation from 'react-native-geolocation-service';
import GeofenceService from '../services/GeofenceService';
import {STATUS_CODE} from '../constants/constants';
import {checkForegorundLocationPermission} from '../constants/permissions';
import {useTranslation} from 'react-i18next';
import {Platform} from 'react-native';

interface GeofenceContextType {
  showGeofenceModal: boolean;
  setShowGeofenceModal: (value: boolean) => void;
  startGeofencing: () => void;
  stopGeofencing: () => void;
  checkCurrentLocation: () => void;
}

const GeofenceContext = createContext<GeofenceContextType | undefined>(
  undefined,
);

export const GeofenceProvider: React.FC<React.PropsWithChildren<{}>> = ({
  children,
}) => {
  const {t} = useTranslation();
  const [showGeofenceModal, setShowGeofenceModal] = useState(false);
  const {showToast} = useToast();
  const {isAuthenticated} = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        const {latitude, longitude} = position.coords;
        if (latitude && longitude) {
          if (isAuthenticated) {
            checkGeofence({latitude, longitude});
          }
        }
      },
      (error: {message: string}) => {
        console.log('Geolocation error:', error.message);
      },
      {
        timeout: 15000,
        maximumAge: 10000,
        enableHighAccuracy: false,
      },
    );
  };

  const checkGeofence = async (currentLocation: {
    latitude: number;
    longitude: number;
  }) => {
    if (!isAuthenticated) return;

    try {
      const {data} = await GeofenceService.checkGeofence(currentLocation);
      const isInside = data.data.isInside;

      if (!isInside !== showGeofenceModal) {
        setShowGeofenceModal(!isInside);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      }
    }
  };

  const startGeofencing = async () => {
    stopGeofencing();
    const locationPermissionGranted = await checkForegorundLocationPermission();

    if (isAuthenticated && locationPermissionGranted) {
      fetchCurrentLocation();
      const intervalTime = Platform.OS === 'ios' ? 30000 : 20000;

      intervalRef.current = setInterval(() => {
        fetchCurrentLocation();
      }, intervalTime);
    } else {
      if (!locationPermissionGranted) {
        const permissionResult = await checkForegorundLocationPermission();

        if (permissionResult && isAuthenticated) {
          fetchCurrentLocation();
          const intervalTime = Platform.OS === 'ios' ? 30000 : 20000;
          intervalRef.current = setInterval(() => {
            fetchCurrentLocation();
          }, intervalTime);
        }
      }
    }
  };

  const stopGeofencing = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };
  const checkCurrentLocation = () => {
    fetchCurrentLocation();
  };

  return (
    <>
      <GeofenceContext.Provider
        value={{
          showGeofenceModal,
          setShowGeofenceModal,
          startGeofencing,
          stopGeofencing,
          checkCurrentLocation,
        }}>
        {children}
      </GeofenceContext.Provider>
    </>
  );
};

export const useGeofence = () => {
  const context = useContext(GeofenceContext);
  if (!context)
    throw new Error('useGeofence must be used within a GeofenceProvider');
  return context;
};
