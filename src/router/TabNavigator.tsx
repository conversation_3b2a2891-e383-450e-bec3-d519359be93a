import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Maps from '../screens/Maps';
import MyRide from '../screens/MyRide';
import MyProfile from '../screens/MyProfile';
import home from '../icons/home.svg';
import homeActive from '../icons/home_active.svg';
import ride from '../icons/rides.svg';
import rideActive from '../icons/ride_active.svg';
import profile from '../icons/profile.svg';
import profileActive from '../icons/profile_active.svg';
import IconSvgView from '../components/IconSvgView/IconSvgView';
import {Platform, Text, View} from 'react-native';
import {colors, sizes} from '../constants';
import {spacing} from '../constants/theme';
import {useTranslation} from 'react-i18next';

const Tab = createBottomTabNavigator();

function BottomTab() {
  const {t} = useTranslation();

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={({route}) => ({
        tabBarIcon: ({color, focused}) => {
          let icon;
          if (route.name === t('home')) {
            icon = (
              <IconSvgView
                source={focused ? homeActive : home}
                color={color}
                size={30}
              />
            );
          } else if (route.name === t('my_rides')) {
            icon = (
              <IconSvgView
                source={focused ? rideActive : ride}
                color={color}
                size={25}
              />
            );
          } else if (route.name === t('profile')) {
            icon = (
              <IconSvgView
                source={focused ? profileActive : profile}
                color={color}
                size={25}
              />
            );
          }

          return <View style={{marginTop: spacing.md}}>{icon}</View>;
        },
        tabBarLabel: ({color}) => {
          const labelStyle = {
            fontSize: sizes.body,
            color: color,
            textAlign: 'center',
            padding: spacing.xs,
            paddingBottom: Platform.OS === 'ios' ? 4 : 2,
            fontFamily: 'Geist-Regular',
          };

          return (
            <Text numberOfLines={2} style={labelStyle}>
              {route.name}
            </Text>
          );
        },
        tabBarActiveTintColor: colors.white,
        tabBarInactiveTintColor: colors.grey,
        tabBarStyle: {
          backgroundColor: colors.darkGrey,
          borderTopColor: colors.davyGrey,
          height: Platform.OS === 'ios' ? 85 : 90,
          paddingBottom: Platform.OS === 'ios' ? 20 : 10,
          paddingTop: 8,
        },
        headerShown: false,
      })}>
      <Tab.Screen name={t('home')} component={Maps} />
      <Tab.Screen name={t('my_rides')} component={MyRide} />
      <Tab.Screen name={t('profile')} component={MyProfile} />
    </Tab.Navigator>
  );
}

export default BottomTab;
