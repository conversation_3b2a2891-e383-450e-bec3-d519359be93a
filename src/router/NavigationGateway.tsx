import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useAuth} from '../hooks/useAuth';
import {
  Image,
  ImageBackground,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';
import {images} from '../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {spacing} from '../constants/theme';
import {STATUS_CODE} from '../constants/constants';
import TripService from '../services/TripService';
import {useRideDetails} from '../hooks/useRideDetails';
import {useUser} from '../hooks/useUser';
import RideService from '../services/RideService';

interface NavigationGatewayProps {
  navigation?: any;
}

const NavigationGateway: React.FC<NavigationGatewayProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {isAuthenticated, checkAuthentication} = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const {setTripDetails, setDriverRoute} = useRideDetails();
  const {fetchUser, forceUpdate} = useUser();

const checkTripStatus = async () => {
  try {
    const response = await TripService.getActiveRide();

    if (response.status === STATUS_CODE.ok) {
      const activeRide = response.data.data.activeRide;

      if (!activeRide) {
        console.log('[NavigationGateway] No active ride, checking last ride');
        const lastRideResponse = await RideService.getLastRide();

        if (lastRideResponse.status === STATUS_CODE.ok) {
          const lastRide = lastRideResponse.data.data.lastTrip;

          if (lastRide?.status === 'completed') {
            const updatedAt = lastRide.updated_at;
            const updatedTime = new Date(updatedAt).getTime();
            const currentTime = Date.now();
            const elapsedTime = currentTime - updatedTime;
            const TEN_MINUTES = 10 * 60 * 1000;

            if (elapsedTime >= TEN_MINUTES) {
              console.log('[NavigationGateway] Last ride completed over 10 mins ago. Navigating to BottomTab');
              await AsyncStorage.multiRemove(['tripId', 'rideStatus']);
              return null;
            }

            console.log('[NavigationGateway] Last ride needs payment, navigating to CollectCash');
            await AsyncStorage.setItem('tripId', lastRide.id.toString());
            setTripDetails(lastRide);
            navigation.reset({
              index: 0,
              routes: [{name: 'CollectCash'}],
            });
            return lastRide.status;
          }
        }

        console.log('[NavigationGateway] No pending rides found');
        await AsyncStorage.multiRemove(['tripId', 'rideStatus']);
        return null;
      }

      // Handle active ride
      await AsyncStorage.setItem('tripId', activeRide.id.toString());
      setTripDetails(activeRide);
      const navigationRoutes = {
        accepted: 'RideDetails',
        aborted: 'BottomTab',
        completed: 'CollectCash',
        processing: 'Confirm',
        no_drivers_available: 'Direction',
        driver_cancelled: 'Direction',
        verified: 'RideRoute',
      };

      const route =
        navigationRoutes[activeRide.status as keyof typeof navigationRoutes];
      console.log('[NavigationGateway] Active ride status:', activeRide.status);

      if (route) {
        console.log('[NavigationGateway] Navigating to:', route);
        navigation.reset({
          index: 0,
          routes: [{name: route}],
        });
        return activeRide.status;
      }
    }

    return null;
  } catch (err: any) {
    if (err?.response?.data?.code === 'no_active_trip') {
      console.log('[NavigationGateway] No active trip found');
      await AsyncStorage.multiRemove(['tripId', 'rideStatus']);
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTab'}],
      });
    } else {
      console.error('Error checking trip status:', err);
    }
    return null;
  }
};


  const handleTripNavigation = async (
    tripStatus: string | null,
    userData: any,
  ) => {
    if (!tripStatus) {
      await handleUserSetupNavigation(userData);
    }
  };

  const handleUserSetupNavigation = async (userData: any) => {
    console.log('[NavigationGateway] Setup navigation for:', userData);
    const auth = await checkAuthentication();

    if (!auth) {
      navigation.reset({
        index: 0,
        routes: [{name: 'Start'}],
      });
      return;
    }

    if (userData?.name && userData?.gender) {
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTab'}],
      });
    } else {
      navigation.reset({
        index: 0,
        routes: [{name: 'UserSetup'}],
      });
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true);
        const isUserAuthenticated = await checkAuthentication();
        if (!isUserAuthenticated) {
          navigation.reset({index: 0, routes: [{name: 'Start'}]});
          return;
        }

        const user = await fetchUser();
        if (user) {
          const status = await checkTripStatus();
          await handleTripNavigation(status, user);
        }
      } catch (error) {
        navigation.reset({index: 0, routes: [{name: 'Start'}]});
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  return (
    <ImageBackground source={images.map} style={styles.background}>
      <SafeAreaView style={styles.safearea}>
        <View style={styles.loaderContainer}>
          <Image
            source={require('../icons/LOADER.gif')}
            style={styles.appGif}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  safearea: {
    flex: 1,
    margin: spacing.xl * 1.5,
  },
  appGif: {
    width: 50,
    height: 50,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NavigationGateway;
