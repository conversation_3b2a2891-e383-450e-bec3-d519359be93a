import React, {useState, useEffect} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {
  View,
  Text,
  StyleSheet,
  AppState,
  Platform,
  Linking,
  StatusBar,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import {useTranslation} from 'react-i18next';
import {useLoader} from '../hooks/useLoader';
import {useRideDetails} from '../hooks/useRideDetails';
import {colors, GeistFont, sizes} from '../constants';
import {appRoutes} from './router';
import {navigationRef} from './navigationService';
import OfflineScreen from '../screens/Offline';
import FallBack from '../screens/FallBack';
import {MAX_RETRIES, RETRY_DELAY} from '../constants/constants';
import {spacing} from '../constants/theme';
import {useUser} from '../hooks/useUser';
import BottomUpModal from '../components/BottomUpModal/BottomUpModal';
import notifee, {
  AndroidImportance,
  AndroidStyle,
  AuthorizationStatus,
} from '@notifee/react-native';
import {useToast} from '../components/Toast/Toast';

import {
  setupiOSCategories,
  displayiOSNotification,
} from '../utils/iosNotifications';

import GlobalTripPollingService from '../services/GlobalTripPollingService';

const Stack = createStackNavigator();

const AppRouter = () => {
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {t} = useTranslation();
  const {
    handleNotification,
    fetchTripDetails,
    tripStatus,
    tripId,
  } = useRideDetails();
  const {forceUpdate, setForceUpdate, isSystemPaused, fetchUser} = useUser();
  const [isConnected, setIsConnected] = useState(true);
  const [onlineStatus, setOnlineStatus] = useState('');
  const [wasOffline, setWasOffline] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [initialRoute] = useState<string>('SplashScreen');

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('transparent');
    StatusBar.setTranslucent(true);
  }, []);

  const shouldShowForegroundNotification = (eventType: string | object) => {
    const rewardNotifications = [
      'ReferralRewardProcessed',
      'TripleTreatRewardProcessed',
      'WelcomeRewardProcessed',
    ];

    return (
      eventType === 'DRIVER_MESSAGE' ||
      eventType === 'driver-message' ||
      rewardNotifications.includes(eventType as string)
    );
  };

  // Function to check if notification should be visible in background
  const shouldShowBackgroundNotification = (eventType: string | object) => {
    const visibleBackgroundTypes = [
      'RideAccepted',
      'RideCanceledDriver',
      'RideVerified',
      'RideCompleted',
      'DRIVER_MESSAGE',
      'driver-message',
      'chat',
      'RideDriverArrived',
      'RideAborted',
      'RideDriverHalted',
      'ReferralRewardProcessed',
      'TripleTreatRewardProcessed',
      'WelcomeRewardProcessed',
      'RideNoDriversAvailable',
      'RideOtpVerify',
      'chat',
      'ride_messege',
    ];

    return (
      typeof eventType === 'string' &&
      visibleBackgroundTypes.includes(eventType)
    );
  };

  const handleUpdateClick = () => {
    const androidUrl =
      'https://play.google.com/store/apps/details?id=com.mapto.driver&pli=1';
    const iosUrl =
      'https://play.google.com/store/apps/details?id=com.mapto.driver&pli=1';
    const url = Platform.OS === 'android' ? androidUrl : iosUrl;
    Linking.openURL(url).catch(err => console.error('Error opening URL:', err));
  };

  const requestiOSPermissions = async () => {
    if (Platform.OS !== 'ios') return;

    try {
      // Request more comprehensive permissions for iOS
      const authStatus = await messaging().requestPermission({
        provisional: true,
        announcement: true,
        criticalAlert: true,
      });

      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      console.log('iOS Notification Auth Status:', authStatus);
      return enabled;
    } catch (error) {
      console.error('Error checking iOS permissions:', error);
      return null;
    }
  };

  const registerForBackgroundNotifications = async () => {
    if (Platform.OS !== 'ios') return;

    try {
      // Request permissions with provisional option
      const settings = await notifee.requestPermission({
        provisional: true,
        criticalAlert: true,
        announcement: true,
      });

      console.log('iOS notification permission settings:', settings);

      // Register for remote notifications
      await messaging().registerDeviceForRemoteMessages();

      console.log('iOS background notifications registered successfully');
    } catch (error) {
      console.error(
        'Error registering for iOS background notifications:',
        error,
      );
    }
  };

  const ensureString = (value: string | object | undefined): string => {
    if (typeof value === 'string') {
      return value;
    }
    return '';
  };

  const displayChatNotification = async (
    titleInput: string | object | undefined,
    bodyInput: string | object | undefined,
    isBackground: boolean = false,
  ) => {
    const title = ensureString(titleInput);
    const body = ensureString(bodyInput);
    if (!title || !body) return;
    console.log(
      `Displaying chat notification (${
        isBackground ? 'background' : 'foreground'
      }):`,
      title,
      body,
    );

    try {
      if (Platform.OS === 'android') {
        await notifee.requestPermission();
      } else if (Platform.OS === 'ios') {
        await requestiOSPermissions();
        await setupiOSCategories();
      }

      let channelId;
      if (Platform.OS === 'android') {
        channelId = await notifee.createChannel({
          id: 'chat-messages',
          name: 'Chat Messages',
          importance: AndroidImportance.HIGH,
          vibration: true,
          sound: 'default',
        });
      }

      // Enhanced iOS notification configuration
      const iosConfig =
        Platform.OS === 'ios'
          ? {
              critical: true,
              sound: 'default',
              categoryId: 'chat', // Must match one of your categories
              threadId: 'chat-messages',
              foregroundPresentationOptions: {
                badge: true,
                sound: true,
                banner: true,
                list: true,
              },
              // Force more prominent display for background notifications
              interruptionLevel: isBackground ? 'critical' : 'timeSensitive',
              attachments: [],
            }
          : undefined;

      await notifee.displayNotification({
        id: `driver-message-${Date.now()}`, // Use unique ID for each message
        title: title,
        body: body,
        data: {
          type: 'driver-message',
          isBackground: isBackground ? 'true' : 'false',
        },
        android:
          Platform.OS === 'android'
            ? {
                channelId,
                importance: AndroidImportance.HIGH,
                pressAction: {
                  id: 'open-chat',
                  launchActivity: 'default',
                },
                style: {
                  type: AndroidStyle.BIGTEXT,
                  text: ensureString(body),
                },
                smallIcon: 'ic_notification',
                color: '#4CAF50',
                actions: [
                  {
                    title: 'Reply',
                    pressAction: {
                      id: 'reply',
                    },
                  },
                  {
                    title: 'View',
                    pressAction: {
                      id: 'open-chat',
                    },
                  },
                ],
              }
            : undefined,
        ios: iosConfig,
      });
    } catch (error) {
      console.error('Error displaying notification:', error);
    }
  };

  const displayBackGroundNotification = async (
    titleInput: string | undefined,
    bodyInput?: string | undefined,
  ) => {
    if (Platform.OS === 'ios') {
      const safeTitle = ensureString(titleInput);
      const safeBody = ensureString(bodyInput);
      if (safeTitle) {
        await displayiOSNotification(safeTitle, safeBody);
      }
    }
  };

  const processForegroundNotification = async (
    remoteMessage: FirebaseMessagingTypes.RemoteMessage,
  ) => {
    if (Platform.OS === 'ios' && remoteMessage.notification) {
      await displayiOSNotification(
        remoteMessage.notification.title || '',
        remoteMessage.notification.body || '',
        remoteMessage.data || {},
      );
      return;
    }

    if (!remoteMessage.data) return;

    const eventType = remoteMessage.data.event || remoteMessage.data.type;

    handleNotification(remoteMessage);

    if (shouldShowForegroundNotification(eventType)) {
      console.log('Processing foreground notification, eventtype:', eventType);

      const messageContent =
        remoteMessage.notification?.body || remoteMessage.data.body;
      const messageTitle =
        remoteMessage.notification?.title ||
        remoteMessage.data.title ||
        'Driver Message';

      // Pass false or no parameter for foreground notification
      await displayChatNotification(messageTitle, messageContent, false);

      const messageId =
        remoteMessage.messageId?.toString() || Date.now().toString();
      await AsyncStorage.setItem(
        `messages-${messageId}`,
        JSON.stringify({
          content: messageContent,
          timestamp: Date.now(),
          id: messageId,
        }),
      );
    } else {
      if (remoteMessage.data.tripId) {
        await AsyncStorage.setItem('latestTripId', remoteMessage.data.tripId);
      }
    }
  };

  const handleRetrySystemPause = async () => {
    await fetchUser();
  };

  const processBackgroundNotification = async (
    remoteMessage: FirebaseMessagingTypes.RemoteMessage,
  ) => {
    if (!remoteMessage || !remoteMessage.data) return;
    const eventType = remoteMessage.data.event || remoteMessage.data.type;
    handleNotification(remoteMessage);
    console.log('Background message received:', remoteMessage, eventType);

    if (shouldShowBackgroundNotification(eventType)) {
      console.log('Processing background notification, eventtype:', eventType);

      if (eventType === 'ride_messege') {
        console.log('Processing chat message in background', eventType);
        const messageContent =
          remoteMessage.data.content ||
          remoteMessage.notification?.body ||
          remoteMessage.data.body;

        const messageTitle =
          remoteMessage.notification?.title ||
          remoteMessage.data.title ||
          'Driver';

        // Pass true to indicate background notification
        await displayChatNotification(messageTitle, messageContent, true);

        const messageId = remoteMessage.messageId || Date.now().toString();
        await AsyncStorage.setItem(
          `messages-${messageId}`,
          JSON.stringify({
            content: messageContent,
            timestamp: Date.now(),
            id: messageId,
          }),
        );
      } else {
        let title;
        let body;

        switch (eventType) {
          case 'RideAccepted':
            title = 'RideAccepted';
            break;
          case 'RideCanceledDriver':
            title = 'RideCanceledDriver';
            break;
          case 'RideCompleted':
            title = 'RideCompleted';
            break;
          case 'RideDriverArrived':
            title = 'RideDriverArrived';
            break;
          case 'RideOtpVerify':
            if (remoteMessage.data.is_verified === 'true') {
              title = 'RideOtpVerify';
              body = 'Your ride has been verified.';
            }
            break;
          case 'RideDriverHalted':
            title = 'RideDriverHalted';
            break;
          case 'ReferralRewardProcessed':
            title = 'Referral Reward';
            body = 'Your referral reward has been processed!';
            break;
          case 'TripleTreatRewardProcessed':
            title = 'Triple Treat Reward';
            body = 'Your triple treat reward has been processed!';
            break;
          case 'WelcomeRewardProcessed':
            title = 'Welcome Reward';
            body = 'Your welcome reward has been processed!';
            break;
          case 'RideNoDriversAvailable':
            title = 'RideNoDriversAvailable';
            break;
          default:
            title = `Ride Update: ${eventType}`;
            body = `Your ride status has been updated to ${eventType}.`;
        }

        await displayBackGroundNotification(title, body);
      }
    } else {
      if (remoteMessage.data.tripId) {
        await AsyncStorage.setItem('latestTripId', remoteMessage.data.tripId);
      }
    }
  };

  // Initialize global polling service for iOS
  useEffect(() => {
    if (Platform.OS === 'ios') {
      const globalPollingService = GlobalTripPollingService.getInstance();
      globalPollingService.checkAndStartPollingIfNeeded();
    }
  }, []);

  useEffect(() => {
    const unsubscribeNetInfo = NetInfo.addEventListener(state => {
      setIsConnected(!!state.isConnected);
      if (state.isConnected && wasOffline) {
        setOnlineStatus('You are back online!');
        setWasOffline(false);
        setTimeout(() => setOnlineStatus(''), 5000);
        navigationRef.current.reset({
          index: 0,
          routes: [{name: 'NavigationGateway'}],
        });
      } else if (!state.isConnected) {
        setOnlineStatus('');
        setWasOffline(true);
      }
    });
    return () => unsubscribeNetInfo();
  }, [wasOffline]);

  const handleRefresh = async () => {
    showLoader();
    setRetryCount(0);

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      const state = await NetInfo.fetch();
      if (state.isConnected) {
        setIsConnected(true);
        hideLoader();
        navigationRef.current?.reset({
          index: 0,
          routes: [{name: 'NavigationGateway'}],
        });
        return;
      }
      setRetryCount(attempt);
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }
    setIsConnected(false);
    hideLoader();
  };

  // Fetch tripdetails in quit state for android and ios
  useEffect(() => {
    fetchTripDetails();
  }, [tripId]);

  useEffect(() => {
    const setupNotifications = async () => {
      try {
        if (Platform.OS === 'ios') {
          await requestiOSPermissions();
          await setupiOSCategories();
          await registerForBackgroundNotifications();
          messaging().registerDeviceForRemoteMessages();
        }

        const authStatus = await messaging().requestPermission({
          provisional: true,
          announcement: true,
          criticalAlert: true,
        });

        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log('Firebase Messaging Authorization status:', authStatus);

          const fcmToken = await messaging().getToken();
          console.log('FCM Token:', fcmToken);

          await AsyncStorage.setItem('fcmToken', fcmToken);

          if (Platform.OS === 'ios') {
            await messaging().setAutoInitEnabled(true);

            await notifee.setForegroundServiceNotificationId(
              'driver-app-notification',
            );

            await notifee.setNotificationCategories([
              {
                id: 'rideStatus',
                actions: [
                  {
                    id: 'view',
                    title: 'View',
                  },
                ],
              },
              {
                id: 'chat',
                actions: [
                  {
                    id: 'reply',
                    title: 'Reply',
                    input: {
                      buttonText: 'Send',
                      placeholderText: 'Reply to message...',
                    },
                  },
                  {
                    id: 'view',
                    title: 'View',
                  },
                ],
              },
            ]);
          }
        }
      } catch (error) {
        console.error('Error setting up notifications:', error);
      }
    };

    setupNotifications();

    const handleAppStateChange = async (nextAppState: string) => {
      if (nextAppState === 'active') {
        AsyncStorage.getItem('pendingNavigation').then(destination => {
          if (destination) {
            switch (destination) {
              case 'chat':
                navigationRef.current?.navigate('ChatScreen');
                break;
              // case 'rideComplete':
              //   navigationRef.current?.navigate('CollectCash');
              //   break;
              default:
                // Other navigation destinations
                break;
            }
            AsyncStorage.removeItem('pendingNavigation');
          }
        });
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    const handleForegroundMessage = async (
      remoteMessage: FirebaseMessagingTypes.RemoteMessage,
    ) => {
      console.log('Foreground message received:', remoteMessage);

      await processForegroundNotification(remoteMessage);
    };

    const unsubscribeMessaging = messaging().onMessage(handleForegroundMessage);

    messaging().onNotificationOpenedApp(async remoteMessage => {
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage,
      );
      handleNotification(remoteMessage);

      const eventType = remoteMessage.data?.event || remoteMessage.data?.type;
      if (
        eventType === 'DRIVER_MESSAGE' ||
        eventType === 'driver-message' ||
        eventType === 'chat'
      ) {
        navigationRef.current?.navigate('ChatScreen');
      }
    });

    messaging()
      .getInitialNotification()
      .then(async remoteMessage => {
        if (remoteMessage) {
          console.log(
            'App opened from quit state by notification:',
            remoteMessage,
          );
          handleNotification(remoteMessage);

          const eventType =
            remoteMessage.data?.event || remoteMessage.data?.type;
          if (
            eventType === 'DRIVER_MESSAGE' ||
            eventType === 'driver-message' ||
            eventType === 'chat'
          ) {
            setTimeout(() => {
              navigationRef.current?.navigate('ChatScreen');
            }, 1000);
          }
        }
      });

    return () => unsubscribeMessaging();
  }, []);

  useEffect(() => {
    // Set up explicit background handler
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Background message received Approuter:', remoteMessage);
      try {
        await processBackgroundNotification(remoteMessage);
        return Promise.resolve();
      } catch (error) {
        console.error('Error in background handler:', error);
        return Promise.resolve();
      }
    });

    notifee.onBackgroundEvent(async ({type, detail}) => {
      try {
        const {notification, pressAction} = detail;

        if (type === 2) {
          if (pressAction?.id === 'open-chat') {
            await AsyncStorage.setItem('pendingNavigation', 'chat');
          } else if (notification?.data) {
            const notificationData = notification.data;
            if (notificationData.event === 'RideCompleted') {
              await AsyncStorage.setItem('pendingNavigation', 'rideComplete');
            }
          }
        }

        return Promise.resolve();
      } catch (error) {
        console.error('Error in notifee background handler:', error);
        return Promise.resolve();
      }
    });
  }, []);

  return (
    <>
      {initialRoute && (
        <Stack.Navigator initialRouteName={initialRoute}>
          {!isConnected ? (
            <Stack.Screen
              name="Offline"
              options={{headerShown: false}}
              children={() => (
                <OfflineScreen
                  handleRefresh={handleRefresh}
                  retryCount={retryCount}
                />
              )}
            />
          ) : (
            appRoutes.map(item => (
              <Stack.Screen
                key={item.name}
                name={item.name}
                component={item.component}
                options={{header: () => null, gestureEnabled: true}}
              />
            ))
          )}
          <Stack.Screen
            name="Fallback"
            options={{headerShown: false}}
            component={FallBack}
          />
        </Stack.Navigator>
      )}
      {onlineStatus && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>{onlineStatus}</Text>
        </View>
      )}
      {forceUpdate && (
        <BottomUpModal
          showModal={forceUpdate}
          onClose={() => setForceUpdate(false)}
          title={t('update_available')}
          description={t('please_update')}
          buttonText={t('update')}
          onButtonClick={() => {
            setForceUpdate(false);
            handleUpdateClick();
          }}
          forceUpdate={true}
        />
      )}
      {isSystemPaused && (
        <BottomUpModal
          showModal={isSystemPaused}
          onClose={() => {}}
          title={t('system_maintenance')}
          description={t('system_paused_message')}
          buttonText={t('retry')}
          onButtonClick={handleRetrySystemPause}
          forceUpdate={true}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  statusContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    padding: 10,
    backgroundColor: colors.green,
    height: 100,
  },
  statusText: {
    color: colors.white,
    textAlign: 'center',
    marginTop: spacing.xl * 3,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
});

export default AppRouter;
