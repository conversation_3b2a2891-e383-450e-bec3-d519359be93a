const car = require('../../assets/images/start.png');
const bg1 = require('../../assets/images/bg.png');
const bg2 = require('../../assets/images/background.png');
const diamond = require('../../assets/images/diamond.png');
const diamondActive = require('../../assets/images/diamondActive.png');
const diamondGrey = require('../../assets/images/diamondGrey.png');
const back = require('../../assets/images/back.png');
const info = require('../../assets/images/info.png');
const dropDown = require('../../assets/images/chevron-down.png');
const dot = require('../../assets/images/dot.png');
const home = require('../../assets/images/home.png');
const favourite = require('../../assets/images/fav.png');
const rides = require('../../assets/images/rides.png');
const profile = require('../../assets/images/profile.png');
const myLocation = require('../../assets/images/my-location.png');
const close = require('../../assets/images/close.png');
const clear = require('../../assets/images/clear.png');
const location = require('../../assets/images/location.png');
const divider = require('../../assets/images/divider.png');
const loader = require('../../assets/images/loader.png');
const pickup = require('../../assets/images/pickup.png');
const drop = require('../../assets/images/drop.png');
const more = require('../../assets/images/more.png');
const ellipse = require('../../assets/images/Ellipse.png');
const auto = require('../../assets/images/auto.png');
const drag = require('../../assets/images/drag.png');
const line = require('../../assets/images/line.png');
const handler = require('../../assets/images/handler.png');
const setLocation = require('../../assets/images/setLocation.png');
const mapPin = require('../../assets/images/mapPin.png');
const star = require('../../assets/images/star.png');
const activePickup = require('../../assets/images/activePickup.png');
const activeDrop = require('../../assets/images/activeDrop.png');
const pickupMarker = require('../../assets/images/pickupMarker.png');
const dropMarker = require('../../assets/images/dropMarker.png');
const verticalLine = require('../../assets/images/verticalLine.png');
const user = require('../../assets/images/user.png');
const map = require('../../assets/images/map.png');
const about = require('../../assets/images/aboutImage.png');
const pickupSpot = require('../../assets/images/pickupSpots.png');
const activePoints = require('../../assets/images/activePoints.png');
const mapto = require('../../assets/images/mapto.png');
const autoTop = require('../../assets/images/autoTop.png');

export default {
  autoTop,
  car,
  bg1,
  bg2,
  diamond,
  diamondActive,
  diamondGrey,
  back,
  info,
  dropDown,
  dot,
  home,
  favourite,
  rides,
  profile,
  myLocation,
  close,
  clear,
  location,
  divider,
  loader,
  more,
  ellipse,
  auto,
  drag,
  line,
  handler,
  setLocation,
  mapPin,
  star,
  pickup,
  drop,
  activePickup,
  activeDrop,
  pickupMarker,
  dropMarker,
  verticalLine,
  user,
  map,
  about,
  pickupSpot,
  activePoints,
  mapto,
};
