import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
  useRef,
} from 'react';
import MapView, {LatLng} from 'react-native-maps';

interface UserLocationContextValue {
  userLocation: LatLng | null;
  setUserLocation: Dispatch<SetStateAction<LatLng | null>>;
  mapViewRef: React.MutableRefObject<MapView | null>;
}

export const UserLocationContext = createContext<
  UserLocationContextValue | undefined
>(undefined);

export const UserLocationProvider: React.FC<{children: ReactNode}> = ({
  children,
}) => {
  const [userLocation, setUserLocation] = useState<LatLng | null>(null);
  const mapViewRef = useRef<MapView | null>(null);
  
  return (
    <UserLocationContext.Provider
      value={{
        userLocation,
        setUserLocation,
        mapViewRef,
      }}>
      {children}
    </UserLocationContext.Provider>
  );
};

export const userLocationContext = () => {
  const context = useContext(UserLocationContext);
  if (!context) {
    throw new Error(
      'useDeviceLocationContext must be used within a DeviceLocationProvider',
    );
  }
  return context;
};
