import Sound from 'react-native-sound';
import {Platform} from 'react-native';

Sound.setCategory('Playback', true);

let currentSound: Sound | null = null;
let soundTimer: NodeJS.Timeout | null = null;

const playSound = (fileName: string, label: string) => {
  stopAllSounds();

  const soundPath = fileName;
  const bundle = Platform.OS === 'ios' ? Sound.MAIN_BUNDLE : Sound.MAIN_BUNDLE;

  currentSound = new Sound(soundPath, bundle, error => {
    if (error) {
      console.log(`Failed to load ${label} sound`, error);
      return;
    }

    currentSound?.play(success => {
      if (!success) {
        console.log(`Playback of ${label} sound failed`);
      }
    });
  });
};

export const playDriverCanceled = () =>
  playSound('drivercanceled.mp3', 'cancel');
export const playDriverArrived = () =>
  playSound('driverarrived.mp3', 'arrived');
export const playNoDrivers = () => playSound('nodrivers.mp3', 'no-driver');
export const playRideAborted = () =>
  playSound('rideaborted.mp3', 'ride-aborted');
export const playRideAccepted = () =>
  playSound('rideaccepted.mp3', 'ride-accepted');
export const playRideVerified = () =>
  playSound('rideverified.mp3', 'ride-verified');
// export const playSafetyCheck = () =>
//   playSound('safetycheck.mp3', 'safety-check');
export const playRideCompleted = () =>
  playSound('ridecompleted.mp3', 'ride-completed');

export const stopAllSounds = () => {
  if (currentSound) {
    currentSound.stop();
    currentSound.release();
    currentSound = null;
  }

  if (soundTimer) {
    clearTimeout(soundTimer);
    soundTimer = null;
  }
};
