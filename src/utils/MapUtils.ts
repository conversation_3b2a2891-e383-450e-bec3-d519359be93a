import MapView, {LatLng} from 'react-native-maps';
import Config from 'react-native-config';

//Direction.tsx

export const decodePolyline = (encoded: string) => {
  const points: {latitude: number; longitude: number}[] = [];
  let index = 0,
    lat = 0,
    lng = 0;

  while (index < encoded.length) {
    let b,
      shift = 0,
      result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lat += dlat;

    shift = 0;
    result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lng += dlng;

    points.push({latitude: lat / 1e5, longitude: lng / 1e5});
  }

  return points;
};

export const zoomToRoute = (
  mapViewRef: React.RefObject<MapView>,
  coordinates: {latitude: number; longitude: number}[],
) => {
  if (coordinates.length > 0 && mapViewRef.current) {
    const minLat = Math.min(...coordinates.map(coord => coord.latitude));
    const maxLat = Math.max(...coordinates.map(coord => coord.latitude));
    const minLng = Math.min(...coordinates.map(coord => coord.longitude));
    const maxLng = Math.max(...coordinates.map(coord => coord.longitude));

    const padding = 0.01;
    const region = {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.max(maxLat - minLat + padding, 0.01),
      longitudeDelta: Math.max(maxLng - minLng + padding, 0.01),
    };
    mapViewRef.current.animateToRegion(region, 500);
  }
};

export const getDirections = async (origin: string, destination: string) => {
  try {
    const directionsApiUrl = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=${Config.GOOGLE_API}`;
    const response = await fetch(directionsApiUrl);
    const data = await response.json();
    if (
      data.routes &&
      data.routes.length > 0 &&
      data.routes[0].overview_polyline
    ) {
      const overviewPolyline = data.routes[0].overview_polyline.points;
      const points = decodePolyline(overviewPolyline);

      const distanceInMeters = data.routes[0].legs[0].distance.value;
      const distanceInKm = distanceInMeters / 1000;
      const distance = Number(distanceInKm.toFixed(2)); 

      const durationInSeconds = data.routes[0].legs[0].duration.value;
      const durationInMinutes = durationInSeconds / 60; 
      const duration = parseFloat(durationInMinutes.toFixed()); // Ensure it's a number
      return {points, distance, duration};
    } else {
      throw new Error('Directions not found');
    }
  } catch (error) {
    console.error('Error fetching directions:', error);
    throw error;
  }
};

//Location.tsx
export const handleSearch = async (
  text: string,
  userLocation: { latitude?: number; longitude?: number } | null,
  setPlaces: React.Dispatch<React.SetStateAction<Record<string, any>[]>>,
  setLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setNoResultsFound: React.Dispatch<React.SetStateAction<boolean>>,
  searchRadius?: number,
) => {
  try {
    setNoResultsFound(false);
    setLoading(true);
    if (!userLocation?.latitude || !userLocation?.longitude) {
      throw new Error('User location not found');
    }

    const apiKey = Config.GOOGLE_PLACES_API_KEY;
    const location = `${userLocation.latitude},${userLocation.longitude}`;
    const radius = searchRadius?.toString() || '100000';

    const latDelta = parseInt(radius) / 111000;
    const lngDelta =
      (parseInt(radius) / 111000) /
      Math.cos(userLocation.latitude * (Math.PI / 180));

    const bounds = `${userLocation.latitude - latDelta},${userLocation.longitude - lngDelta}|${userLocation.latitude + latDelta},${userLocation.longitude + lngDelta}`;

    const apiUrl = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&key=${apiKey}&location=${location}&radius=${radius}&components=country:in&bounds=${bounds}`;

    const response = await fetch(apiUrl);
    const data = await response.json();

    if (data.status === 'OK' && data.predictions && data.predictions.length > 0) {
      const placesPromises = data.predictions.slice(0, 10).map(async (prediction: any) => {
        const detailsUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.place_id}&fields=geometry,name,vicinity,formatted_address&key=${apiKey}`;
        const detailsResponse = await fetch(detailsUrl);
        const detailsData = await detailsResponse.json();

        if (detailsData.status === 'OK' && detailsData.result) {
          const distanceInMeters = calculateDistance(userLocation, {
            latitude: detailsData.result.geometry.location.lat,
            longitude: detailsData.result.geometry.location.lng,
          });

          return {
            id: prediction.place_id,
            name: prediction.structured_formatting?.main_text || prediction.description,
            latitude: detailsData.result.geometry.location.lat,
            longitude: detailsData.result.geometry.location.lng,
            vicinity:
              prediction.structured_formatting?.secondary_text ||
              detailsData.result.vicinity ||
              detailsData.result.formatted_address,
            distance: distanceInMeters,
            searchText: text,
            types: prediction.types,
          };
        }
        return null;
      });

      const placesData = (await Promise.all(placesPromises)).filter(Boolean);

      if (placesData.length > 0) {
        const searchTextLower = text.toLowerCase();

        const placesWithRelevance = placesData.map((place: any) => {
          const nameLower = place.name.toLowerCase();
          let relevanceScore = 0;

          if (nameLower === searchTextLower) {
            relevanceScore = 100;
          } else if (nameLower.startsWith(searchTextLower)) {
            relevanceScore = 80;
          } else if (nameLower.includes(searchTextLower)) {
            relevanceScore = 60;
          } else {
            const searchWords = searchTextLower.split(/\s+/);
            const matchedWords = searchWords.filter(
              (word) => word.length > 1 && nameLower.includes(word)
            );
            if (matchedWords.length > 0) {
              relevanceScore = 40 * (matchedWords.length / searchWords.length);
            }
          }

          const distanceFactor = Math.max(0, 20 - Math.floor(place.distance / 500));

          return {
            ...place,
            relevanceScore: relevanceScore + distanceFactor,
          };
        });

        const finalPlaces = placesWithRelevance
          .sort((a: any, b: any) => {
            if (b.relevanceScore !== a.relevanceScore) {
              return b.relevanceScore - a.relevanceScore;
            }
            return a.distance - b.distance;
          })
          .slice(0, 5); 

        console.log(
          'Search results:',
          finalPlaces.map(
            (p: any) =>
              `${p.name} (score: ${p.relevanceScore}, distance: ${p.distance.toFixed(1)}m)`
          )
        );

        setPlaces(finalPlaces);
        setNoResultsFound(finalPlaces.length === 0);
      } else {
        setPlaces([]);
        setNoResultsFound(true);
      }
    } else {
      setPlaces([]);
      setNoResultsFound(true);
    }
  } catch (error) {
    console.error('Error fetching places:', error);
    setPlaces([]);
    setNoResultsFound(true);
  } finally {
    setLoading(false);
  }
};




export const calculateDistance = (
  userLocation: LatLng | {latitude?: number; longitude?: number} | null,
  placeLocation: LatLng | null,
): number => {
  if (
    userLocation &&
    placeLocation &&
    userLocation.latitude !== undefined &&
    userLocation.longitude !== undefined
  ) {
    const R = 6371e3; // Radius of the Earth in meters
    const lat1 = userLocation.latitude * (Math.PI / 180);
    const lat2 = placeLocation.latitude * (Math.PI / 180);
    const lon1 = userLocation.longitude * (Math.PI / 180);
    const lon2 = placeLocation.longitude * (Math.PI / 180);

    const dLat = lat2 - lat1;
    const dLon = lon2 - lon1;

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }
  return Infinity;
};
