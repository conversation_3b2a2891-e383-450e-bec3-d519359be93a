// useAuth.js
import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    checkAuthentication();
  }, [isAuthenticated]);

  const checkAuthentication = async () => {
    try {
      const authToken = await AsyncStorage.getItem('accessToken');
      setIsAuthenticated(!!authToken);
    } catch (error) {
      console.error('Error checking authentication state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return { isAuthenticated, isLoading }; 
};
