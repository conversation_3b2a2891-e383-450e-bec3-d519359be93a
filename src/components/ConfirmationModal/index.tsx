import React from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import Button from '../Button/Button';
import HollowButton from '../Button/HollowButton/HollowButton';
import { GeistFont, colors, sizes } from '../../constants';
import { spacing } from '../../constants/theme';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';


interface ConfirmationModalProps {
    title: string;
    visible: boolean;
    message: string;
    onConfirm: () => void;
    onCancel: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
    title,
    visible,
    message,
    onConfirm,
    onCancel,
}) => {
    return (
        <Modal transparent={true} visible={visible} animationType="fade">
            <ModalBackground>
                <ModalContainer>
                    <Title>{title}</Title>
                    <FadingHorizontalLine />
                    <Message>{message}</Message>
                    <ButtonContainer>
                        <Button
                            title={'Confirm'}
                            style={{ width: '48%' }}
                            onPress={onConfirm}
                        />
                        <HollowButton
                            title={'Cancel'}
                            style={{ width: '48%' }}
                            borderColor={colors.red}
                            textColor={colors.red}
                            onPress={onCancel}
                        />
                    </ButtonContainer>
                </ModalContainer>
            </ModalBackground>
        </Modal>
    );
};

const ModalBackground = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
`;

const ModalContainer = styled.View`
  width: 85%;
  padding: ${spacing.xxl}px;
  background-color: ${colors.darkGrey};
  border-radius: ${spacing.xxs}px;
`;

const Title = styled.Text`
  font-size: ${sizes.h5}px;  
  color: ${colors.white};   
  margin-bottom: ${spacing.md}px;  
  align-items: flex-start;
  justify-content: flex-start;
  font-family: ${GeistFont.bold};
`;

const Message = styled.Text`
  font-size: ${sizes.h6}px;
  font-family:${GeistFont.regular};
  margin-vertical: ${spacing.lg}px;
  text-align: center;
  color:${colors.lightGrey}
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
`;


export default ConfirmationModal;
