import React, {useState, useEffect, createContext, useContext} from 'react';
import styled from 'styled-components/native';
import {Text} from 'react-native';
import {spacing} from '../../constants/theme';
import {colors} from '../../constants';

interface ToastProps {
  message: string;
  type?: 'success' | 'failure' | 'default';
}

const ToastContainer = styled.View<{type: 'success' | 'failure' | 'default'}>`
  background-color: ${({type}: {type: 'success' | 'failure' | 'default'}) =>
    type === 'success' ? colors.green :
    type === 'failure' ? colors.red :
    colors.grey};
  padding: ${spacing.md}px;
  border-radius: ${spacing.xs}px;
  position: absolute;
  top: ${spacing.xl * 2}px;
  right: ${spacing.xl}px;
  left: ${spacing.xxl}px; 
  margin-horizontal: ${spacing.md}px;
  align-items: center;
`;


const ToastText = styled(Text)`
  color: #fff;
  font-size: 14px;
  text-align: center;
  font-family: 'Geist-Regular';
`;

const Toast: React.FC<ToastProps> = ({message, type = 'default'}) => {
  const [isVisible, setIsVisible] = useState<boolean>(true);

  useEffect(() => {
    setIsVisible(true); 
    const timeout = setTimeout(() => {
      setIsVisible(false);
    }, 5000);

    return () => clearTimeout(timeout);
  }, [message]);

  return isVisible ? (
    <ToastContainer type={type}>
      <ToastText>{message}</ToastText>
    </ToastContainer>
  ) : null;
};

interface ToastContextProps {
  showToast: (
    message: string,
    type?: 'success' | 'failure' | 'default',
  ) => void;
}

interface ToastProviderProps {
  children: React.ReactNode;
}

const ToastContext = createContext<ToastContextProps | undefined>(undefined);

const ToastProvider: React.FC<ToastProviderProps> = ({children}) => {
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<
    'success' | 'failure' | 'default' | null
  >(null);
  const [toastKey, setToastKey] = useState<number>(0);

  const showToast = (
    message: string,
    type: 'success' | 'failure' | 'default' = 'default',
  ) => {
    setToastMessage(message);
    setToastType(type);
    setToastKey(prevKey => prevKey + 1);
  };

  return (
    <ToastContext.Provider value={{showToast}}>
      {children}
      {toastMessage && toastType && (
        <Toast key={toastKey} message={toastMessage} type={toastType} />
      )}
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export {ToastProvider};
