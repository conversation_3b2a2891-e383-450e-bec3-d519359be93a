import React from 'react';
import { View, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface FadingLineProps {
  style?: any;
}

const FadingHorizontalLine: React.FC<FadingLineProps> = ({ style }) => {
  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={['rgba(233, 233, 233, 0)', 'rgba(233, 233, 233, 1)', 'rgba(233, 233, 233, 0)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4, 
    marginVertical: 0,  
  },
  gradient: {
    height: 0.5,
    width: '90%',
  },
});

export default FadingHorizontalLine;
