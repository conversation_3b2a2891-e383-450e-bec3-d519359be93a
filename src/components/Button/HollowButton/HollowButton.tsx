import React from 'react';
import { TouchableOpacity, Text, TouchableOpacityProps, View } from 'react-native';
import styled from 'styled-components/native';
import { colors, EBGaramondFont, GeistFont, sizes } from '../../../constants';
import { spacing } from '../../../constants/theme';
import IconSvgView from '../../IconSvgView/IconSvgView';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  disabled?: boolean;
  textColor?: string;
  borderColor?: string;
  icon?: string
}

const ButtonContainer = styled(TouchableOpacity) <{ disabled?: boolean; borderColor?: string }>`
  padding: ${spacing.md}px ${spacing.lg}px;
  border-radius: ${spacing.xxs}px;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-width: 2px;
  border-color: ${({ disabled, borderColor }) =>
    disabled ? 'rgba(45, 46, 50, 0.40)' : borderColor ? borderColor : colors.grey};
  flex-direction: row; 
    background-color: ${({ disabled }) =>
    disabled ? 'rgba(45, 46, 50, 0.40);' : colors.darkCharcoal};
`;

const ButtonText = styled(Text) <{ disabled?: boolean; textColor?: string }>`
  color: ${({ disabled, textColor }) =>
    disabled ? '#4A4B4F' : textColor ? textColor : colors.lightGrey};
  font-size: ${sizes.h6}px;
  margin-horizontal: ${spacing.sm}px; 
  font-family: ${GeistFont.regular};
  font-weight: 700;
`;

const HollowButton: React.FC<ButtonProps> = ({
  onPress,
  title,
  disabled = false,
  textColor,
  borderColor,
  icon,
  ...rest
}) => {
  return (
    <ButtonContainer
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      borderColor={borderColor}
      {...rest}
      testID="button-container"
    >
      {icon && <IconSvgView source={icon} />}
      <ButtonText
        disabled={disabled}
        textColor={textColor}
        testID="button-text"
      >
        {title}
      </ButtonText>
    </ButtonContainer>
  );
};

export default HollowButton;
