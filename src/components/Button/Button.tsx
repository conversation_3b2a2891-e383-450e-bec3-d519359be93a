import React from 'react';
import {
  TouchableOpacity,
  Text,
  TouchableOpacityProps,
  ActivityIndicator,
} from 'react-native';
import styled from 'styled-components/native';
import {spacing} from '../../constants/theme';
import {GeistFont, colors, sizes} from '../../constants';

interface ButtonProps extends TouchableOpacityProps {
  title?: React.ReactNode;
  disabled?: boolean;
  textColor?: string;
  loading?: boolean;
  backgroundColor?: string;
  borderColor?: string;
  disabledBorderColor?: string; 
}

const ButtonContainer = styled(TouchableOpacity)<{
  disabled?: boolean;
  backgroundColor?: string;
  borderColor?: string;
  disabledBorderColor?: string; 
}>`
  padding: ${spacing.md}px ${spacing.lg}px;
  border-radius: ${spacing.xxs}px;
  align-items: center;
  justify-content: center;
  background-color: ${({
    disabled,
    backgroundColor,
  }: {
    disabled?: boolean;
    backgroundColor?: string;
  }) =>
    disabled ? 'rgba(45, 46, 50, 0.40)' : backgroundColor || colors.lightGrey};
  border-width: ${({borderColor, disabled, disabledBorderColor}) => 
    (borderColor || (disabled && disabledBorderColor)) ? '2px' : '0px'};
  border-color: ${({disabled, borderColor, disabledBorderColor}) => 
    disabled && disabledBorderColor ? disabledBorderColor : 
    disabled ? 'rgba(74, 75, 79, 0.5)' : 
    borderColor || 'transparent'};
`;

const ButtonText = styled(Text)<{disabled?: boolean; textColor?: string}>`
  color: ${({disabled, textColor}) =>
    disabled ? '#4A4B4F' : textColor ? textColor : colors.davyGrey};
  font-size: ${sizes.h6}px;
  font-weight: 700;
  text-align: center;
  font-family: ${GeistFont.regular};
`;

const Button: React.FC<ButtonProps> = ({
  onPress,
  title,
  disabled = false,
  textColor,
  loading = false,
  backgroundColor,
  borderColor,
  disabledBorderColor, 
  ...rest
}) => {
  return (
    <ButtonContainer
      onPress={disabled || loading ? undefined : onPress}
      disabled={disabled || loading}
      backgroundColor={backgroundColor}
      borderColor={borderColor}
      disabledBorderColor={disabledBorderColor} 
      {...rest}
      testID="button-container">
      {loading ? (
        <ActivityIndicator size="small" color="#fff" />
      ) : typeof title === 'string' ? (
        <ButtonText
          disabled={disabled}
          textColor={textColor}
          testID="button-text">
          {title}
        </ButtonText>
      ) : (
        title
      )}
    </ButtonContainer>
  );
};

export default Button;
