import React, {useRef} from 'react';
import {
  Modal,
  ScrollView,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  View,
  TouchableWithoutFeedback,
  Text,
  Linking,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import close from '../../icons/close.svg';
import styled from 'styled-components/native';
import IconSvgView from '../IconSvgView/IconSvgView';
import {colors, GeistFont, sizes} from '../../constants';
import Button from '../Button/Button';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {spacing} from '../../constants/theme';

const {height} = Dimensions.get('window');

interface BottomUpModalProps {
  showModal: boolean;
  onClose: () => void;
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
  forceUpdate: boolean;
  descriptionComponent?: React.ReactNode;
}

const BottomUpModal: React.FC<BottomUpModalProps> = ({
  showModal,
  onClose,
  title,
  description,
  buttonText,
  onButtonClick,
  forceUpdate,
  descriptionComponent,
}) => {
  const {t} = useTranslation();
  const scrollViewRef = useRef<ScrollView>(null);

  return (
    <Modal
      transparent={true}
      animationType="slide"
      visible={showModal}
      onRequestClose={() => {
        if (!forceUpdate) onClose();
      }}>
      <TouchableWithoutFeedback>
        <OverlayContainer>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1}}>
            <SafeAreaView style={styles.safeArea}>
              <ModalContainer>
                <HeaderContainer>
                  <Title>{title}</Title>
                  {!forceUpdate && (
                    <CloseButton onPress={onClose}>
                      <IconSvgView width={14} source={close} />
                    </CloseButton>
                  )}
                </HeaderContainer>
                <FadingHorizontalLine />

                {descriptionComponent ? (
                  <DescriptionContainer>
                    {descriptionComponent}
                  </DescriptionContainer>
                ) : (
                  <Description>{description}</Description>
                )}

                <Button title={buttonText} onPress={onButtonClick} />
              </ModalContainer>
            </SafeAreaView>
          </KeyboardAvoidingView>
        </OverlayContainer>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const OverlayContainer = styled.View`
  flex: 1;
  background-color: rgba(43, 43, 43, 0.1);
  justify-content: flex-end;
`;

const ModalContainer = styled(View)`
  background-color: ${colors.darkCharcoal};
  padding: ${spacing.xxl}px;
  border-top-left-radius: ${spacing.xxl}px;
  border-top-right-radius: ${spacing.xxl}px;
  elevation: 5;
  shadow-color: #000;
  shadow-opacity: 0.1;
  shadow-radius: ${spacing.md}px;
`;

const HeaderContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-right: ${spacing.md}px;
  margin-bottom: ${spacing.xl}px;
`;

const Title = styled.Text`
  flex: 1;
  font-size: ${sizes.h5}px;
  font-weight: bold;
  line-height: ${sizes.body * 2}px;
  color: ${colors.lightGrey};
  flex-shrink: 1;
`;

const CloseButton = styled.TouchableOpacity`
  padding-horizontal: ${spacing.md}px;
`;

const Description = styled.Text`
  font-size: ${sizes.body}px;
  color: ${colors.white};
  margin-vertical: ${spacing.xl}px;
  line-height: ${sizes.body * 1.5}px;
  font-family: ${GeistFont.regular};
`;

const DescriptionContainer = styled.View`
  margin-vertical: ${spacing.xl}px;
`;

const styles = {
  safeArea: {
    flex: 1,
    justifyContent: 'flex-end' as 'flex-end',
  },
};

export default BottomUpModal;
