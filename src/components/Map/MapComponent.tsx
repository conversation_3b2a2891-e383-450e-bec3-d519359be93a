import React, {forwardRef, useEffect, useState, useRef, useMemo} from 'react';
import {
  Animated,
  Image,
  Modal,
  Text,
  View,
  Platform,
  Dimensions,
} from 'react-native';
import MapView, {
  LatLng,
  PROVIDER_GOOGLE,
  Polyline,
  Polygon,
  UserLocationChangeEvent,
  Marker,
  Region,
} from 'react-native-maps';
import {colors, images} from '../../constants';
import {DarkMapStyle} from '../../constants/constants';
import {calculateDistance} from '../../utils/MapUtils';
import {userLocationContext} from '../../utils/userLocationContext';
import styles from './MapComponentStyle';
import Config from 'react-native-config';
import {StyleSheet} from 'react-native';

const MapComponent = forwardRef<
  MapView,
  {
    setAddress: (address: string) => void;
    showLocation?: boolean;
    marker?: boolean;
    children?: React.ReactNode;
    style?: any;
    onMapMove?: () => void;
    mapPinText?: string;
    region: LatLng | null;
    setRegion: (region: LatLng | null) => void;
    ridePoints?: {latitude: number; longitude: number}[];
    boundary?: LatLng[];
    selectedLocation?: any;
    selectedIndex?: any;
    initialAnimationComplete?: boolean;
    preserveAddress?: boolean;
  }
>(
  (
    {
      setAddress,
      showLocation,
      marker = true,
      children,
      style,
      onMapMove,
      mapPinText,
      region,
      setRegion,
      ridePoints,
      boundary,
      selectedLocation,
      selectedIndex,
      initialAnimationComplete = true,
      preserveAddress = false,
    },
    ref,
  ) => {
    const {userLocation, setUserLocation} = userLocationContext();
    const [showText, setShowText] = useState<boolean>(false);
    const [mapLoaded, setMapLoaded] = useState(false);
    const scaleAnim = useState(new Animated.Value(0))[0];
    const [isMapLoading, setIsMapLoading] = useState<boolean>(true);
    const [initialRegionSet, setInitialRegionSet] = useState<boolean>(false);
    const [mapMovedByUser, setMapMovedByUser] = useState<boolean>(false);
    const mapRef = useRef<MapView | null>(null);

    const hasNotch = useMemo(() => {
      const {height, width} = Dimensions.get('window');
      return (
        Platform.OS === 'ios' &&
        !Platform.isPad &&
        !Platform.isTV &&
        (height === 812 ||
          width === 812 ||
          height === 844 ||
          width === 844 ||
          height === 896 ||
          width === 896 ||
          height === 926 ||
          width === 926 ||
          height > 926)
      );
    }, [Dimensions]);

    const iPhoneStatusBarHeight = useMemo(
      () => (Platform.OS === 'ios' ? (hasNotch ? 44 : 20) : 0),
      [hasNotch],
    );

    const fetchAddress = async (latitude: number, longitude: number) => {
      try {
        const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_API}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === 'OK') {
          const addressComponents = data.results[0].address_components;
          const relevantParts = addressComponents
            .filter(
              (component: {types: string | string[]}) =>
                component.types.includes('route') || // Street/Road
                component.types.includes('sublocality') || // Area
                component.types.includes('neighborhood') || // Neighborhood
                component.types.includes('sublocality_level_1') || // Sub area
                component.types.includes('sublocality_level_2') || // More specific area
                component.types.includes('locality'), // City
            )
            .map((component: {long_name: any}) => component.long_name)
            .filter(Boolean);

          // Filter out any Google Plus code formats from the address
          const addressParts = data.results[0].formatted_address.split(',');
          const filteredParts = addressParts.filter((part: string) => {
            const trimmed = part.trim();
            // Filter out any part that looks like a plus code (matches various formats)
            return !trimmed.match(/^[A-Z0-9]{4,8}\+[A-Z0-9]{2,3}/) && 
                   !trimmed.match(/^[A-Z0-9]{2,3}\d{2,3}\+[A-Z0-9]{2,3}/) &&
                   !trimmed.match(/^X\d{3,4}\+[A-Z0-9]{2,3}/);
          });
          
          // Use the relevant parts from address_components if filtered address is too short
          let streetAddress;
          if (filteredParts.length < 2 && relevantParts.length > 0) {
            streetAddress = relevantParts.slice(0, 3).join(', ');
          } else {
            streetAddress = filteredParts.slice(0, 3).join(',');
          }
          
          // Ensure we're not returning "Current Location" as the address
          if (streetAddress && streetAddress.trim() !== '') {
            console.log('streetAddress:', streetAddress);
            setAddress(streetAddress);
          } else {
            // If we couldn't get a meaningful address, try to create one from the most relevant components
            const fallbackAddress = addressComponents
              .filter((component: {types: string | string[]}) => 
                component.types.includes('premise') ||
                component.types.includes('street_number') ||
                component.types.includes('route') ||
                component.types.includes('sublocality')
              )
              .map((component: {long_name: any}) => component.long_name)
              .filter(Boolean)
              .slice(0, 3)
              .join(', ');
            
            console.log('fallback address:', fallbackAddress);
            setAddress(fallbackAddress || 'Location');
          }
        } else {
          setAddress('Location');
        }
      } catch (error) {
        console.error('Error fetching address:', error);
        setAddress('Location');
      }
    };

    // Fetch address in these cases:
    // 1. When map is moved by user (mapMovedByUser is true)
    // 2. When we're in the pickup screen and have a region but no address yet
    // 3. When initialAnimationComplete is true and we need to show the address right away
    useEffect(() => {
      if (region?.latitude && region?.longitude && initialAnimationComplete) {
        // Either the map was moved by user, or we need to fetch address on initial load
        if (mapMovedByUser || preserveAddress) {
          fetchAddress(region.latitude, region.longitude);
        }
      }      
    }, [region, initialAnimationComplete, mapMovedByUser, preserveAddress]);

    useEffect(() => {
      if (mapLoaded && region && !initialRegionSet) {
        // Always prioritize the provided region over userLocation
        const regionToUse = {
          latitude: region.latitude,
          longitude: region.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };

        if (mapRef.current) {
          mapRef.current.animateToRegion(regionToUse, 500);
          setInitialRegionSet(true);
          
          // For pickup screen: if preserveAddress is true, we need to 
          // ensure mapMovedByUser is true to fetch the address
          if (preserveAddress) {
            setMapMovedByUser(true);
          }
        }
      } else if (mapLoaded && userLocation && !initialRegionSet && !region) {
        // Only use userLocation if no region is provided
        const initialRegion = {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };

        if (mapRef.current) {
          mapRef.current.animateToRegion(initialRegion, 500);
          setRegion(userLocation);
          setInitialRegionSet(true);
          
          // Current location needs geocoding, so set this to true
          setMapMovedByUser(true);
        }
      }
    }, [mapLoaded, region, userLocation, initialRegionSet, preserveAddress, setRegion]);

    const handleChange = (e: UserLocationChangeEvent) => {
      const coordinate = e.nativeEvent.coordinate;

      if (coordinate?.latitude && coordinate.longitude) {
        setUserLocation({
          latitude: coordinate.latitude,
          longitude: coordinate.longitude,
        });
      }
    };

    const handleRegionChangeComplete = (region: Region, details: any) => {
      if (details?.isGesture && onMapMove) {
        console.log('regionComplete', region);
        
        setRegion({
          latitude: region.latitude,
          longitude: region.longitude,
        });
        onMapMove();
        setShowText(true);
        // Mark that the map has been moved by the user
        setMapMovedByUser(true);
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      }
    };

    const INITIAL_REGION = userLocation
      ? {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }
      : {
          latitude: 20.5937,
          longitude: 78.9629,
          latitudeDelta: 5,
          longitudeDelta: 5,
        };

    return (
      <View style={[styles.container, style]}>
        <View style={styles.mapContainer}>
          <MapView
            initialRegion={INITIAL_REGION}
            provider={PROVIDER_GOOGLE}
            ref={mapInstance => {
              if (typeof ref === 'function') {
                ref(mapInstance);
              } else if (ref) {
                ref.current = mapInstance;
              }
              mapRef.current = mapInstance;
            }}
            followsUserLocation={Platform.OS === 'ios'}
            style={[StyleSheet.absoluteFillObject, styles.map]}
            showsUserLocation={showLocation}
            showsMyLocationButton={false}
            showsCompass={false}
            onRegionChangeComplete={handleRegionChangeComplete}
            customMapStyle={DarkMapStyle}
            onUserLocationChange={handleChange}
            loadingEnabled={false}
            loadingBackgroundColor={colors.black}
            userLocationUpdateInterval={20000}
            pitchEnabled={true}
            zoomEnabled={true}
            onMapLoaded={() => {
              setMapLoaded(true);
              setIsMapLoading(false);
            }}
            paddingAdjustmentBehavior="automatic"
            mapPadding={
              Platform.OS === 'ios'
                ? {top: 0, right: 0, bottom: 0, left: 0}
                : undefined
            }>
            {children}

            {boundary && boundary.length > 0 && (
              <Polygon
                coordinates={boundary}
                strokeColor="#FAFBFE"
                fillColor="rgba(250, 251, 254, 0.2)"
                strokeWidth={2}
              />
            )}

            {ridePoints &&
              ridePoints.length > 0 &&
              ridePoints.map((point, index) => {
                const latitude = parseFloat(point.latitude.toString());
                const longitude = parseFloat(point.longitude.toString());
                if (isNaN(latitude) || isNaN(longitude)) {
                  console.warn(
                    'Invalid latitude or longitude in ride point:',
                    point,
                  );
                  return null;
                }

                const isSelectedLocation =
                  selectedLocation &&
                  parseFloat(selectedLocation.latitude.toString()) ===
                    latitude &&
                  parseFloat(selectedLocation.longitude.toString()) ===
                    longitude;

                return (
                  <Marker
                    key={index}
                    anchor={{x: 0.5, y: 0.5}}
                    tracksViewChanges={false}
                    coordinate={{latitude, longitude}}
                    icon={
                      isSelectedLocation
                        ? images.activePoints
                        : images.pickupSpot
                    }
                  />
                );
              })}

            {selectedLocation && (
              <Marker
                anchor={{x: 0.5, y: 0.5}}
                tracksViewChanges={false}
                coordinate={{
                  latitude: parseFloat(selectedLocation.latitude.toString()),
                  longitude: parseFloat(selectedLocation.longitude.toString()),
                }}
                icon={images.activePoints}
              />
            )}

            {region &&
            userLocation &&
            marker &&
            calculateDistance(userLocation, region)! <= 250 &&
            mapLoaded ? (
              <Polyline
                coordinates={[userLocation, region]}
                strokeColor="#FFFFFF"
                strokeColors={['#FFFFFF']}
                strokeWidth={4}
                lineDashPattern={[20, 10]}
              />
            ) : null}
          </MapView>

          {marker && mapLoaded && (
            <View
              style={
                selectedLocation
                  ? styles.pickupMarker
                  : Platform.OS === 'ios' &&
                    region &&
                    userLocation &&
                    calculateDistance(userLocation, region)! <= 50
                  ? styles.locationMarker 
                  : styles.markerFixed
              }
              pointerEvents="none">
              <View
                style={{
                  height: 70,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: Platform.OS === 'ios' ? iPhoneStatusBarHeight : 0,
                }}>
                <Image source={images.mapPin} style={{width: 40, height: 50}} />
              </View>
            </View>
          )}
        </View>
      </View>
    );
  },
);

export default MapComponent;
