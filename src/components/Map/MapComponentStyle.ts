import {Platform, StyleSheet} from 'react-native';
import {spacing} from '../../constants/theme';
import {GeistFont, colors, sizes} from '../../constants';

export default StyleSheet.create({
  marker: {
    flex: 1,
    resizeMode: 'cover',
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: '#7b7c82',
    borderRadius: 1,
    height: 50,
    width: 50,
  },
  diamondIcon: {
    marginRight: spacing.xl,
  },
  markerFixed: {
    left: '50%',
    marginLeft: Platform.OS === 'ios' ? -18 : -20, 
    position: 'absolute',
    top: '50%',
    marginTop: Platform.OS === 'ios' ? -55 : -60, 
    alignItems: 'center',
    height: 80,
  },
  locationMarker: {
    left: '50%',
    marginLeft: Platform.OS === 'ios' ? -18 : -20,
    position: 'absolute',
    top: '50%',
    marginTop: Platform.OS === 'ios' ? -40 : -45, 
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
  },
  pickupMarker: {
    left: '50%',
    marginLeft: -20,
    position: 'absolute',
    top: '50%',
    marginTop: -60,
    alignItems: 'center',
    height: 80,
  },
  mapPinContainer: {
    backgroundColor: colors.white,
    padding: spacing.sm,
    borderRadius: spacing.xxs,
  },

  mapPinContainerRound: {
    backgroundColor: colors.white,
    padding: spacing.md,
    width: spacing.xxl * 2,
    height: spacing.xxl * 2,
    borderRadius: spacing.xxl,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },

  mapPinText: {
    color: colors.black,
    fontSize: sizes.body / 1.1,
    fontWeight: '600',
  },
  pickupAddress: {
    color: colors.white,
    fontSize: sizes.body / 1.3,
    width: 200,
    marginBottom: spacing.sm,
  },
  container: {
    flex: 1,
    position: 'relative',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'visible',
  },
  map: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  loaderContainer: {
    flex: 1,
    backgroundColor: colors.black,
  },
  loaderWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.black,
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});
