import React, {forwardRef, useImperativeHandle} from 'react';
import {StyleSheet, Dimensions} from 'react-native';
import {OtpInput} from 'react-native-otp-entry';
import {spacing} from '../../constants/theme';
import {colors, GeistFont, sizes} from '../../constants';

const screenWidth = Dimensions.get('window').width;
const safeAreaPadding = spacing.xl * 1.5 * 2;
const availableWidth = screenWidth - safeAreaPadding;
const boxSize = availableWidth / 8;

interface OTPInputProps {
  otp: string;
  onOtpChange: (otp: string) => void;
  onOtpComplete: (isComplete: boolean) => void;
  defaultValue?: number;
}

const OTPInput = forwardRef(
  ({otp, onOtpChange, onOtpComplete, defaultValue = 6}: OTPInputProps, ref) => {
    useImperativeHandle(ref, () => ({
      resetFocus: () => {
        onOtpChange('');
      },
    }));

    return (
      <OtpInput
        numberOfDigits={defaultValue}
        onTextChange={onOtpChange}
        onFilled={(otpValue: string | any[]) =>
          onOtpComplete(otpValue.length === defaultValue)
        }
        focusColor="white"
        theme={{
          containerStyle: styles.container,
          pinCodeContainerStyle: styles.pinCodeContainer,
          pinCodeTextStyle: styles.pinCodeText,
          focusStickStyle: styles.focusStick,
          focusedPinCodeContainerStyle: styles.activePinCodeContainer,
        }}
      />
    );
  },
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: spacing.md,
    flexDirection: 'row',
  },
  pinCodeContainer: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    width: boxSize,
    height: boxSize * 1.2,
    marginHorizontal: 4,
  },
  activePinCodeContainer: {
    borderColor: colors.white,
    borderWidth: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  pinCodeText: {
    color: colors.white,
    fontSize: sizes.h4,
    fontFamily: GeistFont.regular,
  },
  focusStick: {
    width: 2,
    height: spacing.xxl,
    backgroundColor: colors.white,
  },
});

export default OTPInput;
