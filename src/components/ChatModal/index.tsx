import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Animated,
  FlatList,
  StyleSheet,
  Dimensions,
  Platform,
  TextInput,
  Keyboard,
  Image,
  BackHandler,
} from 'react-native';
import IconSvgView from '../IconSvgView/IconSvgView';
import close from '../../icons/close.svg';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../constants';
import {spacing} from '../../constants/theme';
import FadingHorizontalLine from '../FadingLine/FadingHorizontalLine';
import {Message} from '../../hooks/useRideDetails';
import {t} from 'i18next';
import notifee from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

type ChatModalProps = {
  visible: boolean;
  onClose: () => void;
  userId: string;
  messages: Message[];
  tripId: string | undefined;
  sendMessage: (message: string) => void;
  title?: string;
  emptyMessageText?: string;
  isLoading: boolean;
};

const {height: screenHeight} = Dimensions.get('window');

const ChatModal: React.FC<ChatModalProps> = ({
  visible,
  onClose,
  messages,
  sendMessage,
  title = t('chat'),
  emptyMessageText = t('no_messages'),
  isLoading,
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList<Message>>(null);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textInputRef = useRef<TextInput>(null);
  const [initialScroll, setInitialScroll] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [autoResponseSent, setAutoResponseSent] = useState<
    Record<string, boolean>
  >({});
  const [processedMessages, setProcessedMessages] = useState<Message[]>([]);

  useEffect(() => {
    if (messages && messages.length > 0) {
      const validMessages = messages.map(msg => ({
        ...msg,
        text: msg.text || msg.content || '',
        time: msg.time || new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        id: msg.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      }));
      setProcessedMessages(validMessages);
    } else {
      setProcessedMessages([]);
    }
  }, [messages]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (visible) {
        slideOutModal();
        return true; 
      }
      return false;
    });

    return () => backHandler.remove();
  }, [visible]);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      e => {
        setKeyboardVisible(true);
        setKeyboardHeight(e.endCoordinates.height);
      },
    );
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        setKeyboardHeight(0);
      },
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  useEffect(() => {
    const clearDriverMessageNotifications = async () => {
      try {
        await notifee.cancelNotification('driver-messages');

        const notifications = await notifee.getDisplayedNotifications();
        for (const notification of notifications) {
          if (
            notification.id === 'driver-messages' ||
            ((notification as any).data &&
              (notification as any).data.type === 'driver-message')
          ) {
            if (notification.id) {
              await notifee.cancelNotification(notification.id);
            }
          }
        }

        const viewedTimestamp = Date.now();
        await AsyncStorage.setItem(
          'messagesViewedTimestamp',
          viewedTimestamp.toString(),
        );

        const keys = await AsyncStorage.getAllKeys();
        const messageKeys = keys.filter(key => key.startsWith('messages-'));
        if (messageKeys.length > 0) {
          await AsyncStorage.multiRemove(messageKeys);
        }

        console.log(
          'Driver message notifications cleared at:',
          viewedTimestamp,
        );
      } catch (error) {
        console.error('Error clearing notifications:', error);
      }
    };

    if (visible) {
      clearDriverMessageNotifications();
      
      const intervalId = setInterval(() => {
        clearDriverMessageNotifications();
      }, 10000);
      
      return () => clearInterval(intervalId);
    }
  }, [visible]);

  const slideInModal = () => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const slideOutModal = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      slideInModal();
      setInitialScroll(false);
    }
  }, [visible]);

  const scrollToBottom = (animated = true) => {
    if (processedMessages.length > 0 && flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated });
    }
  };

  useEffect(() => {
    if (visible && processedMessages.length > 0) {
      const timer = setTimeout(() => {
        scrollToBottom(false);
        setTimeout(() => {
          scrollToBottom(false);
          setInitialScroll(true);
        }, 300);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [visible, processedMessages]);

  // Track the last send time to prevent duplicate sends
  const lastSendTimeRef = useRef<number>(0);
  
  const handleSendMessage = async () => {
    // Prevent duplicate sends by checking timestamp (especially important for iOS)
    const now = Date.now();
    if (now - lastSendTimeRef.current < 500) {
      console.log('Preventing duplicate message send');
      return;
    }
    
    if (message.trim() && !isSending) {
      const messageToSend = message.trim();
      lastSendTimeRef.current = now;
      setIsSending(true);
      setMessage('');

      if (textInputRef.current) {
        textInputRef.current.clear(); 
      }

      try {
        if (Platform.OS === 'ios') {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        await sendMessage(messageToSend);
        setTimeout(() => {
          scrollToBottom(true);
        }, 200);
      } catch (error) {
        console.error('Error sending message:', error);
      } finally {
        setIsSending(false);
      }
    }
  };

  const showCustomEmptyMessage = emptyMessageText !== t('no_messages');
  const shouldAutoRespond = (messageText: string): boolean => {
    const triggerPhrases = [
      'your driver is on the way',
      'your ride is arriving',
      'your ride has arrived',
      'driver is waiting for you',
    ];

    const lowerCaseMessage = messageText.toLowerCase();
    return triggerPhrases.some(phrase =>
      lowerCaseMessage.includes(phrase.toLowerCase()),
    );
  };

  useEffect(() => {
    if (processedMessages.length > 0) {
      const latestReceiverMessages = [...processedMessages]
        .reverse()
        .filter(msg => !msg.sender);

      if (latestReceiverMessages.length > 0) {
        const latestMsg = latestReceiverMessages[0];
        const msgContent = latestMsg.text || '';
        const msgId = `${latestMsg.id || latestMsg.time}`;

        if (shouldAutoRespond(msgContent) && !autoResponseSent[msgId]) {
          setAutoResponseSent(prev => ({...prev, [msgId]: true}));

          const timer = setTimeout(() => {
            sendMessage(t('thank_you_auto_response'));
          }, 1500);

          return () => clearTimeout(timer);
        }
      }
    }
  }, [processedMessages, sendMessage, autoResponseSent]);

  const renderMessageItem = ({ item }: { item: Message }) => {
    const messageText = item.text || item.content || '';
    
    if (!messageText) {
      console.warn('Empty message content detected', item);
      return null;
    }
    
    return (
      <View style={styles.messageWrapper}>
        <View
          style={[
            styles.messageBubble,
            item.sender ? styles.sender : styles.receiver,
          ]}>
          <Text style={styles.messageText}>
            {messageText}
          </Text>
        </View>
        <Text
          style={[
            styles.timeText,
            item.sender ? styles.senderTime : styles.receiverTime,
          ]}>
          {item.time || ''}
        </Text>
      </View>
    );
  };

  return (
    <Modal 
      transparent 
      visible={visible} 
      animationType="none"
      onRequestClose={slideOutModal}
    >
      <Animated.View
        style={[
          styles.modalContainer,
          {
            transform: [
              {
                translateY: slideAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [screenHeight, 0],
                }),
              },
            ],
            // Adjust bottom position when keyboard is visible on iOS
            ...(Platform.OS === 'ios' && keyboardVisible
              ? {bottom: keyboardHeight}
              : {}),
          },
        ]}>
        <View style={styles.chatContainer}>
          <View style={styles.headerSection}>
            <View style={styles.titleContainer}>
              <Text
                style={styles.title}
                accessibilityRole="header"
                accessibilityHint={title}>
                {title}
              </Text>
              <TouchableOpacity
                style={{padding: spacing.md}}
                onPress={slideOutModal}
                accessible
                accessibilityLabel="Close chat modal">
                <IconSvgView source={close} size={15} />
              </TouchableOpacity>
            </View>
            <View style={{marginBottom: spacing.md}}>
              <FadingHorizontalLine />
            </View>
          </View>

          <View style={styles.messagesContainer}>
            <FlatList
              ref={flatListRef}
              data={processedMessages}
              extraData={processedMessages.length} // Force re-render when messages change
              keyExtractor={(item, index) => item.id || `msg-${index}`}
              renderItem={renderMessageItem}
              removeClippedSubviews={false} // Important for rendering all items
              initialNumToRender={20} // Show more items initially
              maxToRenderPerBatch={20}
              windowSize={21} // Increase window size for better rendering
              ListEmptyComponent={
                showCustomEmptyMessage ? (
                  <View style={styles.messageWrapper}>
                    <View style={[styles.messageBubble, styles.receiver]}>
                      <Text style={styles.messageText}>{emptyMessageText}</Text>
                    </View>
                    <Text style={[styles.timeText, styles.receiverTime]}>
                      {new Date().toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.emptyText}>{t('no_messages')}</Text>
                )
              }
              contentContainerStyle={styles.flatListContainer}
              onLayout={() => {
                if (processedMessages.length > 0) {
                  scrollToBottom(false);
                }
              }}
              onContentSizeChange={() => {
                if (processedMessages.length > 0) {
                  scrollToBottom(!initialScroll);
                }
              }}
            />
          </View>

          <View style={styles.inputSection}>
            <View style={styles.inputContainer}>
              <TextInput
                ref={textInputRef}
                style={[styles.textInput, { maxHeight: 100 }]}
                value={message}
                onChangeText={setMessage}
                placeholder={t('type_your_message_here')}
                placeholderTextColor="#999"
                multiline={true}
                keyboardType="default"
                returnKeyType="send" 
                onSubmitEditing={() => {
                  if (Platform.OS === 'ios' && !isSending && message.trim()) {
                    handleSendMessage();
                  }
                }}
                blurOnSubmit={false}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  isSending && styles.disabledButton,
                ]}
                onPress={handleSendMessage}
                disabled={isSending}
                accessible
                accessibilityLabel={t('send_message')}
                activeOpacity={0.7} // Prevent double tap feel
                >
                <Text style={styles.sendButtonText}>{t('send')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        {isLoading && (
          <View style={styles.loaderContainer}>
            <Image
              source={require('../../icons/LOADER.gif')}
              style={styles.loaderImage}
            />
          </View>
        )}
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: Platform.OS === 'ios' ? '60%' : '65%',
  },

  headerSection: {
    minHeight: 60,
  },

  inputSection: {
    width: '100%',
    backgroundColor: colors.black,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },

  titleContainer: {
    flexDirection: 'row',
    padding: spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  chatContainer: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: spacing.md,
    borderTopLeftRadius: spacing.md,
    borderTopRightRadius: spacing.md,
    justifyContent: 'space-between',
  },

  messagesContainer: {
    flex: 1,
  },

  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
    flex: 1,
    flexWrap: 'wrap',
    paddingRight: spacing.md,
  },

  emptyText: {
    color: colors.white,
    textAlign: 'center',
    marginVertical: spacing.md,
    fontStyle: 'italic',
    opacity: 0.7,
    fontFamily: GeistFont.regular,
  },

  flatListContainer: {
    flexGrow: 1,
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },

  messageWrapper: {
    marginBottom: spacing.sm,
  },

  // New unified input container that works consistently across platforms
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2124',
    paddingHorizontal: 10,
    paddingVertical: 5,
    minHeight: 60,
  },

  textInput: {
    flex: 1,
    color: 'white',
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontFamily: GeistFont.regular,
    minHeight: 40,
  },

  sendButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    height: 40,
    alignSelf: 'center',
  },

  sendButtonText: {
    color: 'white',
    fontSize: sizes.h6,
    fontFamily: GeistFont.bold,
  },

  messageBubble: {
    marginVertical: spacing.sm,
    padding: spacing.md,
    borderRadius: spacing.sm,
    maxWidth: '70%',
  },
  sender: {
    alignSelf: 'flex-end',
    backgroundColor: '#013A63',
  },
  receiver: {
    alignSelf: 'flex-start',
    backgroundColor: colors.darkCharcoal,
  },
  messageText: {
    color: 'white',
    fontFamily: GeistFont.regular,
  },
  timeText: {
    fontSize: sizes.h3 / 2,
    color: colors.white,
    marginBottom: spacing.sm,
    alignSelf: 'flex-end',
    fontFamily: GeistFont.regular,
  },
  senderTime: {
    alignSelf: 'flex-end',
  },
  receiverTime: {
    alignSelf: 'flex-start',
    fontFamily: GeistFont.regular,
  },
  disabledButton: {
    opacity: 0.5,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  loaderImage: {
    marginTop: spacing.md,
    width: 50,
    height: 50,
  },
});

export default ChatModal;