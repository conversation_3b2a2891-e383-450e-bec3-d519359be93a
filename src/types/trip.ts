interface Coordinates {
    latitude: number;
    longitude: number;
    latitudeDelta?: number; 
    longitudeDelta?: number; 
}

interface TripData {
    id: string;
    userId: number;
    driverId: number | null;
    otp: string | null;
    source: Coordinates;
    source_address: string;
    destination: Coordinates;
    destination_address: string;
    status: string;
    distance: string;
    pickupDistance: string;
    vehicleType: string;
    duration: number;
    fare: string;
    pickupFare: string;
    dropFare: string;
    tip: string | null;
    created_at: string;
    updated_at: string;
    ride_end_time: string | null;
    fare_changed: boolean;
    original_fare: string | null;
    driverDetails: DriverDetails | null;
}

interface RideResponse {
    data: TripData;
    error: string | null;
    errorDetails: string;
}