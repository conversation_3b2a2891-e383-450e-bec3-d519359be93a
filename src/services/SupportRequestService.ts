import {create} from 'lodash';
import Api from './Api';

export default {
  getAllTickets() {
    return Api.get(`support/tickets`);
  },

  getOneTicket(id: number) {
    return Api.get(`support/tickets/${id}`);
  },

  getSupportTemplate() {
    return Api.get(`support/templates`);
  },

  postQuerySupportTicket(payload: {subject: string; rideId: any}) {
    return Api.post(`support/tickets/find-available`, payload);
  },

  createSupportRequest(payload: {
    subject: string;
    rideId: number;
    body: string;
    files: string[];
  }) {
    return Api.post(`support/tickets`, payload);
  },

  updateSupportRequest(id: number, payload: {body: string; files: string[]}) {
    return Api.post(`support/tickets/${id}/reply`, payload);
  },

  markTicketAsRead(id: number) {
    return Api.patch(`support/tickets/${id}/read`);
  },
};
