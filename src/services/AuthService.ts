import Api from './Api';

export default {
  login(credentials: any) {
    return Api.post(`auth/login`, {
      phone: credentials.phone,
      referrerPhone: credentials.referrerPhone,
    });
  },

  verifyOtp(phone: string, otp: string) {
    return Api.post(`auth/verify`, {phone, otp});
  },

  register(user: any) {
    return Api.patch(`user/me`, user);
  },

  getUser(os: string, currentVersion: string) {
    return Api.get(`user/me`, {
      params: {
        os,
        currentVersion,
      },
    });
  },

  updateUser(userData: {
    name?: string;
    phone?: string;
    email?: string;
    profilePic?: any;
  }) {
    return Api.patch(`user/me`, userData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  emailOtp(email: string) {
    return Api.post(`/users/email/update`, {email});
  },

  verifyEmail(email: string, otp: string) {
    return Api.post(`/users/email/verify`, {email, otp});
  },

  resendEmailOtp(email: string) {
    return Api.post(`/users/email/resend`, {email});
  },

  updateFcmToken(token: string) {
    return Api.post(`fcm-tokens/upsert`, {
      entityType: 'User',
      fcmToken: token,
    });
  },

  deleteAccount(id: number) {
    return Api.delete(`user/${id}`);
  },

  updatePaymentId(upi_id: string) {
    return Api.put(`user/payment`, {upi_id});
  },

  getUserConfig() {
    return Api.put(`user-config}`);
  },
};
