import AsyncStorage from '@react-native-async-storage/async-storage';
import Config from 'react-native-config';
import axios from 'axios';
import {AppState, AppStateStatus} from 'react-native';

const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000;
let refreshTimer: NodeJS.Timeout | null = null;
let appStateSubscription: any = null;
const REFRESH_TIMESTAMP_KEY = 'token_next_refresh_timestamp';

type Token = {
  accessToken: string;
  refreshToken: string;
};

type RefreshFunction = () => Promise<Token>;

export const decodeJWT = (token: string) => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decodedPayload = JSON.parse(
      atob(payload.replace(/-/g, '+').replace(/_/g, '/')),
    );
    return decodedPayload;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return null;
  }
};

export const getTokenExpiration = (token: string): number | null => {
  const decodedToken = decodeJWT(token);

  if (decodedToken && decodedToken.exp) {
    return decodedToken.exp * 1000; //convert to milliseconds
  }

  return null;
};

export const getTimeRemaining = (token: string): number | null => {
  const expirationTime = getTokenExpiration(token);

  if (!expirationTime) {
    return null;
  }

  const timeRemaining = expirationTime - Date.now();
  return timeRemaining > 0 ? timeRemaining : null;
};

export const scheduleTokenRefresh = async (): Promise<void> => {
  try {
    if (refreshTimer) {
      clearTimeout(refreshTimer);
      refreshTimer = null;
    }

    const accessToken = await AsyncStorage.getItem('accessToken');
    if (!accessToken) {
      console.log('No access token found, not scheduling refresh');
      return;
    }

    const timeRemaining = getTimeRemaining(accessToken);

    if (!timeRemaining) {
      console.log(
        'Invalid token or already expired, attempting immediate refresh',
      );
      const refreshed = await refreshTokens();
      if (!refreshed) {
        console.log('Token refresh failed, not scheduling next refresh');
      }
      return;
    }

    const refreshIn = Math.max(timeRemaining - TOKEN_REFRESH_THRESHOLD, 0);

    const nextRefreshTimestamp = Date.now() + refreshIn;
    await AsyncStorage.setItem(
      REFRESH_TIMESTAMP_KEY,
      nextRefreshTimestamp.toString(),
    );

    console.log(`Token refresh scheduled in ${Math.round(refreshIn/1000)} seconds`);
    
    // Set up timer for when app is in foreground
    refreshTimer = setTimeout(async () => {
      await refreshTokens();
    }, refreshIn);
  } catch (error) {
    console.error('Error scheduling token refresh:', error);
  }
};

export const withTokenRefresh = (refreshFn: RefreshFunction) => {
  let isRefreshing = false;
  let refreshPromise: Promise<Token> | null = null;

  return function refreshToken(): Promise<Token> {
    if (isRefreshing && refreshPromise) {
      return refreshPromise;
    }

    isRefreshing = true;
    refreshPromise = refreshFn()
      .then(token => {
        return token;
      })
      .catch(err => {
        refreshPromise = null;
        throw err;
      })
      .finally(() => {
        isRefreshing = false;
        refreshPromise = null;
      });

    return refreshPromise;
  };
};

const performTokenRefresh = async (): Promise<Token> => {
  console.log('Performing token refresh');
  const refreshToken = await AsyncStorage.getItem('refreshToken');

  if (!refreshToken) {
    console.error('No refresh token available');
    throw new Error('No refresh token available');
  }

  try {
    const apiUrl = Config.API_URL || '';
    const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;
    const refreshEndpoint = `${baseUrl}auth/refresh`;

    const refreshResponse = await axios.get(refreshEndpoint, {
      headers: {
        Authorization: `Bearer ${refreshToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 5000,
    });

    if (refreshResponse?.data?.data?.accessToken) {
      const {accessToken, refreshToken: newRefreshToken} =
        refreshResponse.data.data;

      await AsyncStorage.setItem('accessToken', accessToken);
      await AsyncStorage.setItem('refreshToken', newRefreshToken);
      await AsyncStorage.removeItem(REFRESH_TIMESTAMP_KEY);

      return {
        accessToken,
        refreshToken: newRefreshToken,
      };
    } else {
      console.error('Invalid refresh token response format');
      throw new Error('Invalid refresh token response format');
    }
  } catch (error: any) {
    if (error.response && error.response.status === 401) {
      console.error('Refresh token unauthorized (401). Clearing tokens.');
      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      await AsyncStorage.removeItem(REFRESH_TIMESTAMP_KEY);
      throw new Error('Refresh token unauthorized');
    }
    throw error;
  }
};

const wrappedRefreshTokens = withTokenRefresh(performTokenRefresh);

export const refreshTokens = async (retryCount = 0, maxRetries = 3): Promise<boolean> => {
  try {
    console.log('Attempting to refresh tokens');
    const tokens = await wrappedRefreshTokens();

    await scheduleTokenRefresh();

    console.log('Token refreshed successfully');
    return true;
  } catch (error: any) {
    console.error('Error refreshing token:', error?.message || error);
    
    if (error.message === 'Network Error' && retryCount < maxRetries) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s...
      console.log(`Retrying token refresh in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
      
      return new Promise((resolve) => {
        setTimeout(async () => {
          const result = await refreshTokens(retryCount + 1, maxRetries);
          resolve(result);
        }, delay);
      });
    }
    
    if (error.message === 'Refresh token unauthorized') {
      console.log('User needs to log in again due to expired refresh token');
      return false;
    }
    
    return false;
  }
};


export const checkTokenRefreshNeeded = async (): Promise<boolean> => {
  try {
    const nextRefreshTimestamp = await AsyncStorage.getItem(
      REFRESH_TIMESTAMP_KEY,
    );
    if (!nextRefreshTimestamp) {
      const accessToken = await AsyncStorage.getItem('accessToken');
      if (accessToken) {
        const timeRemaining = getTimeRemaining(accessToken);
        return !timeRemaining || timeRemaining < TOKEN_REFRESH_THRESHOLD;
      }
      return false;
    }

    const refreshNeeded = parseInt(nextRefreshTimestamp) <= Date.now();
    return refreshNeeded;
  } catch (error) {
    console.error('Error checking if token refresh needed:', error);
    return false;
  }
};


const handleAppStateChange = async (nextAppState: AppStateStatus) => {
  if (nextAppState === 'active') {
    console.log('App has come to the foreground, checking token status');
    const refreshNeeded = await checkTokenRefreshNeeded();
    if (refreshNeeded) {
      console.log('Token refresh needed after app state change');
      await refreshTokens();
    } else {
      await scheduleTokenRefresh();
    }
  }
};


export const initTokenManagement = async (): Promise<void> => {
  try {
    if (!appStateSubscription) {
      appStateSubscription = AppState.addEventListener(
        'change',
        handleAppStateChange,
      );
    }

    const refreshNeeded = await checkTokenRefreshNeeded();
    if (refreshNeeded) {
      await refreshTokens();
    } else {
      await scheduleTokenRefresh();
    }
  } catch (error) {
    console.error('Error initializing token management:', error);
  }
};


export const cleanupTokenManagement = (): void => {
  if (refreshTimer) {
    clearTimeout(refreshTimer);
    refreshTimer = null;
  }

  if (appStateSubscription) {
    appStateSubscription.remove();
    appStateSubscription = null;
  }
};

export default {
  decodeJWT,
  getTokenExpiration,
  getTimeRemaining,
  scheduleTokenRefresh,
  refreshTokens,
  initTokenManagement,
  cleanupTokenManagement,
  checkTokenRefreshNeeded,
};
