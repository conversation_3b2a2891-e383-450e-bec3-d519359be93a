import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import notifee from '@notifee/react-native';
import TripService from './TripService';
import RideService from './RideService';
import { STATUS_CODE } from '../constants/constants';
import { navigationRef } from '../router/navigationService';

interface TripDetails {
  id: string;
  status: string;
  [key: string]: any;
}

interface NavigationState {
  currentRoute: string | null;
  lastNavigationTime: number;
  isNavigating: boolean;
}

class GlobalTripPollingService {
  private static instance: GlobalTripPollingService;
  private pollingInterval: NodeJS.Timeout | null = null;
  private isPolling: boolean = false;
  private lastPollTime: number = 0;
  private navigationState: NavigationState = {
    currentRoute: null,
    lastNavigationTime: 0,
    isNavigating: false,
  };
  private tripStatusCallbacks: Set<(tripDetails: TripDetails | null, status: string | null) => void> = new Set();
  private readonly POLLING_INTERVAL = 5000; // 5 seconds
  private readonly MIN_POLL_GAP = 5000; // Minimum 5 seconds between polls
  private readonly NAVIGATION_COOLDOWN = 2000; // 2 seconds cooldown between navigations

  private constructor() {}

  public static getInstance(): GlobalTripPollingService {
    if (!GlobalTripPollingService.instance) {
      GlobalTripPollingService.instance = new GlobalTripPollingService();
    }
    return GlobalTripPollingService.instance;
  }

  public async startPolling(): Promise<void> {
    if (Platform.OS !== 'ios') {
      console.log('🚫 Global polling only enabled for iOS');
      return;
    }

    if (this.isPolling) {
      console.log('🔄 Global polling already active');
      return;
    }

    try {
      const settings = await notifee.getNotificationSettings();
      const hasFullPermission = settings.authorizationStatus === 2;
      console.log('📱 Global Polling - Notification Permission Status:', hasFullPermission);

      if (!hasFullPermission) {
        console.log('🔔 Starting global trip polling - No notification permission');
        this.isPolling = true;

        // Initial fetch
        await this.fetchAndProcessTripDetails();

        // Set up interval for polling
        this.pollingInterval = setInterval(async () => {
          await this.fetchAndProcessTripDetails();
        }, this.POLLING_INTERVAL);
      } else {
        console.log('✅ Notifications enabled, global polling not needed');
      }
    } catch (error) {
      console.error('❌ Error in global polling setup:', error);
    }
  }

  public stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.isPolling = false;
    console.log('🛑 Global trip polling stopped');
  }

  public addTripStatusCallback(callback: (tripDetails: TripDetails | null, status: string | null) => void): void {
    this.tripStatusCallbacks.add(callback);
  }

  public removeTripStatusCallback(callback: (tripDetails: TripDetails | null, status: string | null) => void): void {
    this.tripStatusCallbacks.delete(callback);
  }

  private async fetchAndProcessTripDetails(): Promise<void> {
    const now = Date.now();

    // Rate limiting check
    if (now - this.lastPollTime < this.MIN_POLL_GAP) {
      console.log('⏳ Skipping poll - too soon since last poll');
      return;
    }

    this.lastPollTime = now;

    const storedTripId = await AsyncStorage.getItem('tripId');
    if (!storedTripId) {
      console.log('📍 No stored trip ID, skipping poll');
      return;
    }

    // Check for noDrivers flag - if set, stop polling
    const noDriversFlag = await AsyncStorage.getItem('noDrivers');
    if (noDriversFlag === 'true') {
      console.log('📍 NoDrivers flag detected, stopping polling');
      this.stopPolling();
      await this.clearTripStorage();
      await this.navigateToRoute('Direction');
      return;
    }

    try {
      console.log('⏰ Global polling trip details...');
      const response = await TripService.getActiveRide();

      if (response.status === STATUS_CODE.ok) {
        if (!response.data.data.activeRide) {
          await this.handleNoActiveRide();
          return;
        }

        const activeRide = response.data.data.activeRide;
        const newTripStatus = activeRide.status;

        console.log('📍 Global polling - Current trip details:', activeRide);
        console.log('📍 Global polling - New status:', newTripStatus);

        // Notify all callbacks
        this.tripStatusCallbacks.forEach(callback => {
          callback(activeRide, newTripStatus);
        });

        // Handle navigation based on status
        await this.handleTripNavigation(activeRide, newTripStatus);
      }
    } catch (err: any) {
      console.error('❌ Error in global trip polling:', err);
      
      // Handle specific error cases
      if (err?.response?.data?.code === 'no_active_trip') {
        await this.handleNoActiveRide();
      }
    }
  }

  private async handleNoActiveRide(): Promise<void> {
    console.log('📍 Global polling - No active ride found');

    try {
      await this.clearTripStorage();

      // Stop polling when no active ride
      this.stopPolling();

      // Navigate to Direction without checking last ride to prevent loops
      await this.navigateToRoute('Direction');

      console.log('📍 No active ride - stopped polling and navigated to Direction');
    } catch (error) {
      console.error('❌ Error handling no active ride:', error);
      this.stopPolling();
      await this.navigateToRoute('Direction');
    }
  }

  private async handleTripNavigation(tripDetails: TripDetails, status: string): Promise<void> {
    const navigationRoutes: { [key: string]: string } = {
      accepted: 'RideDetails',
      aborted: 'BottomTab',
      completed: 'CollectCash',
      processing: 'Confirm',
      no_drivers_available: 'Direction',
      driver_cancelled: 'Direction',
      verified: 'RideRoute',
    };

    // Special handling for terminal states that should stop polling
    if (status === 'driver_cancelled' || status === 'no_drivers_available') {
      console.log(`🛑 Terminal status detected: ${status}, clearing trip and stopping polling`);
      await this.clearTripStorage();

      // Stop polling to prevent immediate re-detection
      this.stopPolling();

      // Navigate to Direction
      await this.navigateToRoute('Direction');
      return;
    }

    const targetRoute = navigationRoutes[status];

    if (targetRoute) {
      await this.navigateToRoute(targetRoute);
    }
  }

  private async navigateToRoute(routeName: string): Promise<void> {
    const now = Date.now();
    
    // Prevent duplicate navigation
    if (
      this.navigationState.currentRoute === routeName ||
      this.navigationState.isNavigating ||
      (now - this.navigationState.lastNavigationTime < this.NAVIGATION_COOLDOWN)
    ) {
      console.log(`🚫 Skipping navigation to ${routeName} - already there or cooling down`);
      return;
    }

    this.navigationState.isNavigating = true;
    this.navigationState.lastNavigationTime = now;

    try {
      console.log(`🧭 Global polling navigating to: ${routeName}`);
      navigationRef.current?.reset({
        index: 0,
        routes: [{ name: routeName }],
      });
      
      this.navigationState.currentRoute = routeName;
    } catch (error) {
      console.error(`❌ Navigation error to ${routeName}:`, error);
    } finally {
      // Reset navigation state after a short delay
      setTimeout(() => {
        this.navigationState.isNavigating = false;
      }, 1000);
    }
  }

  private async clearTripStorage(): Promise<void> {
    try {
      const keysToRemove = [
        'tripId',
        'rideStatus',
        'driverCanceled',
        'newMessage',
        'rideAborted',
        'driverArrived',
        'driverNearby',
      ];

      await AsyncStorage.multiRemove(keysToRemove);

      const notificationId = await AsyncStorage.getItem('notificationId');
      if (notificationId) {
        await notifee.cancelDisplayedNotification(notificationId);
      }
    } catch (error) {
      console.error('❌ Error clearing trip storage:', error);
    }
  }

  public updateCurrentRoute(routeName: string): void {
    this.navigationState.currentRoute = routeName;
  }

  public isCurrentlyPolling(): boolean {
    return this.isPolling;
  }

  public async restartPollingForNewTrip(): Promise<void> {
    console.log('🔄 Restarting polling for new trip');

    // Stop any existing polling
    this.stopPolling();

    // Reset navigation state
    this.navigationState = {
      currentRoute: null,
      lastNavigationTime: 0,
      isNavigating: false,
    };

    // Reset last poll time to allow immediate polling
    this.lastPollTime = 0;

    // Start polling again
    await this.startPolling();
  }

  public async checkAndStartPollingIfNeeded(): Promise<void> {
    const storedTripId = await AsyncStorage.getItem('tripId');
    const noDriversFlag = await AsyncStorage.getItem('noDrivers');

    // Don't start polling if noDrivers flag is set
    if (noDriversFlag === 'true') {
      console.log('📍 NoDrivers flag detected, not starting polling');
      this.stopPolling();
      return;
    }

    if (storedTripId && !this.isPolling) {
      console.log('📍 Found stored trip ID, starting polling');
      await this.startPolling();
    } else if (!storedTripId && this.isPolling) {
      console.log('📍 No stored trip ID, stopping polling');
      this.stopPolling();
    }
  }
}

export default GlobalTripPollingService;
