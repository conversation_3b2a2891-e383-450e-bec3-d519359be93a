import Api from './Api';

export default {
  create(trip: any) {
    return Api.post(`trip`, trip);
  },
  update(trip: any) {
    return Api.patch(`trip`, trip);
  },
  delete(trip: any) {
    return Api.delete(`trip`, trip);
  },
  getTrips(currentPage: number, PAGE_SIZE: number) {
    return Api.get(`trip/user?page=${currentPage}&limit=${PAGE_SIZE}`);
  },
  createRatings(ratings: any) {
    return Api.post(`ratings`, ratings);
  },
  fetchTrip(id: any) {
    return Api.get(`trip/${id}`);
  },
  getActiveRide() {
    return Api.get(`ride/active`);
  },
};
