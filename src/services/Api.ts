import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import {replace} from '../router/navigationService';
import axiosRetry from 'axios-retry';
import Config from 'react-native-config';
import TokenService from './TokenService';

// Create axios instance
const axiosInstance = axios.create({
  baseURL: Config.API_URL,
  timeout: 15000, 
});

// Add retry logic
axiosRetry(axiosInstance, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: error => {
    if (
      error?.response?.status === 429 &&
      error.config?.url?.includes('refresh')
    ) {
      console.warn('429 on refresh token endpoint – skipping axios-retry');
      return false;
    }
    return (
      axiosRetry.isNetworkOrIdempotentRequestError(error) ||
      (error?.response && error.response.status >= 500) ||
      !error.response
    );
  },
  onRetry: (retryCount, error) => {
    console.log(
      `Retry attempt #${retryCount} for request: ${
        error?.config?.url ?? 'unknown URL'
      }`,
    );
  },
});

// Request interceptor to handle authorization
axiosInstance.interceptors.request.use(
  async config => {
    if (config.url && !config.url.includes('auth/refresh')) {
      try {
        const refreshNeeded = await TokenService.checkTokenRefreshNeeded();
        if (refreshNeeded) {
          console.log('Token refresh needed before request');
          await TokenService.refreshTokens();
        }
        
        const accessToken = await AsyncStorage.getItem('accessToken');
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      } catch (error) {
        console.error('Error in token refresh check:', error);
        const accessToken = await AsyncStorage.getItem('accessToken');
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      }
    } else {
      const accessToken = await AsyncStorage.getItem('accessToken');
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
    }

    // Automatically set Content-Type to multipart/form-data if data is FormData
    if (config.data instanceof FormData) {
      config.headers['Content-Type'] = 'multipart/form-data';
    }

    return config;
  },
  error => Promise.reject(error),
);

async function handleLogoutAndRedirect(
  sourceId = 'unknown',
  errorMsg = 'Session expired',
) {
  try {
    const accessToken = await AsyncStorage.getItem('accessToken');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    const userIdString = await AsyncStorage.getItem('userId');
    const userId = userIdString ? parseInt(userIdString, 10) : null;
    const userType = 'user';

    const apiUrl = Config.API_URL || '';
    const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;

    const formattedErrorMsg =
      typeof errorMsg === 'object' && errorMsg !== null
        ? JSON.stringify(errorMsg)
        : String(errorMsg);

    try {
      await axios.post(`${baseUrl}logout-logs`, {
        accessToken,
        refreshToken,
        userId,
        userType,
        errorMessage: `[Source:${sourceId}] ${formattedErrorMsg}`,
      });
    } catch (logError: any) {
      console.log(
        'Failed to log refresh token failure:',
        logError?.message || logError,
      );
    }

    await AsyncStorage.clear();
    replace('Start');

    return Promise.reject(
      new Error(
        `[Source:${sourceId}] ${formattedErrorMsg}. Redirected to Start.`,
      ),
    );
  } catch (error: any) {
    console.error('Error during logout process:', error);
    await AsyncStorage.clear();
    replace('Start');
    return Promise.reject(
      new Error(`Failed logout process: ${error?.message || 'Unknown error'}`),
    );
  }
}

// Response interceptor
axiosInstance.interceptors.response.use(
  response => response,
  async (error: any) => {
    const originalRequest = error.config;

    // Handle 401 errors and token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');

        if (!refreshToken) throw new Error('Refresh token not found');

        const apiUrl = Config.API_URL || '';
        const baseUrl = apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`;
        const refreshEndpoint = `${baseUrl}auth/refresh`;

        let refreshResponse;

        try {
          refreshResponse = await axios.get(refreshEndpoint, {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
              'Content-Type': 'application/json',
            },
            timeout: 5000, 
          });
        } catch (refreshError: any) {
          if (refreshError.response?.status === 429) {
            console.warn('429 on refresh token. Retrying after delay...');
            const delay =
              parseInt(refreshError.response.headers['retry-after']) || 3;
            await new Promise(resolve => setTimeout(resolve, delay * 1000));

            try {
              console.log(
                'Retrying token refresh after rate limit cooldown...',
              );
              refreshResponse = await axios.get(refreshEndpoint, {
                headers: {
                  Authorization: `Bearer ${refreshToken}`,
                  'Content-Type': 'application/json',
                },
                timeout: 10000, // 10 seconds timeout for retry
              });
              console.log('Retry succeeded');
            } catch (finalRefreshError: any) {
              console.error(
                'Final refresh attempt failed:',
                finalRefreshError.response?.status || finalRefreshError.message,
              );
              return handleLogoutAndRedirect(
                '1',
                `Rate limit retry failed: ${
                  finalRefreshError.message || 'Unknown error'
                }`,
              );
            }
          } else if (refreshError.response?.status === 401) {
            console.error(
              'Refresh token rejected (401 Unauthorized). Token may be expired or invalid.',
            );
            return handleLogoutAndRedirect(
              '2',
              'Refresh token rejected (401 Unauthorized)',
            );
          } else {
            console.error(
              'Refresh failed:',
              refreshError.message || refreshError,
            );
            return handleLogoutAndRedirect(
              '3',
              `Refresh failed: ${refreshError.message || 'Unknown error'}`,
            );
          }
        }

        if (!refreshResponse?.data?.data?.accessToken) {
          console.error('Invalid refresh token response format');
          return handleLogoutAndRedirect(
            '5',
            'Invalid refresh token response format',
          );
        }

        const {accessToken, refreshToken: newRefreshToken} =
          refreshResponse.data.data;

        await AsyncStorage.setItem('accessToken', accessToken);
        await AsyncStorage.setItem('refreshToken', newRefreshToken);

        // Update the request headers with new token
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return axiosInstance(originalRequest);
      } catch (err: any) {
        return handleLogoutAndRedirect(
          '4',
          `Token refresh process error: ${err?.message || 'Unknown error'}`,
        );
      }
    }

    // Handle repeated failures after retry attempts
    if (
      error.config &&
      error.config['axios-retry'] &&
      error.config['axios-retry'].retryCount >= 3
    ) {
      console.error('All retries failed:', error);

      if (!error.response || error.message?.includes('Network Error')) {
        console.warn('Network error occurred');
      } else if (error.response?.status >= 500) {
        console.warn('Unexpected server error. Redirecting to fallback screen');
        replace('Fallback');
      }
    }

    if (error.response?.status === 429) {
      console.warn(
        `429 Too Many Requests for endpoint: ${error.config?.url || 'unknown'}`,
      );
    }

    return Promise.reject(error);
  },
);

const Api = {
  instance: axiosInstance,

  get: <T>(url: string, config = {}) => axiosInstance.get<T>(url, config),

  post: <T>(url: string, data = {}, config = {}) =>
    axiosInstance.post<T>(url, data, config),

  put: <T>(url: string, data = {}, config = {}) =>
    axiosInstance.put<T>(url, data, config),
    
  patch: <T>(url: string, data = {}, config = {}) =>
    axiosInstance.patch<T>(url, data, config),

  delete: <T>(url: string, config = {}) => axiosInstance.delete<T>(url, config),

  checkConnectivity: async () => {
    try {
      await axiosInstance.get('/ping', {timeout: 5000});
      return true;
    } catch (error) {
      return false;
    }
  },
};

export default Api;
