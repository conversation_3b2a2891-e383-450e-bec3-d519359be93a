import Api from './Api';

interface Location {
  latitude: number;
  longitude: number;
}

interface RideRequestPayload {
  source: Location;
  destination: Location;
  source_address: string;
  destination_address: string;
  status: string;
  vehicleType: string;
}

const defaultHeaders = {
  'Keep-Alive': 'timeout=120',
};

export default {
  sendRideRequest(payload: RideRequestPayload) {
    return Api.post('ride/request', payload, {headers: defaultHeaders});
  },

  cancelRide() {
    return Api.post(
      '/ride/cancel-session',
      {
        status: 'user_cancelled',
      },
      {headers: defaultHeaders},
    );
  },

  getDriver() {
    return Api.get(`/ride/driver-session-info`);
  },

  getDriverDetail(id: number) {
    return Api.get(`/ride/${id}/driver`);
  },

  getPolyline() {
    return Api.get(`/ride/polyline-session`);
  },

  getLastRide() {
    return Api.get('/trip/user/last');
  },
};
