import GlobalTripPollingService from '../GlobalTripPollingService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import notifee from '@notifee/react-native';
import TripService from '../TripService';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('@notifee/react-native');
jest.mock('../TripService');
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));
jest.mock('../../router/navigationService', () => ({
  navigationRef: {
    current: {
      reset: jest.fn(),
    },
  },
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockNotifee = notifee as jest.Mocked<typeof notifee>;
const mockTripService = TripService as jest.Mocked<typeof TripService>;

describe('GlobalTripPollingService', () => {
  let service: GlobalTripPollingService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = GlobalTripPollingService.getInstance();
    
    // Reset singleton instance for testing
    (GlobalTripPollingService as any).instance = undefined;
    service = GlobalTripPollingService.getInstance();
  });

  afterEach(() => {
    service.stopPolling();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = GlobalTripPollingService.getInstance();
      const instance2 = GlobalTripPollingService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('iOS Polling', () => {
    it('should start polling when iOS notifications are disabled', async () => {
      mockNotifee.getNotificationSettings.mockResolvedValue({
        authorizationStatus: 1, // Not authorized
      } as any);
      mockAsyncStorage.getItem.mockResolvedValue('123');
      mockTripService.getActiveRide.mockResolvedValue({
        status: 200,
        data: {
          data: {
            activeRide: {
              id: '123',
              status: 'accepted',
            },
          },
        },
      } as any);

      await service.startPolling();
      expect(service.isCurrentlyPolling()).toBe(true);
    });

    it('should not start polling when iOS notifications are enabled', async () => {
      mockNotifee.getNotificationSettings.mockResolvedValue({
        authorizationStatus: 2, // Authorized
      } as any);

      await service.startPolling();
      expect(service.isCurrentlyPolling()).toBe(false);
    });

    it('should not start polling on Android', async () => {
      (Platform as any).OS = 'android';
      
      await service.startPolling();
      expect(service.isCurrentlyPolling()).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should respect minimum poll gap', async () => {
      mockNotifee.getNotificationSettings.mockResolvedValue({
        authorizationStatus: 1,
      } as any);
      mockAsyncStorage.getItem.mockResolvedValue('123');

      const callback = jest.fn();
      service.addTripStatusCallback(callback);

      await service.startPolling();
      
      // Should not call API too frequently
      expect(mockTripService.getActiveRide).toHaveBeenCalledTimes(1);
    });
  });

  describe('Navigation Prevention', () => {
    it('should prevent duplicate navigation to same route', async () => {
      const mockNavigationRef = require('../../router/navigationService').navigationRef;
      
      // Simulate navigation to same route twice
      await (service as any).navigateToRoute('RideDetails');
      await (service as any).navigateToRoute('RideDetails');
      
      // Should only navigate once
      expect(mockNavigationRef.current.reset).toHaveBeenCalledTimes(1);
    });

    it('should respect navigation cooldown', async () => {
      const mockNavigationRef = require('../../router/navigationService').navigationRef;
      
      // Simulate rapid navigation attempts
      await (service as any).navigateToRoute('RideDetails');
      await (service as any).navigateToRoute('CollectCash');
      
      // Should only navigate once due to cooldown
      expect(mockNavigationRef.current.reset).toHaveBeenCalledTimes(1);
    });
  });

  describe('Callback Management', () => {
    it('should add and remove callbacks correctly', () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();

      service.addTripStatusCallback(callback1);
      service.addTripStatusCallback(callback2);

      // Simulate trip update
      (service as any).tripStatusCallbacks.forEach((cb: any) => cb({ id: '123', status: 'accepted' }, 'accepted'));

      expect(callback1).toHaveBeenCalledWith({ id: '123', status: 'accepted' }, 'accepted');
      expect(callback2).toHaveBeenCalledWith({ id: '123', status: 'accepted' }, 'accepted');

      service.removeTripStatusCallback(callback1);
      
      // Only callback2 should be called now
      (service as any).tripStatusCallbacks.forEach((cb: any) => cb({ id: '456', status: 'completed' }, 'completed'));
      
      expect(callback1).toHaveBeenCalledTimes(1); // Still only called once
      expect(callback2).toHaveBeenCalledTimes(2); // Called twice
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockNotifee.getNotificationSettings.mockResolvedValue({
        authorizationStatus: 1,
      } as any);
      mockAsyncStorage.getItem.mockResolvedValue('123');
      mockTripService.getActiveRide.mockRejectedValue(new Error('API Error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await service.startPolling();
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error in global polling setup')
      );

      consoleSpy.mockRestore();
    });
  });
});
