import React, {useMemo, useState} from 'react';
import {
  Image,
  ImageBackground,
  Text,
  ScrollView,
  TouchableOpacity,
  View,
  Keyboard,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import {colors, GeistFont, images, sizes} from '../../constants';
import styles from './UserSetupStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import Dropdown from '../../components/DropDown/DropDown';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Back from '../../icons/back.svg';
import AuthService from '../../services/AuthService';
import {useToast} from '../../components/Toast/Toast';
import diamond from '../../icons/diamond.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {STATUS_CODE} from '../../constants/constants';
import ReferralService from '../../services/ReferralService';
import {spacing} from '../../constants/theme';

interface UserSetupProps {
  navigation: any;
  route: any;
}

const UserSetup: React.FC<UserSetupProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const [name, setName] = useState('');
  const [gender, setGender] = useState<Record<string, string>>();
  const {showToast} = useToast();
  const [error, setError] = useState<any>('null');
  const [referralNumber, setReferralNumber] = useState('+91 ');
  const [referralError, setReferralError] = useState<string>('');
  const [referralValid, setReferralValid] = useState<boolean>(false);
  const [referralDriverName, setReferralDriverName] = useState<string>('');
  const [isValidatingReferral, setIsValidatingReferral] =
    useState<boolean>(false);
  const [referralVerified, setReferralVerified] = useState(false);

  const data = [
    {label: t('men'), value: '1'},
    {label: t('women'), value: '2'},
    {label: t('not_specified'), value: '3'},
  ];

  const handleReferralChange = (input: string) => {
    if (!input.startsWith('+91 ')) {
      setReferralNumber('+91 ');
      setReferralVerified(false);
      setReferralValid(false);
      setReferralError('');
      return;
    }

    const numberPart = input.substring(4);
    const digitsOnly = numberPart.replace(/[^0-9]/g, '');

    if (digitsOnly.length <= 10) {
      setReferralNumber('+91 ' + digitsOnly);

      if (referralVerified) {
        setReferralVerified(false);
        setReferralValid(false);
        if (digitsOnly.length < 10) {
          setReferralError(t('complete_phone_number'));
        } else {
          setReferralError(t('verify_again'));
        }
      }
    }
  };

  const verifyReferral = async () => {
    if (!/^\+91\s[0-9]{10}$/.test(referralNumber)) {
      setReferralError(t('invalid_phone_format'));
      return;
    }

    try {
      setIsValidatingReferral(true);
      setReferralError(t('validating_referral'));
      Keyboard.dismiss();

      const phoneToValidate = referralNumber.replace(/\s/g, '');
      const response = await ReferralService.validateReferral(phoneToValidate);

      if (response.data.data && response.data.data.valid) {
        if (response.data.data.referrerName) {
          setReferralError('');
          setReferralValid(true);
          setReferralVerified(true);
          setReferralDriverName(response.data.data.referrerName);
        } else {
          setReferralError(t('no_such_user_exist'));
          setReferralValid(false);
          setReferralVerified(false);
        }
      } else {
        setReferralValid(false);
        setReferralVerified(false);
        setReferralError(t('referral_invalid'));
      }
    } catch (err) {
      setReferralValid(false);
      setReferralVerified(false);
      setReferralError(t('referral_invalid'));
    } finally {
      setIsValidatingReferral(false);
    }
  };

  const register = async () => {
    const referralToSend = referralNumber.replace(/\s/g, '');

    try {
      if (
        name &&
        gender &&
        (!referralNumber || referralNumber === '+91 ' || referralValid)
      ) {
        const response = await AuthService.register({
          name: name,
          gender: gender.label,
          referrerPhone: referralNumber !== '+91 ' ? referralToSend : '',
        });
        if (response?.status === STATUS_CODE.ok) {
          navigation.navigate('Welcome');
          showToast(t('user_register_sucess'), 'success');
        }
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'user_update_failed' &&
          showToast(t('user_update_failed'), 'failure');
      }
    }
  };

  const handleDropdown = (selected: {label: string; value: string}) => {
    setGender(selected);
  };

  const nameError = useMemo(() => {
    let error: string = '';
    error = name.length === 1 ? t('name_error') : '';
    return error;
  }, [name]);

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.iconContainer}>
          <TouchableOpacity
            style={styles.icon}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={Back} />
          </TouchableOpacity>
          <FlexContainer direction="row" justifyContent="center">
            <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
            <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
            <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
          </FlexContainer>
        </View>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{flex: 1}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View>
              <Text numberOfLines={2} style={styles.title}>
                {t('userSetup_title')}
              </Text>
            </View>
            <View>
              <Input
                inputTitle={t('your_name')}
                placeholder="eg: John Doe"
                onChange={setName}
                value={name}
                error={nameError}
                message={
                  <FlexContainer direction="row" flex={0}>
                    <Image source={images.info} />
                    <Text numberOfLines={1} style={styles.message}>
                      {t('help_driver_note')}
                    </Text>
                  </FlexContainer>
                }
              />
            </View>
            <View>
              <Dropdown
                label={t('select')}
                data={data}
                onSelect={handleDropdown}
                title={t('your_gender')}
              />
            </View>

            {error && error !== 'null' && (
              <FlexContainer justifyContent="center" alignItems="center">
                <Text style={{color: 'red'}}>{error}</Text>
              </FlexContainer>
            )}

            <View style={{marginVertical: spacing.lg}}>
              <Input
                inputTitle={t('referral_number')}
                onChange={handleReferralChange}
                value={referralNumber}
                keyboardType="numeric"
                maxLength={14}
                error={referralError}
                showVerifyButton={true}
                verifyButtonText={t('verify')}
                onVerify={verifyReferral}
                disabled={isValidatingReferral}
                verifyButtonDisabled={
                  isValidatingReferral ||
                  !/^\+91\s[0-9]{10}$/.test(referralNumber)
                }
              />
              {referralVerified && referralDriverName && (
                <View
                  style={{
                    marginTop: spacing.md,
                    padding: spacing.sm,
                    backgroundColor: colors.green,
                    borderRadius: spacing.xs,
                  }}>
                  <Text style={additionalStyles.successMessage}>
                    {t('referrer_name', {name: referralDriverName})}
                  </Text>
                </View>
              )}
            </View>

            <FlexContainer justifyContent="flex-end">
              <Button
                title={t('continue')}
                style={styles.continueBtn}
                disabled={
                  !name ||
                  !gender ||
                  !!nameError ||
                  (referralNumber &&
                    referralNumber !== '+91 ' &&
                    !referralValid)
                }
                onPress={register}
              />
            </FlexContainer>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const additionalStyles = StyleSheet.create({
  referralTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  successMessage: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
  },
});

export default UserSetup;
