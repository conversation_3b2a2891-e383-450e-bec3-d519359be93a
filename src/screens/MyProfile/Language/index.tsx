import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ImageBackground,
  ScrollView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import styles from './LanguageStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import Dropdown from '../../../components/DropDown/DropDown';
import Button from '../../../components/Button/Button';
import {SafeAreaView} from 'react-native-safe-area-context';

const LANGUAGE_KEY = '@app_language';

interface LanguageScreenProps {
  navigation: any;
}

const Language: React.FC<LanguageScreenProps> = ({navigation}) => {
  const {t, i18n} = useTranslation();
  const [language, setLanguage] = useState<string | null>(i18n.language);
  const [isLoading, setIsLoading] = useState(true);

  const data = [
    {label: t('english'), value: 'en'},
    {label: t('malayalam'), value: 'ma'},
    {label: t('tamil'), value: 'ta'},
  ];

  useEffect(() => {
    loadSavedLanguage();
  }, []);

  const loadSavedLanguage = async () => {
    try {
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
      if (savedLanguage) {
        setLanguage(savedLanguage);
        i18n.changeLanguage(savedLanguage);
      }
    } catch (error) {
      console.error('Error loading saved language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedLanguage = data.find(item => item.value === language);

  const handleDropdown = (selected: {label: string; value: string}) => {
    setLanguage(selected.value);
  };

  const handleUpdateLanguage = async () => {
    if (language) {
      try {
        await AsyncStorage.setItem(LANGUAGE_KEY, language);
        i18n.changeLanguage(language);
        navigation.goBack();
      } catch (error) {
        console.error('Error saving language:', error);
      }
    }
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps="handled">
          <View style={styles.titleContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
            <Text style={styles.title}>{t('language_settings')}</Text>
          </View>
          <FadingHorizontalLine />
          <Text style={styles.list}>{t('app_language')}</Text>
          <View>
            <Dropdown
              label={selectedLanguage?.label || ''}
              data={data}
              onSelect={handleDropdown}
              title={t('set_language')}
              dropdownStyle={{width: '100%'}}
            />
          </View>
          <FlexContainer justifyContent="flex-end">
            <Button
              title={t('update')}
              disabled={!language}
              onPress={handleUpdateLanguage}
            />
          </FlexContainer>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Language;
