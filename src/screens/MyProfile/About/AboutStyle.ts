import { StyleSheet } from 'react-native';
import { spacing } from '../../../constants/theme';
import { EBGaramondFont, GeistFont, colors, sizes } from '../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    marginVertical: spacing.xl
  },

  title: {
    marginVertical: spacing.xs,
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },

  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.md * 1.5,
  },

  subtitle: {
    fontSize: sizes.h2,
    fontFamily: EBGaramondFont.EBbold,
    color: colors.lightGrey,
    marginBottom: spacing.md,
  },

  description: {
    color: colors.grey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
  },
});
