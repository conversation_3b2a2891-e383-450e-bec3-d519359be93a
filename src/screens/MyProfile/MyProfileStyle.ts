import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.md * 1.5,
    marginTop: spacing.xxl,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.xxl,
    alignItems: 'center',
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },

  imageWrapper: {
    width: spacing.xl * 3,
    height: spacing.xl * 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.darkGrey,
    marginTop: spacing.xxl * 1.5,
  },
  imageContainer: {
    resizeMode: 'cover',
    borderWidth: spacing.xxs,
    borderColor: colors.white,
    width: spacing.xl * 3,
    height: spacing.xl * 3,
  },
  driverName: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    marginVertical: spacing.md,
  },
  subtitle: {
    backgroundColor: colors.darkGrey,
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    color: colors.white,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.xs,
  },
  listTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.white,
    minHeight: 30,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  continueBtn: {
    marginTop: spacing.xxl,
  },
  listIcon: {
    marginRight: spacing.xl,
    padding: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.sm,
    minHeight: 30,
  },
  deleteButton: {
    marginVertical: spacing.md,
  },
  versionText: {
    textAlign: 'center',
    color: colors.lightGrey,
    fontSize: sizes.body,
    marginTop: spacing.xl,
    marginBottom: spacing.xl,
  },
});
