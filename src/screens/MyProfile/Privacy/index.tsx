import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  Share,
} from 'react-native';
import styles from './PrivacyStyle';
import { useTranslation } from 'react-i18next';
import back from '../../../icons/back.svg';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';

interface PrivacyPolicyScreenProps {
  navigation: {
    goBack: () => void;
  };
}

const PrivacyPolicy: React.FC<PrivacyPolicyScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const [imageError, setImageError] = useState(false);


  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }} onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('privacy_policy')}</Text>
        </View>
        <FadingHorizontalLine />
        <ScrollView showsVerticalScrollIndicator={false} style={{flexGrow:1}}>
          <Text style={styles.subtitle}>maptoed for the Journey</Text>
          <Text style={styles.description}>
            At mapto, we believe that every journey matters, whether it’s a quick trip across town or an adventure to new destinations. Our taxi booking app is designed to provide seamless, reliable, and affordable rides that fit perfectly into your daily life. With just a few taps, we connect you to drivers who are committed to getting you to your destination safely and on time.
            What sets mapto apart is oAt mapto, we believe that every journey matters, whether it’s a quick trip across town or an adventure to new destinations. Our taxi booking app is designed to provide seamless, reliable, and affordable rides that fit perfectly into your daily life. With just a few taps, we connect you to drivers who are committed to getting you to your destination safely and on time.
            What sets mapto apart is oAt mapto, we believe that every journey matters, whether it’s a quick trip across town or an adventure to new destinations. Our taxi booking app is designed to provide seamless, reliable, and affordable rides that fit perfectly into your daily life. With just a few taps, we connect you to drivers who are committed to getting you to your destination safely and on time.
            What sets mapto apart is oAt mapto, we believe that every journey matters, whether it’s a quick trip across town or an adventure to new destinations. Our taxi booking app is designed to provide seamless, reliable, and affordable rides that fit perfectly into your daily life. With just a few taps, we connect you to drivers who are committed to getting you to your destination safely and on time.
            What sets mapto apart is o
            At mapto, we believe that every journey matters, whether it’s a quick trip across town or an adventure to new destinations. Our taxi booking app is designed to provide seamless, reliable, and affordable rides that fit perfectly into your daily life. With just a few taps, we connect you to drivers who are committed to getting you to your destination safely and on time.
            What sets mapto apart is our dedication to innovation and convenience. We leverage cutting-edge technology to ensure smooth ride bookings, real-time tracking, and transparent pricing, giving you complete control over your travel experience. But beyond the technology, we prioritize your safety and comfort by partnering with professional drivers and offering round-the-clock support.
            At mapto, we see transportation as more than just getting from point A to B—it’s about building connections and creating meaningful experiences. We’re here to simplify your life, inspire confidence in your travels, and mapto your journey toward what truly matters. With us, every ride becomes a step toward something greater.</Text>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default PrivacyPolicy;
