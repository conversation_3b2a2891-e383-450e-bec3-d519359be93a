import React, {useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  Linking,
  Platform,
  NativeModules,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import images from '../../constants/images';
import styles from './MyProfileStyle';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import arrowIcon from '../../icons/arrow_icon.svg';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {spacing} from '../../constants/theme';
import {useLoader} from '../../hooks/useLoader';
import ConfirmationModal from '../../components/ConfirmationModal';
import {useToast} from '../../components/Toast/Toast';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ScrollView} from 'react-native-gesture-handler';
import {useAuth} from '../../hooks/useAuth';
import {useUser} from '../../hooks/useUser';
import languageIcon from '../../icons/language.svg';
import editIcon from '../../icons/edit.svg';
import aboutIcon from '../../icons/about.svg';
import helpIcon from '../../icons/help.svg';
import policyIcon from '../../icons/policy.svg';
import termsIcon from '../../icons/terms.svg';
import walletIcon from '../../icons/wallet.svg';
import PolicyService, {Policy} from '../../services/PolicyService';
import {useFocusEffect} from '@react-navigation/native';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import AuthService from '../../services/AuthService';
import {useGeofence} from '../../hooks/useGeofence';

interface MyProfileScreenProps {
  navigation: any;
}
const MyProfile: React.FC<MyProfileScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {user, setUser, fetchUser, setUnreadSupportMsg} = useUser();
  const {showToast} = useToast();
  const {setIsAuthenticated} = useAuth();
  const [modalVisible, setModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [previousUnreadStatus, setPreviousUnreadStatus] = useState<
    boolean | null
  >(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const {stopGeofencing} = useGeofence();

  useEffect(() => {
    fetchUser();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      if (!isLoggingOut) {
        fetchPolicies();
        if (user) {
          const currentUnreadStatus = user.hasUnreadSupportMessages;
          if (previousUnreadStatus !== currentUnreadStatus) {
            setUnreadSupportMsg(currentUnreadStatus);
            if (currentUnreadStatus) {
              showToast(t('unread_support_msg'), 'success');
            }

            setPreviousUnreadStatus(currentUnreadStatus);
          }
        }
      }
    }, [user, previousUnreadStatus, isLoggingOut]),
  );

  const fetchPolicies = async () => {
    if (isLoggingOut) return;

    try {
      showLoader();
      const response = await PolicyService.getPolicies();
      if (response.data && response.data.data) {
        setPolicies(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
    } finally {
      hideLoader();
    }
  };

  const handlePolicyPress = () => {
    if (policies.length > 0) {
      const policyItem = policies.find(policy => policy.name === 'Policy');

      if (policyItem && policyItem.link) {
        Linking.openURL(policyItem.link).catch(() => {
          showToast(t('could_not_open_link'));
        });
      } else {
        navigation.navigate('Policy');
      }
    } else {
      navigation.navigate('Policy');
    }
  };

  const handleTermsPress = () => {
    if (policies.length > 0) {
      const termsItem = policies.find(policy => policy.name === 'Terms');

      if (termsItem && termsItem.link) {
        Linking.openURL(termsItem.link).catch(() => {
          showToast(t('could_not_open_link'));
        });
      } else {
        navigation.navigate('Performance');
      }
    } else {
      navigation.navigate('Performance');
    }
  };

  const handleLogout = async () => {
    try {
      showLoader();
      setIsLoggingOut(true);

      stopGeofencing();

      await AsyncStorage.setItem('isLoggingOut', 'true');
      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      await AsyncStorage.clear();
      setModalVisible(false);
      setUser(null);
      setIsAuthenticated(false);
      navigation.reset({
        index: 0,
        routes: [{name: 'Start'}],
      });
    } catch (err: any) {
      showToast(t('try_again_later'));
    } finally {
      hideLoader();
    }
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const handleDeleteAccount = async () => {
    try {
      showLoader();
      if (user?.id !== undefined) {
        const response = await AuthService.deleteAccount(user.id);
        if (response.status === 204) {
          await AsyncStorage.clear();
          setIsLoggingOut(true);
          setDeleteModalVisible(false);
          setUser(null);
          setIsAuthenticated(false);
          navigation.reset({
            index: 0,
            routes: [{name: 'Start'}],
          });
          showToast(t('account_deleted'), 'success');
        } else {
          throw new Error('Failed to delete account');
        }
      } else {
        throw new Error('User ID is undefined');
      }
    } catch (err: any) {
      showToast(t('delete_account_failed'));
    } finally {
      hideLoader();
    }
  };

  const renderListItem = (title: string, icon: any, onPress: () => void) => (
    <TouchableOpacity style={styles.listItem} onPress={onPress}>
      <View
        style={{
          flexDirection: 'row',
          marginVertical: spacing.lg,
          alignItems: 'center',
          flex: 0.8,
        }}>
        <View style={styles.listIcon}>
          <IconSvgView size={20} source={icon} />
        </View>
        <Text style={[styles.listTxt, {flexShrink: 1}]} numberOfLines={3}>
          {title}
        </Text>
      </View>
      <View>
        <IconSvgView size={14} source={arrowIcon} />
      </View>
    </TouchableOpacity>
  );

  return (
    <>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <View style={styles.safeArea}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{t('profile')}</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('AccountInfo')}>
              <IconSvgView source={editIcon} />
            </TouchableOpacity>
          </View>
          <FadingHorizontalLine />
          <View style={{alignItems: 'center'}}>
            <View style={styles.imageWrapper}>
              <Image
                source={
                  user?.profile_pic && !imageError
                    ? {uri: `${user.profile_pic}?t=${new Date().getTime()}`}
                    : images.user
                }
                style={styles.imageContainer}
                resizeMode="cover"
                onError={() => setImageError(true)}
              />
            </View>

            <Text style={styles.driverName} numberOfLines={2}>
              {user?.name ?? '-'}
            </Text>
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{flexGrow: 1}}>
            {renderListItem(t('language'), languageIcon, () =>
              navigation.navigate('Language'),
            )}
            <FadingHorizontalLine />
            {renderListItem(t('wallet'), walletIcon, () =>
              navigation.navigate('Wallet'),
            )}
            <FadingHorizontalLine />
            {renderListItem(t('terms_conditions'), termsIcon, handleTermsPress)}
            <FadingHorizontalLine />

            {renderListItem(t('privacy_policy'), policyIcon, handlePolicyPress)}
            <FadingHorizontalLine />
            {renderListItem(t('help_support'), helpIcon, () =>
              navigation.navigate('Help'),
            )}

            <View style={{justifyContent: 'flex-end'}}>
              <Button
                title={t('logout')}
                style={styles.continueBtn}
                onPress={() => {
                  setModalVisible(true);
                }}
              />
              <HollowButton
                title={t('delete_account')}
                style={[styles.deleteButton]}
                onPress={() => {
                  setDeleteModalVisible(true);
                }}
              />
              <Text style={styles.versionText}>
                {t('version')} {DeviceInfo.getVersion()}
              </Text>
            </View>
          </ScrollView>
        </View>
      </ImageBackground>

      <ConfirmationModal
        title={t('logout')}
        visible={modalVisible}
        message={'Are you sure you want to log out?'}
        onConfirm={handleLogout}
        onCancel={handleCancel}
      />
      <ConfirmationModal
        title={t('delete_account')}
        visible={deleteModalVisible}
        message={
          'Are you sure you want to delete your account? This action cannot be undone.'
        }
        onConfirm={handleDeleteAccount}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </>
  );
};

export default MyProfile;
