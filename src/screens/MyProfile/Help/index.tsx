import React, {useCallback, useEffect, useState} from 'react';
import {View, TouchableOpacity, Text, ScrollView, Image} from 'react-native';
import {useTranslation} from 'react-i18next';
import helpIcon from '../../../assets/icons/help.svg';
import styles from './HelpStyle';
import Button from '../../../components/Button/Button';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import TripService from '../../../services/TripService';
import {STATUS_CODE} from '../../../constants/constants';
import {useLoader} from '../../../hooks/useLoader';
import {useToast} from '../../../components/Toast/Toast';
import back from '../../../icons/back.svg';

interface Ride {
  id: number;
  created_at: string;
  updated_at: string;
  driverId: number;
  userId: number;
  status: string;
  source: {latitude: number; longitude: number};
  destination: {latitude: number; longitude: number};
  source_address: string;
  destination_address: string;
  distance: number;
  duration: number;
  fare: number;
  tip: number | null;
  vehicleType: string;
}

enum RideStatus {
  Completed = 'completed',
  DriverCancelled = 'driver_cancelled',
  UserCancelled = 'user_cancelled',
  Aborted = 'aborted',
}

import {NavigationProp} from '@react-navigation/native';
import images from '../../../constants/images';
import {formatTime} from '../../../utils/TImeUtils';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import {spacing} from '../../../constants/theme';
import {colors} from '../../../constants';
import {SafeAreaView} from 'react-native-safe-area-context';

const Help = ({navigation}: {navigation: NavigationProp<any>}) => {
  const {t} = useTranslation();
  const [lastRide, setLastRide] = useState<Ride | null>(null);
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const [hasNoRides, setHasNoRides] = useState(false);

  const fetchLastRide = async () => {
    try {
      showLoader();
      const response = await TripService.getTrips(1, 20);

      if (response.data?.data?.data && response.data.data.data.length > 0) {
        console.log('Fetched rides:', response.data.data.data.length);

        const filteredRides = response.data.data.data.filter(
          (ride: Ride) =>
            ride.status === RideStatus.Completed ||
            ride.status === RideStatus.DriverCancelled ||
            ride.status === RideStatus.UserCancelled ||
            ride.status === RideStatus.Aborted,
        );

        console.log('Filtered rides:', filteredRides.length);

        if (filteredRides.length > 0) {
          console.log(
            'Setting last ride with status:',
            filteredRides[0].status,
          );
          setLastRide(filteredRides[0]);
          return filteredRides[0];
        } else {
          console.log('No rides with matching status found');
          setHasNoRides(true);
        }
      } else {
        console.log('No rides found in response');
        setHasNoRides(true);
      }
      return null;
    } catch (err: any) {
      console.error('Error fetching rides:', err);
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        setHasNoRides(true);
        return null;
      } else if (STATUS_CODE.bad_request) {
        code === 'getting_trips_failed' &&
          showToast(t('error_fetching_trip'), 'failure');
        setHasNoRides(true);
      }
      return null;
    } finally {
      hideLoader();
    }
  };

  useEffect(() => {
    fetchLastRide();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getVehicleImage = useCallback((vehicleType: string) => {
    const vehicleImages: {[key: string]: any} = {
      AUTORICKSHAW: images.auto,
    };
    return vehicleImages[vehicleType] || images.auto;
  }, []);

  const renderRecentRide = () => {
    if (lastRide) {
      return (
        <>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginVertical: spacing.xxl,
            }}>
            <Text style={styles.subtitle}>{t('your_last_ride')}</Text>

            <TouchableOpacity
              onPress={() => navigation.navigate('MyRide', {fromHelp: true})}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
                width: '50%',
              }}>
              <Text style={styles.viewAllText}>{t('view_all_rides')}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.rideContainerTop}>
            <View style={{flexDirection: 'row'}}>
              <Image
                source={getVehicleImage(lastRide.vehicleType)}
                style={styles.vehicleIcon}
              />
              <View style={{alignItems: 'flex-start', margin: 12}}>
                <Text style={styles.dateText}>
                  {formatTime(new Date(lastRide?.updated_at))}
                </Text>

                <Text style={styles.locationLabel}>
                  {formatDate(lastRide.created_at)}
                </Text>
              </View>
            </View>
            <View style={styles.detailsContainer}>
              <Text style={styles.dateText}>
                {t('rupee')}
                {Math.round(lastRide.fare)}
              </Text>
              <Text style={styles.locationLabel}>{lastRide.distance} km</Text>
            </View>
          </View>
          <View style={styles.rideContainer}>
            <View style={styles.statusContainer}>
              <Text style={styles.locationLabel}>{t('status')}: </Text>
              <Text style={[styles.statusText]}>
                {lastRide.status === RideStatus.Completed
                  ? t('completed')
                  : lastRide.status === RideStatus.DriverCancelled
                  ? t('driver_cancelled')
                  : lastRide.status === RideStatus.UserCancelled
                  ? t('user_cancelled')
                  : t('aborted')}
              </Text>
            </View>
            <Text style={styles.locationLabel}>{t('pickup_location')}: </Text>
            <Text numberOfLines={2} style={styles.whiteText}>
              {lastRide.source_address}
            </Text>
            <View style={{marginVertical: spacing.sm}}>
              <FadingHorizontalLine />
            </View>
            <Text style={styles.locationLabel}>{t('where_to')}: </Text>
            <Text numberOfLines={2} style={styles.whiteText}>
              {lastRide.destination_address}
            </Text>
            <View style={{marginTop: spacing.xl, width: '100%'}}>
              <Button
                onPress={() =>
                  navigation.navigate('FaQ', {
                    tripId: lastRide.id,
                    category: 'Trip',
                  })
                }
                title={t('get_help')}
                style={{width: '100%'}}
              />
            </View>
          </View>
        </>
      );
    }
    return null;
  };

  return (
    <ScrollView contentContainerStyle={{margin: 16}}>
      <SafeAreaView>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('help_support')}</Text>
        </View>
        <FadingHorizontalLine />

        {renderRecentRide()}

        <View style={styles.card}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text style={styles.subtitle}>{t('having_an_issue')}</Text>
            <View style={{width: '50%', alignItems: 'flex-end'}}>
              <Button
                onPress={() =>
                  navigation.navigate('FaQ', {
                    tripId: lastRide?.id,
                    category: 'General',
                  })
                }
                title={t('get_help')}
              />
            </View>
          </View>
          <View style={{marginVertical: spacing.xxl}}>
            <FadingHorizontalLine />
          </View>
          <TouchableOpacity
            onPress={() => {
              navigation.navigate('Tickets');
            }}
            style={{alignItems: 'center'}}>
            <Text style={styles.viewAllText}>{t('view_all_tickets')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default Help;
