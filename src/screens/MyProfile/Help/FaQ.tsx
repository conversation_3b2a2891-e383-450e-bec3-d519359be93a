import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ImageBackground,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import styles from './FaQStyle';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import images from '../../../constants/images';
import {SafeAreaView} from 'react-native-safe-area-context';
import {spacing} from '../../../constants/theme';
import arrowIcon from '../../../icons/arrow_icon.svg';
import documentIcon from '../../../icons/document.svg';
import {useToast} from '../../../components/Toast/Toast';
import ChatModal from '../../../components/ChatModal';
import SupportRequestService from '../../../services/SupportRequestService';
import back from '../../../icons/back.svg';
import {colors} from '../../../constants';
import {useUser} from '../../../hooks/useUser';

interface HelpScreenProps {
  navigation: any;
  route: any;
}

interface TemplateItem {
  id: number;
  category: string;
  subject: string;
  isActive: boolean;
  created_at: string;
  updated_at: string;
}

const FaQ: React.FC<HelpScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {tripId, category = 'General'} = route.params || {};
  const {user} = useUser();
  const {showToast} = useToast();
  const [modalVisible, setModalVisible] = useState(false);
  const [currentTopic, setCurrentTopic] = useState('');
  const [messages, setMessages] = useState<any[]>([]);
  const [supportRequestId, setSupportRequestId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [templates, setTemplates] = useState<TemplateItem[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(true);
  const [filteredTemplates, setFilteredTemplates] = useState<TemplateItem[]>([]);

  const supportRequestIdRef = useRef<number | null>(null);

  useEffect(() => {
    const fetchList = async () => {
      try {
        const response = await SupportRequestService.getSupportTemplate();
        if (response.status === 200) {
          setTemplates(response.data.data);
          // Filter templates based on category
          const templatesByCategory = response.data.data.filter(
            (template: TemplateItem) => 
              template.category.toLowerCase() === category.toLowerCase()
          );
          setFilteredTemplates(templatesByCategory);
        }
      } catch (error) {
        console.error('Error fetching list:', error);
      } finally {
        setTemplatesLoading(false);
      }
    };
    fetchList();
  }, [category]);

  const renderListItem = (title: string, onPress: () => void) => (
    <TouchableOpacity style={styles.listItem} onPress={onPress}>
      <View
        style={{
          flexDirection: 'row',
          marginVertical: spacing.md,
          alignItems: 'center',
        }}>
        <View style={styles.listIcon}>
          <IconSvgView size={20} source={documentIcon} />
        </View>
        <Text style={styles.listTxt}>{title}</Text>
      </View>
      <IconSvgView size={14} source={arrowIcon} />
    </TouchableOpacity>
  );

  const handleOpenChat = async (topic: string, category: string) => {
    if (category === 'Trip' && !tripId) {
      showToast(t('no_trip_selected'), 'failure');
      return;
    }

    setCurrentTopic(topic);
    setIsLoading(true);
    setMessages([]);

    try {
      const queryResponse = await SupportRequestService.postQuerySupportTicket({
        subject: topic,
        rideId: category === 'Trip' ? tripId : null,
      });

      console.log('Query response:', JSON.stringify(queryResponse.data));
      console.log(
        'Response structure check:',
        'data' in queryResponse.data ? 'Has data property' : 'No data property',
        'data.data' in queryResponse
          ? 'Has data.data property'
          : 'No data.data property',
        'conversations' in queryResponse.data
          ? 'Has conversations at root'
          : 'No conversations at root',
      );

      const ticketData = queryResponse.data.data;

      if (ticketData && ticketData.id) {
        setSupportRequestId(ticketData.id);
        supportRequestIdRef.current = ticketData.id;

        if (
          ticketData.conversations &&
          Array.isArray(ticketData.conversations) &&
          ticketData.conversations.length > 0
        ) {
          console.log(
            'Found conversations in first response:',
            ticketData.conversations.length,
          );

          const historyMessages = ticketData.conversations.map(msg => ({
            text: msg.body || msg.message || '',
            sender: msg.senderType === 'user',
            time: new Date(msg.created_at || Date.now()).toLocaleTimeString(
              [],
              {
                hour: '2-digit',
                minute: '2-digit',
              },
            ),
          }));

          console.log('Processed messages:', historyMessages.length);
          setMessages(historyMessages);
        } else {
          console.log('No conversations found in first response');
          try {
            const ticketDetails = await SupportRequestService.getOneTicket(
              ticketData.id,
            );
            console.log('Ticket details response:', ticketDetails.status);
            console.log(
              'Ticket details data structure:',
              Object.keys(ticketDetails.data || {}),
            );

            const detailsData = ticketDetails.data?.data || ticketDetails.data;
            if (detailsData) {
              let conversations = [];

              if (
                detailsData.conversations &&
                Array.isArray(detailsData.conversations)
              ) {
                conversations = detailsData.conversations;
              } else if (
                detailsData.replies &&
                Array.isArray(detailsData.replies)
              ) {
                conversations = detailsData.replies;
              }

              if (conversations.length > 0) {
                console.log(
                  `Found ${conversations.length} messages in ticket details`,
                );
                const historyMessages = conversations.map(
                  (msg: {
                    body: any;
                    message: any;
                    senderType: string;
                    created_at: any;
                  }) => ({
                    text: msg.body || msg.message || '',
                    sender: msg.senderType === 'user',
                    time: new Date(
                      msg.created_at || Date.now(),
                    ).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    }),
                  }),
                );

                setMessages(historyMessages);
              } else {
                console.log('No conversations found in ticket details');
              }
            }
          } catch (historyError) {
            console.error('Error fetching ticket details:', historyError);
          }
        }
      } else {
        console.log('No existing ticket found');
      }
    } catch (error) {
      console.error('Error querying support tickets:', error);
    } finally {
      setIsLoading(false);
      setModalVisible(true);
    }
  };

  const handleCloseChat = () => {
    setModalVisible(false);
    setMessages([]);
    setSupportRequestId(null);
    setIsLoading(false);
  };

  const handleSendMessage = async (message: string) => {
    const newMessage = {
      text: message,
      sender: true,
      time: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
    setMessages(prevMessages => [...prevMessages, newMessage]);
    setIsLoading(true);

    console.log('Current ID state:', supportRequestId);
    console.log('Current ID ref:', supportRequestIdRef.current);

    try {
      if (supportRequestId === null && supportRequestIdRef.current === null) {
        const shouldSendRideId =
          currentTopic === t('trip_issues') ||
          currentTopic.toLowerCase().includes('ride');

        console.log('Creating new support request with params:', {
          subject: currentTopic,
          rideId: shouldSendRideId ? tripId : undefined,
        });

        const response = await SupportRequestService.createSupportRequest({
          subject: currentTopic,
          rideId: shouldSendRideId ? tripId : undefined,
          body: message,
          files: [],
        });

        console.log('Create response:', JSON.stringify(response.data));
        const ticketData = response.data;
        const ticketId =
          ticketData && typeof ticketData === 'object' ? ticketData.id : null;

        if (response.status === 201 && ticketId) {
          setSupportRequestId(ticketId);
          supportRequestIdRef.current = ticketId;

          console.log('Set supportRequestId to:', ticketId);
          showToast(t('support_request_success'), 'success');
        } else {
          const responseText = JSON.stringify(response.data);
          const matches = responseText.match(/"id":(\d+)/);

          if (matches && matches[1]) {
            const extractedId = parseInt(matches[1], 10);
            console.log('Extracted ID from response text:', extractedId);

            setSupportRequestId(extractedId);
            supportRequestIdRef.current = extractedId;
            showToast(t('support_request_success'), 'success');
          }
        }
      } else {
        const activeTicketId = supportRequestId || supportRequestIdRef.current;
        console.log('Updating existing ticket with ID:', activeTicketId);

        const response = await SupportRequestService.updateSupportRequest(
          activeTicketId!,
          {
            body: message,
            files: [],
          },
        );

        console.log('Update response:', JSON.stringify(response.data));
        showToast(t('updated_ticket'), 'success');
      }
    } catch (error) {
      console.error('Support request error:', error);
      showToast(t('support_request_failed'), 'failure');
    } finally {
      setIsLoading(false);
      const autoReplyMessage = {
        text: t('thank_you_message'),
        sender: false,
        time: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
      };
      setMessages(prevMessages => [...prevMessages, autoReplyMessage]);
      console.log(
        'supportRequestId after operation - state:',
        supportRequestId,
      );
      console.log(
        'supportRequestId after operation - ref:',
        supportRequestIdRef.current,
      );
    }

    return Promise.resolve();
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('select_issue')}</Text>
        </View>
        <ScrollView showsVerticalScrollIndicator={false} style={{flexGrow: 1}}>
          {templatesLoading ? (
            <ActivityIndicator size="large" color={colors.lightGrey} />
          ) : filteredTemplates.length > 0 ? (
            filteredTemplates.map((item, index) => (
              <React.Fragment key={item.id}>
                {index > 0 && <FadingHorizontalLine />}
                {renderListItem(item.subject, () =>
                  handleOpenChat(item.subject, item.category),
                )}
              </React.Fragment>
            ))
          ) : (
            <View style={styles.noTemplatesContainer}>
              <Text style={styles.noTemplatesText}>{t('no_help_topics_found')}</Text>
            </View>
          )}
        </ScrollView>

        <ChatModal
          visible={modalVisible}
          onClose={handleCloseChat}
          userId={user?.id?.toString() || ''}
          messages={messages}
          tripId={currentTopic === t('trip_issues') ? tripId : undefined}
          sendMessage={handleSendMessage}
          title={currentTopic}
          emptyMessageText={t('send_your_query')}
          isLoading={isLoading}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default FaQ;
