import {StyleSheet} from 'react-native';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../../constants';
import {spacing} from '../../../constants/theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingHorizontal: spacing.md * 1.5,
  },
  card: {
    backgroundColor: colors.darkGrey,
    padding: spacing.lg,
    borderRadius: spacing.xs,
    marginVertical: spacing.md,
  },
  cardTitle: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: GeistFont.bold,
    marginLeft: spacing.md,
  },
  cardText: {
    fontSize: sizes.h6,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    marginTop: spacing.xs,
  },
  rideContainerTop: {
    borderTopEndRadius: spacing.xs,
    borderTopStartRadius: spacing.xs,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.darkGrey,
    marginTop: spacing.xxl,
    alignItems: 'center',
  },
  vehicleIcon: {
    width: 40,
    height: 40,
    margin: spacing.md,
  },
  dateText: {
    fontSize: sizes.h6,
    color: colors.white,
    marginVertical: spacing.xs,
    fontFamily: GeistFont.bold,
  },
  locationLabel: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.variable,
  },
  detailsContainer: {
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: spacing.xxs,
    paddingHorizontal: spacing.sm,
  },

  detailsText: {
    fontSize: sizes.body,
    color: colors.grey,
  },

  whiteText: {
    color: colors.white,
  },
  rideContainer: {
    flex: 1,
    padding: spacing.lg,
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderBottomEndRadius: spacing.xs,
    borderBottomStartRadius: spacing.xs,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    marginLeft: spacing.lg,
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },
  statusContainer: {
    position: 'absolute',
    right: 20,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 16,
    color: colors.green,
  },
  noTicketsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  noTicketsText: {
    fontSize: 16,
    color: colors.lightGrey,
    textAlign: 'center',
    fontFamily: GeistFont.regular,
  },
  appGif: {
    width: 50,
    height: 50,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
