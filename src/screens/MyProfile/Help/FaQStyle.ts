import {StyleSheet} from 'react-native';
import {spacing} from '../../../constants/theme';
import {EBGaramondFont, GeistFont, colors, sizes} from '../../../constants';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.md,
  },

  title: {
    marginLeft: spacing.lg,
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },

  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.md * 1.5,
  },

  subtitle: {
    fontSize: sizes.h2,
    fontFamily: EBGaramondFont.EBbold,
    color: colors.white,
    marginVertical: spacing.lg,
  },

  listIcon: {
    padding: spacing.md,
    borderRadius: spacing.sm,
  },
  listTxt: {
    width: '75%',
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h4,
    color: colors.white,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chatBtn: {
    marginVertical: spacing.xl,
  },
  loaderImage: {
    marginTop: spacing.md,
    width: 50,
    height: 50,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  noTemplatesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  noTemplatesText: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.h3,
    color: colors.white,
    textAlign: 'center',
  },
});
