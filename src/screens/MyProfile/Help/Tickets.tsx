import React, {useEffect, useState} from 'react';
import {View, TouchableOpacity, Text, ScrollView, Image} from 'react-native';
import {useTranslation} from 'react-i18next';
import styles from './TicketsStyle';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import TripService from '../../../services/TripService';
import {STATUS_CODE} from '../../../constants/constants';
import {useLoader} from '../../../hooks/useLoader';
import {useToast} from '../../../components/Toast/Toast';
import back from '../../../icons/back.svg';
import ChatModal from '../../../components/ChatModal';

interface Ride {
  id: number;
  created_at: string;
  updated_at: string;
  driverId: number;
  userId: number;
  status: string;
  source: {latitude: number; longitude: number};
  destination: {latitude: number; longitude: number};
  source_address: string;
  destination_address: string;
  distance: number;
  duration: number;
  fare: number;
  tip: number | null;
  vehicleType: string;
}

interface Conversation {
  id: number;
  body: string;
  senderType: string;
  userId: number;
  isReadByAdmin: boolean;
  isReadByUser: boolean;
  files: string[];
  created_at: string;
  updated_at: string;
}

interface Ticket {
  id: number;
  subject: string;
  status: string;
  created_at: string;
  rideId?: number;
  ride?: Ride;
  conversations?: Conversation[];
}

import {NavigationProp} from '@react-navigation/native';
import images from '../../../constants/images';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import {colors} from '../../../constants/fonts';
import SupportRequestService from '../../../services/SupportRequestService';
import {useUser} from '../../../hooks/useUser';
import {SafeAreaView} from 'react-native-safe-area-context';
import {spacing} from '../../../constants/theme';

const Tickets = ({
  navigation,
  route,
}: {
  navigation: NavigationProp<any>;
  route: any;
}) => {
  const {t} = useTranslation();
  const rideId = route.params?.rideId;
  const {user, setUnreadSupportMsg} = useUser();
  const [lastRide, setLastRide] = useState<Ride | null>(null);
  const {showToast} = useToast();
  const [modalVisible, setModalVisible] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [activeTicket, setActiveTicket] = useState<Ticket | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Add this state to track loading

  useEffect(() => {
    const fetchTickets = async () => {
      try {
        setIsLoading(true);
        setUnreadSupportMsg(false);
        const response = await SupportRequestService.getAllTickets();
        console.log(response.data.data, 'support request');
        const ticketData = response.data.data;
        const updatedTickets = [...ticketData];

        const ticketsWithRides = ticketData.filter(
          (ticket: Ticket) => ticket.rideId,
        );

        for (let i = 0; i < ticketsWithRides.length; i += 3) {
          const batch = ticketsWithRides.slice(i, i + 3);

          const results = await Promise.all(
            batch.map(async (ticket: Ticket) => {
              try {
                const rideResponse = await TripService.fetchTrip(
                  ticket.rideId!,
                );
                const rideDetails =
                  rideResponse?.data?.data || rideResponse.data;
                return {...ticket, ride: rideDetails};
              } catch (err) {
                console.error('Error fetching ride for ticket', ticket.id, err);
                return ticket;
              }
            }),
          );

          results.forEach(updatedTicket => {
            const index = updatedTickets.findIndex(
              t => t.id === updatedTicket.id,
            );
            if (index !== -1) {
              updatedTickets[index] = updatedTicket;
            }
          });

          if (i + 3 < ticketsWithRides.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        setTickets(updatedTickets);
      } catch (error) {
        console.error('Error fetching tickets:', error);
        showToast(t('error_fetching_tickets'), 'failure');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTickets();
  }, []);

  const fetchLastRide = async () => {
    try {
      const response = await TripService.getTrips(1, 1);

      if (response.data?.data?.data && response.data.data.data.length > 0) {
        const fetchedRide = response.data.data.data[0];
        setLastRide(fetchedRide);
        return fetchedRide;
      }
      return null;
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return null;
      } else if (STATUS_CODE.bad_request) {
        code === 'getting_trips_failed' &&
          showToast(t('error_fetching_trip'), 'failure');
      }
      return null;
    }
  };

  useEffect(() => {
    fetchLastRide();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleOpenChat = async (ticket: Ticket) => {
    setActiveTicket(ticket);

    if (ticket.conversations && ticket.conversations.length > 0) {
      console.log(
        `Found ${ticket.conversations.length} conversations for ticket ${ticket.id}`,
      );

      const sortedConversations = [...ticket.conversations].sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
      );

      const formattedMessages = sortedConversations.map(conversation => {
        const isUserSender = conversation.senderType.toLowerCase() === 'user';

        return {
          text: conversation.body || '',
          sender: isUserSender,
          time: formatTime(conversation.created_at),
          files: conversation.files || [],
          id: conversation.id,
          senderType: conversation.senderType,
        };
      });

      console.log(`Formatted ${formattedMessages.length} messages for display`);
      console.log(
        'Message positioning sample:',
        formattedMessages.map(m => ({
          text: m.text.substring(0, 20),
          sender: m.sender,
          senderType: m.senderType,
        })),
      );

      setMessages([]);
      setTimeout(() => {
        setMessages(formattedMessages);
      }, 50);
    } else {
      console.log('No conversations found for this ticket');
      setMessages([]);
    }

    setModalVisible(true);
  };

  const handleSendMessage = async (message: string) => {
    const newMessage = {
      text: message,
      sender: true,
      time: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };
    setMessages(prevMessages => [...prevMessages, newMessage]);

    if (activeTicket) {
      try {
        const response = await SupportRequestService.updateSupportRequest(
          activeTicket.id,
          {
            body: message,
            files: [],
          },
        );
        console.log('Support ticket updated:', response);

        if (activeTicket.status.toLowerCase() === 'waiting_for_user_response') {
          try {
            await SupportRequestService.markTicketAsRead(activeTicket.id);

            setTickets(prev =>
              prev.map(t => {
                if (t.id === activeTicket.id) {
                  return {...t, status: 'open'};
                }
                return t;
              }),
            );

            setActiveTicket(prevTicket => {
              if (prevTicket) {
                return {...prevTicket, status: 'open'};
              }
              return prevTicket;
            });
          } catch (err) {
            console.error('Error marking ticket as read:', err);
          }
        }

        setTickets(prev =>
          prev.map(ticket => {
            if (ticket.id === activeTicket.id) {
              const updatedTicket = {...ticket};
              const newConversation = {
                id: Date.now(),
                body: message,
                senderType: 'user',
                userId: user?.id,
                isReadByAdmin: false,
                isReadByUser: true,
                files: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              };

              updatedTicket.conversations = [
                ...(updatedTicket.conversations || []),
                newConversation,
              ];

              return updatedTicket;
            }
            return ticket;
          }),
        );
      } catch (err) {
        console.error('Error updating support request:', err);
        showToast(t('error_sending_message'), 'failure');
      }
    } else {
      console.warn('Active ticket not set.');
    }
    return Promise.resolve();
  };

  const getDisplayStatus = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'Open';
      case 'waiting_for_user_response':
        return 'Waiting';
      case 'closed':
        return 'Closed';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'open':
        return colors.green;
      case 'waiting_for_user_response':
        return colors.yellow;
      case 'closed':
        return colors.red;
      default:
        return colors.green;
    }
  };

  return (
    <>
      <View style={{padding: spacing.lg, flex: 1}}>
        <SafeAreaView style={{flex: 1}}>
          <View style={styles.titleContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
            <Text style={styles.title}>{t('tickets')}</Text>
          </View>
          <FadingHorizontalLine />

          <ScrollView
            style={{flex: 1, marginVertical: spacing.md}}
            showsVerticalScrollIndicator={false}>
            {isLoading ? (
              <View style={[styles.loaderContainer, {flex: 1, height: 600}]}>
                <Image
                  source={require('../../../icons/LOADER.gif')}
                  style={styles.appGif}
                />
              </View>
            ) : tickets.length > 0 ? (
              tickets.map(ticket => (
                <View key={ticket.id}>
                  <View style={styles.rideContainerTop}>
                    <View
                      style={{
                        alignItems: 'flex-start',
                        flex: 0.75,
                        margin: spacing.md,
                      }}>
                      <Text style={styles.dateText}>{ticket?.subject}</Text>
                      {ticket.rideId ? (
                        <Text style={styles.locationLabel}>
                          {ticket.ride
                            ? `${
                                ticket.ride.vehicleType === 'AUTORICKSHAW'
                                  ? t('auto1')
                                  : ''
                              } | ${t('booked_on')}: ${formatDate(
                                ticket.ride.created_at,
                              )}  `
                            : `${t('auto1')} | ${t('booked_on')}: 12/09/2023`}
                        </Text>
                      ) : null}
                    </View>
                    <View
                      style={{
                        flex: 0.3,
                        alignItems: 'flex-end',
                        paddingRight: spacing.md,
                      }}>
                      <Text
                        style={[
                          styles.dateText,
                          {
                            color: getStatusColor(ticket.status),
                            marginRight: spacing.md,
                          },
                        ]}>
                        {getDisplayStatus(ticket.status)}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.rideContainer}>
                    <Text style={[styles.locationLabel, {width: '60%'}]}>
                      {t('ticket_opened_on')}: {formatDate(ticket.created_at)}
                    </Text>
                    <View style={{marginTop: spacing.md}}></View>
                    <FadingHorizontalLine />
                    <View style={styles.detailsContainer}>
                      {ticket.status.toLowerCase() !== 'closed' && (
                        <TouchableOpacity
                          onPress={() => handleOpenChat(ticket)}>
                          <Text
                            style={[styles.dateText, {color: colors.davyGrey}]}>
                            {t('view')}
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.noTicketsContainer}>
                <Text style={styles.noTicketsText}>
                  {t('no_tickets_found')}
                </Text>
              </View>
            )}
          </ScrollView>
        </SafeAreaView>
      </View>
      <ChatModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        userId={user?.id}
        messages={messages}
        tripId={activeTicket?.id ? String(activeTicket.id) : undefined}
        sendMessage={handleSendMessage}
        title={activeTicket?.subject || t('Chat')}
      />
    </>
  );
};

export default Tickets;
