import {StyleSheet} from 'react-native';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../../constants';
import {spacing} from '../../../constants/theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingHorizontal: spacing.md * 1.5,
  },
  card: {
    backgroundColor: colors.darkGrey,
    padding: spacing.lg,
    borderRadius: spacing.xs,
    marginVertical: spacing.md,
  },
  cardTitle: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: GeistFont.bold,
    marginLeft: spacing.md,
  },
  cardText: {
    fontSize: sizes.h6,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    marginTop: spacing.xs,
  },
  rideContainerTop: {
    borderTopEndRadius: spacing.xs,
    borderTopStartRadius: spacing.xs,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.darkGrey,
  },
  vehicleIcon: {
    width: 40,
    height: 40,
    margin: spacing.md,
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
    marginVertical: spacing.xs,
    fontFamily: GeistFont.regular,
  },
  locationLabel: {
    color: colors.grey,
    fontSize: sizes.body,
    fontFamily: GeistFont.variable,
  },
  detailsContainer: {
    alignItems: 'flex-end',
    margin: spacing.md,
  },

  detailsText: {
    fontSize: sizes.body,
    color: colors.grey,
  },

  whiteText: {
    color: colors.white,
    fontFamily: GeistFont.regular,
  },
  rideContainer: {
    flex: 1,
    padding: spacing.md,
    alignItems: 'flex-start',
    marginBottom: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderBottomEndRadius: spacing.xs,
    borderBottomStartRadius: spacing.xs,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xxl,
  },
  title: {
    marginLeft: spacing.lg,
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },
  viewAllText: {
    color: colors.white,
    fontSize: sizes.body,
    fontWeight: 'bold',
    fontFamily: GeistFont.bold,
    textAlign: 'center',
  },
  subtitle: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    width: '50%',
  },

  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statusText: {
    fontSize: sizes.body,
    textTransform: 'capitalize',
    fontFamily: GeistFont.bold,
    color: colors.white,
  },
});
