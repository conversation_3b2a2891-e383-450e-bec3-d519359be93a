import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  ScrollView,
} from 'react-native';
import styles from './AccountInfoStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import verified from '../../../icons/verified.svg';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import {colors, images} from '../../../constants';
import {useUser} from '../../../hooks/useUser';
import Input from '../../../components/Input/Input';
import {spacing} from '../../../constants/theme';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import Button from '../../../components/Button/Button';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useFocusEffect} from '@react-navigation/native';

interface AccountInfoScreenProps {
  navigation: any;
}

const AccountInfo: React.FC<AccountInfoScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {user, fetchUser} = useUser();
  const [imageError, setImageError] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    email: user?.email || '',
    profilePic: user?.profile_pic || '',
  });

  useEffect(() => {
    (async () => {
      await fetchUser();
      if (user) {
        setFormData({
          name: user.name || '',
          phone: user.phone || '',
          email: user.email || '',
          profilePic: user?.profile_pic,
        });
      }
    })();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({...prev, [field]: value}));
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile', {formData});
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardShouldPersistTaps="handled">
          <View style={styles.titleContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
            <Text style={styles.title}>{t('account_info')}</Text>
          </View>
          <FadingHorizontalLine />

          <View style={{alignItems: 'center'}}>
            <Image
              source={
                formData.profilePic && !imageError
                  ? {uri: `${formData.profilePic}?t=${new Date().getTime()}`} //state not updating immediately when user is fetched again so refreshing forecefuly
                  : images.user
              }
              style={styles.imageContainer}
              resizeMode="cover"
              onError={() => setImageError(true)}
            />
          </View>

          <View style={{marginTop: spacing.xl * 2}}>
            <Input
              disabled={true}
              inputTitle={t('name')}
              placeholder={t('enter_name')}
              onChange={value => handleInputChange('name', value)}
              value={user?.name || ''}
            />
            <Input
              disabled={true}
              inputTitle={t('phone')}
              placeholder={t('enter_phone')}
              onChange={value => handleInputChange('phone', value)}
              value={user?.phone || ''}
              icon={<IconSvgView size={16} source={verified} />}
            />
            <Input
              disabled={true}
              inputTitle={t('email')}
              placeholder={t('enter_email')}
              onChange={value => handleInputChange('email', value)}
              value={user?.email === 'null' ? '' : (user?.email || '')}
              icon={
                user?.isEmailVerified ? (
                  <IconSvgView size={16} source={verified} />
                ) : undefined
              }
            />
          </View>
          <FlexContainer justifyContent="flex-end">
            <Button
              title={t('edit_profile')}
              style={styles.continueBtn}
              onPress={handleEditProfile}
            />
          </FlexContainer>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default AccountInfo;
