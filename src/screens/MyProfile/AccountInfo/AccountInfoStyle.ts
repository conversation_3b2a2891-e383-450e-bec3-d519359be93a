import {Platform, StyleSheet, ViewStyle} from 'react-native';
import {spacing} from '../../../constants/theme';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../../constants';
import {Gabriela} from '../../../constants/fonts';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    marginVertical: spacing.xl,
  },

  title: {
    marginVertical: spacing.sm,
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },

  safeArea: {
    flex: 1,
    padding: spacing.md * 1.5,
  },

  imageContainer: {
    resizeMode: 'cover',
    borderWidth: spacing.xxs,
    borderColor: colors.white,
    marginTop: spacing.xxl * 1.5,
    width: spacing.xl * 4,
    height: spacing.xl * 4,
    position: 'relative',
  },

  driverName: {
    color: colors.white,
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    marginBottom: spacing.xxl,
    marginTop: spacing.md,
  },

  editIconContainer: {
    marginBottom: spacing.xs,
    padding: spacing.xs,
    backgroundColor: colors.darkCharcoal,
    position: 'absolute',
    bottom: 0,
    right: '40%',
    borderTopLeftRadius: spacing.sm,
  },

  verifyTxt: {
    color: colors.lightGrey,
    fontSize: sizes.body,
    fontFamily: Gabriela.regular,
    textDecorationLine: 'underline',
  },

  continueBtn: {
    marginVertical: spacing.xl,
  },

  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.darkGrey,
    padding: spacing.xl,
    borderTopLeftRadius: spacing.xs,
    borderTopRightRadius: spacing.xs,
  },
  bottomSheetContent: {
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: sizes.h3,
    width: '70%',
    fontFamily: EBGaramondFont.EBbold,
    marginVertical: spacing.lg,
    color:colors.lightGrey
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    width: '100%',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  resendTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.grey,
  },
  resendOtp: {
    fontSize: sizes.h6,
    fontWeight: '400',
    color: colors.white,
    textDecorationLine: 'underline',
    textDecorationColor: colors.grey,
    textDecorationStyle: 'solid',
    paddingLeft: spacing.sm,
  },
  verifyBtn: {
    marginVertical: spacing.xl,
  },
});
