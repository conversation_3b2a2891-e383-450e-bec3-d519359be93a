import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ImageBackground,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import styles from './AccountInfoStyle';
import {useTranslation} from 'react-i18next';
import back from '../../../icons/back.svg';
import editProfile from '../../../icons/editProfile.svg';
import verified from '../../../icons/verified.svg';
import FadingHorizontalLine from '../../../components/FadingLine/FadingHorizontalLine';
import IconSvgView from '../../../components/IconSvgView/IconSvgView';
import {images} from '../../../constants';
import {useUser} from '../../../hooks/useUser';
import Input from '../../../components/Input/Input';
import {spacing} from '../../../constants/theme';
import FlexContainer from '../../../components/FlexContainer/FlexContainer';
import Button from '../../../components/Button/Button';
import {SafeAreaView} from 'react-native-safe-area-context';
import close from '../../../icons/close.svg';
import OTPInput from '../../../components/OtpInput/OtpInput';
import {OTP_TIMEOUT, STATUS_CODE} from '../../../constants/constants';
import AuthService from '../../../services/AuthService';
import {useLoader} from '../../../hooks/useLoader';
import {useToast} from '../../../components/Toast/Toast';
import {launchImageLibrary} from 'react-native-image-picker';
import BackgroundTimer from 'react-native-background-timer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useFocusEffect} from '@react-navigation/native';
import {set} from 'lodash';

interface AccountInfoScreenProps {
  navigation: any;
  route: any;
}

const EditProfile: React.FC<AccountInfoScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {fetchUser, user} = useUser();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const [imageError, setImageError] = useState(false);
  const {formData = {name: '', phone: '', email: '', profileImage: ''}} =
    route.params || {};
  const [editedFormData, setEditedFormData] = useState({
    name: formData?.name || '',
    phone: formData?.phone || '',
    email: formData?.email || '',
  });
  const [profileImageTimestamp] = useState(() => new Date().getTime());
  const [originalFormData, setOriginalFormData] = useState({
    name: '',
    phone: '',
    email: '',
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [otp, setOtp] = useState<string>('');
  const [isOtpComplete, setIsOtpComplete] = useState<boolean>(false);
  const [timer, setTimer] = useState<number>(0);
  const [email, setEmail] = useState('');
  const [confirmation, setConfirmation] = useState<any>();
  const [isEmailChanged, setIsEmailChanged] = useState(false);
  const [profileImage, setProfileImage] = useState(
    formData?.profileImage || '',
  );
  const [originalProfileImage, setOriginalProfileImage] = useState('');
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(false);
  const [otpKey, setOtpKey] = useState<number>(0);
  const timerIdRef = useRef<number | null>(null);
  const otpInputRef = useRef<any>(null);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isUpdateButtonEnabled, setIsUpdateButtonEnabled] = useState(false);

  useEffect(() => {
    return () => {
      if (timerIdRef.current !== null) {
        BackgroundTimer.clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const checkExistingTimer = async () => {
      try {
        const storedExpiryTime = await AsyncStorage.getItem(
          'emailOtpTimerExpiry',
        );
        if (storedExpiryTime) {
          const expiryTime = parseInt(storedExpiryTime);
          const now = Date.now();
          if (expiryTime > now) {
            const remainingTime = Math.floor((expiryTime - now) / 1000);
            setTimer(remainingTime);
            setIsResendDisabled(true);
            startBackgroundTimer(remainingTime);
          } else {
            AsyncStorage.removeItem('emailOtpTimerExpiry');
            setTimer(0);
            setIsResendDisabled(false);
          }
        }
      } catch (error) {
        console.error('Error restoring timer state:', error);
      }
    };

    checkExistingTimer();
  }, []);

  useEffect(() => {
    const isNameChanged = editedFormData.name !== originalFormData.name;
    const isProfilePicChanged = profileImage !== originalProfileImage;
    const isEmailChangePending = isEmailChanged && !isEmailVerified;
    const shouldEnableButton =
      isNameChanged ||
      isProfilePicChanged ||
      (isEmailChanged && isEmailVerified);

    const shouldDisableButton = isEmailChangePending;

    setIsUpdateButtonEnabled(shouldEnableButton && !shouldDisableButton);
  }, [
    editedFormData.name,
    originalFormData.name,
    profileImage,
    originalProfileImage,
    isEmailChanged,
    isEmailVerified,
  ]);

  const startBackgroundTimer = (duration: number) => {
    if (timerIdRef.current !== null) {
      BackgroundTimer.clearInterval(timerIdRef.current);
    }

    setTimer(duration);
    setIsResendDisabled(true);

    const expiryTime = Date.now() + duration * 1000;
    AsyncStorage.setItem('emailOtpTimerExpiry', expiryTime.toString());

    timerIdRef.current = BackgroundTimer.setInterval(() => {
      setTimer(prevTimer => {
        const newValue = prevTimer - 1;

        if (newValue <= 0) {
          if (timerIdRef.current !== null) {
            BackgroundTimer.clearInterval(timerIdRef.current);
            timerIdRef.current = null;
          }
          AsyncStorage.removeItem('emailOtpTimerExpiry');
          setIsResendDisabled(false);
          return 0;
        }

        return newValue;
      });
    }, 1000);
  };

  const resetOtpInput = () => {
    setOtp('');
    setIsOtpComplete(false);
    setOtpKey(prevKey => prevKey + 1);

    if (otpInputRef.current && otpInputRef.current.resetFocus) {
      setTimeout(() => {
        otpInputRef.current.resetFocus();
      }, 50);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      if (user) {
        setProfileImage(user.profile_pic || '');
        setOriginalProfileImage(user.profile_pic || '');

        const userData = {
          name: user.name,
          phone: user.phone,
          email: user.email,
        };

        setEditedFormData(userData);
        setOriginalFormData(userData);
      }
    }, [user]),
  );

  const handleInputChange = (field: string, value: string) => {
    if (field === 'email') {
      const lowerCaseEmail = value.toLowerCase();
      setEmail(lowerCaseEmail);
      
      setEditedFormData(prev => {
        const updatedFormData = {...prev, [field]: lowerCaseEmail};
        const emailChanged = lowerCaseEmail !== originalFormData.email;
        setIsEmailChanged(emailChanged);
        if (emailChanged) {
          setIsEmailVerified(false);
        } else {
          setIsEmailVerified(true);
        }
        return updatedFormData;
      });
    } else {
      setEditedFormData(prev => ({...prev, [field]: value}));
    }
  };

  const handleChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOtpComplete = (complete: boolean) => {
    setIsOtpComplete(complete);
  };

  const handleUpdate = async () => {
    const formData = new FormData();
    if (editedFormData.name) {
      formData.append('name', String(editedFormData.name).trim());
    }

    if (editedFormData.email && editedFormData.email !== 'null') {
      formData.append('email', String(editedFormData.email));
    }

    const isProfileImageChanged =
      profileImage !== originalProfileImage && !profileImage.includes('http'); 

    if (isProfileImageChanged && profileImage) {
      console.log('Including profile image in update');
      const fileExtension =
        profileImage.split('.').pop()?.toLowerCase() || 'jpg';
      const mimeType = fileExtension === 'png' ? 'image/png' : 'image/jpeg';

      const imageUri =
        Platform.OS === 'ios'
          ? profileImage.replace('file://', '')
          : profileImage;

      formData.append('profile_pic', {
        uri: imageUri,
        type: mimeType,
        name: `profile.${fileExtension}`,
      });
    } else {
      console.log('Skipping profile image - not changed or URL');
    }

    console.log('Platform:', Platform.OS);
    console.log('Form data being sent:', {
      name: editedFormData.name,
      email: editedFormData.email,
      profileImageIncluded: isProfileImageChanged,
    });

    try {
      showLoader();
      const response = await AuthService.updateUser(formData);
      console.log('Update response:', response);

      if (response.status === STATUS_CODE.ok) {
        setOriginalFormData(prevData => ({
          ...prevData,
          name: editedFormData.name,
        }));

        await fetchUser();

        showToast(t('profile_updated_successfully'), 'success');

        setTimeout(() => {
          navigation.goBack();
        }, 300);
      }
    } catch (err: any) {
      console.error(
        'Update error details:',
        JSON.stringify(err?.response?.data || err),
      );

      const status = err?.response?.status;
      const code =
        err?.response?.data?.response?.code || err?.response?.data?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        if (code === 'pp_invalid_file_type') {
          showToast(t('invalid_image_format'), 'failure');
        } else if (code === 'user_update_failed') {
          showToast(t('user_update_failed'), 'failure');
        } else if (code === 'profile_picture_upload_failed') {
          showToast(t('profile_picture_upload_failed'), 'failure');
        } else {
          showToast(t('update_failed'), 'failure');
        }
      }
    } finally {
      hideLoader();
    }
  };

  const handleVerify = async () => {
    setModalVisible(false);
    try {
      showLoader();
      const response = await AuthService.verifyEmail(email, otp);
      if (response.status === STATUS_CODE.created) {
        if (timerIdRef.current !== null) {
          BackgroundTimer.clearInterval(timerIdRef.current);
          timerIdRef.current = null;
        }
        AsyncStorage.removeItem('emailOtpTimerExpiry');

        showToast(t('otp_verified_success'), 'success');
        setModalVisible(false);
        setIsEmailVerified(true);
        navigation.replace('AccountInfo');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        const code = err?.response?.data?.code;
        if (code === 'invalid_otp') {
          showToast(t('incorrect_otp'), 'failure');
        } else if (code === 'verification_code_expired') {
          showToast(t('verification_code_expired'), 'failure');
        } else if (code === 'invalid_verification_request') {
          showToast(t('invalid_verification_request'), 'failure');
        }
      }
    } finally {
      hideLoader();
    }
  };

  const handleImagePicker = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.8}, response => {
      if (response.assets && response.assets.length > 0) {
        setProfileImage(response.assets[0].uri);
      }
    });
  };

  const sendOTP = async (email: string) => {
    if (!email || email === 'null') {
      showToast(t('invalid_email'), 'failure');
      return;
    }

    try {
      showLoader();
      resetOtpInput();

      const response = await AuthService.emailOtp(email);
      if (response.status == STATUS_CODE.created) {
        setConfirmation(response.data.data.message);
        showToast(t('otp_send_sucess'), 'success');
        startBackgroundTimer(OTP_TIMEOUT);
        setModalVisible(true);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const expiryTime = err?.response?.data?.response?.remainingTime;

      if (expiryTime) {
        startBackgroundTimer(Math.floor(expiryTime));
      }

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.conflict) {
        showToast(t('email_in_use'), 'failure');
      } else if (STATUS_CODE.bad_request) {
        showToast(t('user_not_found'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const resendOtp = async (email: string) => {
    if (!email || email === 'null') {
      showToast(t('invalid_email'), 'failure');
      return;
    }

    try {
      showLoader();
      resetOtpInput();

      const response = await AuthService.resendEmailOtp(email);
      if (response.status == STATUS_CODE.created) {
        setConfirmation(response.data.data.message);
        showToast(t('otp_send_sucess'), 'success');
        startBackgroundTimer(OTP_TIMEOUT);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.code;
      const expiryTime = err?.response?.data?.response?.remainingTime;

      if (expiryTime) {
        startBackgroundTimer(Math.floor(expiryTime));
      }

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'no_pending_verification' &&
          showToast(t('no_pending_verification'), 'failure');
        code === 'wait_before_resend' &&
          showToast(t('wait_before_resend'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}>
        <SafeAreaView style={styles.safeArea}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View style={styles.titleContainer}>
              <TouchableOpacity
                hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
                onPress={() => navigation.goBack()}>
                <IconSvgView source={back} />
              </TouchableOpacity>
              <Text style={styles.title}>{t('edit_profile')}</Text>
            </View>
            <FadingHorizontalLine />
            <View style={{alignItems: 'center'}}>
              <Image
                source={
                  profileImage
                    ? {uri: `${profileImage}?t=${profileImageTimestamp}`}
                    : images.user
                }
                style={styles.imageContainer}
                resizeMode="cover"
                onError={() => setImageError(true)}
              />
              <TouchableOpacity
                style={styles.editIconContainer}
                onPress={handleImagePicker}>
                <IconSvgView size={15} source={editProfile} />
              </TouchableOpacity>
            </View>

            <View style={{marginTop: spacing.xl * 2}}>
              <Input
                inputTitle={t('name')}
                placeholder={t('enter_name')}
                value={editedFormData?.name || ''}
                onChange={value => handleInputChange('name', value)}
              />
              <Input
                disabled={true}
                inputTitle={t('phone')}
                placeholder={t('enter_phone')}
                onChange={value => handleInputChange('phone', value)}
                value={editedFormData.phone}
                icon={<IconSvgView size={16} source={verified} />}
              />
              <Input
                inputTitle={t('email')}
                placeholder={t('enter_email')}
                onChange={value => handleInputChange('email', value)}
                value={
                  editedFormData.email === 'null'
                    ? ''
                    : editedFormData.email || ''
                }
                error={
                  editedFormData.email &&
                  editedFormData.email !== 'null' &&
                  !validateEmail(editedFormData.email)
                    ? t('invalid_email')
                    : null
                }
                icon={
                  isEmailChanged ? (
                    <TouchableOpacity
                      onPress={() => {
                        const emailToVerify = email || editedFormData.email;
                        if (
                          emailToVerify &&
                          emailToVerify !== 'null' &&
                          validateEmail(emailToVerify)
                        ) {
                          sendOTP(emailToVerify);
                        } else {
                          showToast(t('invalid_email'), 'failure');
                        }
                      }}>
                      <Text style={styles.verifyTxt}>
                        {isEmailVerified ? t('verified') : t('verify')}
                      </Text>
                    </TouchableOpacity>
                  ) : originalFormData.email &&
                    originalFormData.email !== 'null' ? (
                    <IconSvgView size={16} source={verified} />
                  ) : null
                }
              />
            </View>

            <FlexContainer justifyContent="flex-end">
              <Button
                disabled={!isUpdateButtonEnabled}
                title={t('update')}
                style={styles.continueBtn}
                onPress={handleUpdate}
              />
            </FlexContainer>
          </ScrollView>

          {modalVisible && (
            <View style={styles.bottomSheet}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Text style={styles.modalTitle}>{t('enter_otp_email')}</Text>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <IconSvgView size={15} source={close}></IconSvgView>
                </TouchableOpacity>
              </View>
              <View>
                <KeyboardAvoidingView
                  behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                  style={{flex: 1}}
                  testID="otp-input">
                  <OTPInput
                    key={otpKey}
                    ref={otpInputRef}
                    otp={otp}
                    onOtpChange={handleChange}
                    defaultValue={6}
                    onOtpComplete={handleOtpComplete}
                  />
                </KeyboardAvoidingView>
                <FlexContainer flex={1} direction="row">
                  {timer > 0 && (
                    <Text style={styles.resendTxt}>
                      {`${t('resend_otp_in')} ${timer}s`}
                    </Text>
                  )}
                  {timer === 0 && !isResendDisabled && (
                    <FlexContainer direction="row">
                      <Text style={styles.resendTxt}>{t("didn't_get")}</Text>
                      <TouchableOpacity
                        testID="resend-otp"
                        disabled={timer > 0 || isResendDisabled}
                        onPress={() => {
                          const emailToUse = email || editedFormData.email;
                          if (emailToUse && emailToUse !== 'null') {
                            resendOtp(emailToUse);
                          } else {
                            showToast(t('invalid_email'), 'failure');
                          }
                        }}>
                        <Text style={styles.resendOtp}>{t('resend_otp')}</Text>
                      </TouchableOpacity>
                    </FlexContainer>
                  )}
                </FlexContainer>
              </View>
              <Button
                style={styles.verifyBtn}
                title="Verify"
                onPress={handleVerify}
                disabled={!isOtpComplete}
              />
            </View>
          )}
        </SafeAreaView>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
};

export default EditProfile;
