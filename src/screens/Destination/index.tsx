import React, {useState, useEffect, useMemo} from 'react';
import {View, TouchableOpacity, Text, Dimensions, Platform} from 'react-native';
import {colors, sizes} from '../../constants';
import {userLocationContext} from '../../utils/userLocationContext';
import styles from './DestinationStyle';
import MapComponent from '../../components/Map/MapComponent';
import Button from '../../components/Button/Button';
import {useLocationContext} from '../../utils/LocationContext';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import close from '../../icons/close.svg';
import search from '../../icons/search.svg';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import myLocation from '../../icons/my_location.svg';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import GeofenceService from '../../services/GeofenceService';
import useIPLocation from '../../hooks/useIPLocation';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {spacing} from '../../constants/theme';

interface DestinationScreenProps {
  navigation: any;
  route: any;
}

const Destination: React.FC<DestinationScreenProps> = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const {userLocation, mapViewRef} = userLocationContext();
  const {whereto, setWhereto, dropLocation, setDropLocation} =
    useLocationContext();
  const [showLocation, setShowLocation] = useState<boolean>(true);
  const [mapMoved, setMapMoved] = useState<boolean>(false);
  const [showDropGeofence, setShowDropGeofence] = useState(false);
  const ipLocation = useIPLocation();
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [initialAnimationComplete, setInitialAnimationComplete] =
    useState(false);
  const [userMovedMap, setUserMovedMap] = useState(false);

  const insets = useSafeAreaInsets();
  const {height, width} = Dimensions.get('window');
  const isSmallDevice = height < 700;
  const isLargeDevice = height > 800;

  const {mapHeight, bottomHeight} = useMemo(() => {
    if (Platform.OS === 'ios') {
      const usableHeight = height;
      const mapHeightPercent = isSmallDevice
        ? 0.75
        : isLargeDevice
        ? 0.78
        : 0.76;
      console.log('mapHeightPercent', mapHeightPercent);

      return {
        mapHeight: usableHeight * mapHeightPercent,
        bottomHeight: usableHeight * (1 - mapHeightPercent),
      };
    } else {
      const mapHeightPercent = isSmallDevice ? 75 : isLargeDevice ? 78 : 76;

      return {
        mapHeight: hp(`${mapHeightPercent}%`),
        bottomHeight: hp(`${100 - mapHeightPercent}%`),
      };
    }
  }, [height, isSmallDevice, isLargeDevice]);

  const handleGetLocation = () => {
    if (userLocation) {
      const {latitude, longitude} = userLocation;
      setTimeout(() => {
        mapViewRef.current?.animateToRegion(
          {
            latitude,
            longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          },
          300,
        );
      }, 50);

      setDropLocation({
        latitude,
        longitude,
      });

      setUserMovedMap(true);
      setMapMoved(true);
    }
  };

  const checkGeofence = async (selectedPlace: any) => {
    try {
      if (
        !selectedPlace ||
        !selectedPlace.latitude ||
        !selectedPlace.longitude
      ) {
        return false;
      }

      const {data} = await GeofenceService.checkGeofence(selectedPlace);
      return data.data.isInside;
    } catch (error) {
      console.error('Error checking geofence in Destination screen:', error);
      return true;
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      let targetLocation;
      console.log('target', dropLocation);

      if (dropLocation) {
        targetLocation = dropLocation;
        setMapMoved(true);
        setUserMovedMap(true);
      } else {
        targetLocation = userLocation ?? ipLocation;
        setMapMoved(false);
      }

      if (targetLocation && isInitialLoad) {
        const {latitude, longitude} = targetLocation;

        setInitialAnimationComplete(false);

        setTimeout(() => {
          if (mapViewRef.current) {
            mapViewRef.current.animateToRegion(
              {
                latitude,
                longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              },
              100,
            );

            setTimeout(() => {
              setInitialAnimationComplete(true);
            }, 200);
          }
        }, 500);

        setIsInitialLoad(false);
      } else {
        setInitialAnimationComplete(true);
      }
    }, [dropLocation,isInitialLoad]),
  );

  const handleConfirm = async () => {
    try {
      if (!dropLocation) return;

      const isInside = await checkGeofence(dropLocation);

      if (!isInside) {
        setShowDropGeofence(true);
      } else {
        navigation.navigate('Pickup', {setText: ''});
      }
    } catch (err) {
      navigation.navigate('Pickup', {setText: ''});
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.mapContainer, {height: mapHeight}]}>
        <MapComponent
          ref={mapViewRef}
          setAddress={setWhereto}
          showLocation={showLocation}
          onMapMove={() => {
            setUserMovedMap(true);
            setMapMoved(true);
          }}
          mapPinText={t('drop_here')}
          region={dropLocation}
          setRegion={setDropLocation}
          initialAnimationComplete={initialAnimationComplete}
        />

        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => {
            navigation.navigate('Location', {
              focusDropInput: true,
              preserveDropLocation: true,
            });
          }}>
          <IconSvgView width={16} source={close} />
        </TouchableOpacity>

        {mapMoved && (
          <View style={styles.locationPositionContainer}>
            <TouchableOpacity
              style={styles.locationContainer}
              onPress={handleGetLocation}>
              <IconSvgView width={24} source={myLocation} />
            </TouchableOpacity>
          </View>
        )}
      </View>

      <SafeAreaView
        style={[styles.bottomSection, {minHeight: bottomHeight}]}
        edges={['bottom', 'left', 'right']}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title]}>{t('set_destination')}</Text>
        </View>

        <FadingHorizontalLine style={{marginBottom: spacing.md}} />

        <View style={styles.contentContainer}>
          {userMovedMap ? (
            <View style={styles.addressRow}>
              <Text
                style={styles.address}
                numberOfLines={1}
                ellipsizeMode="tail">
                {whereto}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('Location', {
                    focusDropInput: true,
                    preserveDropLocation: true,
                  });
                }}
                style={styles.searchBtn}>
                <IconSvgView width={30} source={search} />
              </TouchableOpacity>
            </View>
          ) : (
            <Text style={styles.moveTxt} numberOfLines={1}>
              {t('move_map')}
            </Text>
          )}

          <Button
            style={styles.confirmBtn}
            disabled={!userMovedMap}
            title={t('confirm')}
            onPress={handleConfirm}
          />
        </View>
      </SafeAreaView>

      {showDropGeofence && (
        <BottomUpModal
          showModal={showDropGeofence}
          onClose={() => setShowDropGeofence(false)}
          title={t('drop_not_serviceable')}
          description={t('another_drop_search')}
          buttonText={t('select_whereto')}
          onButtonClick={() => {
            setShowDropGeofence(false);
            navigation.navigate('Location', {
              focusDropInput: true,
              clearDropText: true,
            });
          }}
          forceUpdate={false}
        />
      )}
    </View>
  );
};

export default Destination;
