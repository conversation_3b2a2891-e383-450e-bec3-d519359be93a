import React, {useEffect, useRef, useState} from 'react';
import {View, Text, ImageBackground, TouchableOpacity} from 'react-native';
import {images} from '../../constants';
import styles from './CollectCashStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import back from '../../icons/back.svg';
import RideService from '../../services/RideService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CompleteRideProps {
  navigation: any;
}

interface LastRideDetails {
  id: string;
  driverDetails: {
    name: string;
  };
  fare: number;
  original_fare?: number;
  fare_changed: boolean;
  distance: string;
  status: string;
  payment_status: boolean;
}

interface ApiResponse {
  status: number;
  data: {
    data: {
      lastTrip: LastRideDetails;
    };
  };
}

const CollectCash: React.FC<CompleteRideProps> = ({navigation}) => {
  const {t} = useTranslation();
  const [lastRideDetails, setLastRideDetails] =
    useState<LastRideDetails | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const navigateToMaps = () => {
    navigation.reset({
      index: 0,
      routes: [{name: 'BottomTab'}],
    });
  };

  useEffect(() => {
    const TEN_MINUTES = 10 * 60 * 1000; //20 minutes in milliseconds

    const fetchLastRideAndCheckTime = async () => {
      try {
        const response = (await RideService.getLastRide()) as ApiResponse;
        if (response.status === 200 && response.data.data.lastTrip) {
          const ride = response.data.data.lastTrip;
          setLastRideDetails(ride);

          const updatedAt = ride.updated_at;
          const updatedTime = new Date(updatedAt).getTime();
          const currentTime = Date.now();
          const elapsedTime = currentTime - updatedTime;

          if (elapsedTime >= TEN_MINUTES) {
            navigation.reset({
              index: 0,
              routes: [{name: 'RatingScreen'}], 
            });
          } else {
            const remainingTime = TEN_MINUTES - elapsedTime;
            timerRef.current = setTimeout(() => {
              navigation.reset({
                index: 0,
                routes: [{name: 'RatingScreen'}],
              });
            }, remainingTime);
          }

          AsyncStorage.setItem('rideStatus', 'RATING');
        }
      } catch (error) {
        console.error('Error fetching or processing last ride:', error);
      }
    };

    fetchLastRideAndCheckTime();

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return (
    <FlexContainer flex={1}>
      <ImageBackground source={images.bg1} style={styles.backgroundImage}>
        <View style={styles.safeArea}>
          <View style={styles.backContainer}>
            <TouchableOpacity
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
          </View>

          <View style={styles.card}>
            <Text style={styles.title}>
              {t('you_owe')} {lastRideDetails?.driverDetails?.name}
            </Text>
            <Text style={styles.rupeeText}>
              {t('rupee')}{' '}
              {lastRideDetails?.fare_changed
                ? lastRideDetails?.original_fare ?? 0
                : lastRideDetails?.fare ?? 0}
            </Text>

            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={styles.priceSubtitle}>
                {t('distance_travelled')}
              </Text>
              <Text style={styles.distanceTravelled}>
                {lastRideDetails?.distance} {t('km')}
              </Text>
            </View>

            {lastRideDetails?.fare_changed && (
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Text style={styles.priceSubtitle}>
                    {t('distance_increased')}
                  </Text>
                  <Text style={styles.distanceTravelled}>{t('yes')}</Text>
                </View>
              </View>
            )}
          </View>

          <Text style={styles.noteText}>{t('pay_driver_cash')}</Text>

          <FlexContainer justifyContent="flex-end">
            <Button
              style={styles.completeBtn}
              title={t('continue')}
              onPress={navigateToMaps}
            />
          </FlexContainer>
        </View>
      </ImageBackground>
    </FlexContainer>
  );
};

export default CollectCash;
