import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    margin: spacing.xl,
  },

  backContainer: {
    position: 'absolute',
    top: 40,
    left: 0,
  },

  card: {
    backgroundColor: colors.davyGrey,
    marginTop: spacing.xl * 4,
    borderRadius: spacing.xs,
    padding: spacing.lg,
  },

  title: {
    textAlign: 'center',
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: GeistFont.regular,
    marginVertical: spacing.xs,
    minHeight: 30,
  },

  rupeeText: {
    textAlign: 'center',
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    minHeight: 30,
  },

  priceSubtitle: {
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: GeistFont.regular,
    marginTop: spacing.md,
    width: '70%',
    minHeight: 30,
  },

  distanceTravelled: {
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: GeistFont.regular,
    marginTop: spacing.md,
  },

  completeBtn: {
    justifyContent: 'flex-end',
  },

  noteText: {
    fontSize: sizes.body / 1.1,
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    marginTop: spacing.md,
  },
});
