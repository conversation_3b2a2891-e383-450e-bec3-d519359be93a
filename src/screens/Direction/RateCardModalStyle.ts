import {EBGaramondFont, GeistFont, colors, sizes} from '../../constants/fonts';
import {spacing} from '../../constants/theme';

const styles = {
  rateCard: {
    flex: 1,
    backgroundColor: 'rgba(45, 46, 50, 0.8)',
  },
  modalContent: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: spacing.xxl,
  },
  icons: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
  },
  titleCard: {
    alignItems: 'center',
    marginTop: spacing.xxl * 2,
  },
  title: {
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    marginTop: spacing.sm,
    color: colors.grey,
  },
  vechicleName: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    textTransform: 'lowercase',
    color: colors.white,
  },
  divider: {
    width: '100%',
    marginVertical: spacing.lg,
  },
  fareDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.md,
  },
  labelTxt: {
    color: colors.lightGrey,
  },
  valueTxt: {
    color: colors.white,
    fontFamily: GeistFont.bold,
  },
  note: {
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginTop: spacing.lg,
    color:colors.lightGrey
  },
  buttonContainer: {
    position: 'absolute',
    bottom: spacing.xl * 2,
    alignSelf: 'center',
    width: '100%',
  },
};

export default styles;
