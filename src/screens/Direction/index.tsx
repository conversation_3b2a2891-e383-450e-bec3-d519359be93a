import React, {useRef, useState, useCallback, useMemo} from 'react';
import {View, Text, TouchableOpacity, Image} from 'react-native';
import MapView, {<PERSON>t<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>yl<PERSON>} from 'react-native-maps';
import styles from './DirectionsStyle';
import {STATUS_CODE} from '../../constants/constants';
import {colors, images} from '../../constants';
import {zoomToRoute} from '../../utils/MapUtils';
import MapComponent from '../../components/Map/MapComponent';
import Button from '../../components/Button/Button';
import BottomSheet, {
  BottomSheetFlatList,
  BottomSheetFlatListMethods,
} from '@gorhom/bottom-sheet';
import {useLocationContext} from '../../utils/LocationContext';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import back from '../../icons/close.svg';
import dot from '../../icons/dot.svg';
import pickupIcon from '../../icons/pickupIcon.svg';
import dropIcon from '../../icons/dropIcon.svg';
import handler from '../../icons/handler.svg';
import {spacing} from '../../constants/theme';
import FareService from '../../services/FareService';
import RideService from '../../services/RideService';
import polyline from '@mapbox/polyline';
import {useFocusEffect} from '@react-navigation/native';
import {useRideDetails} from '../../hooks/useRideDetails';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useToast} from '../../components/Toast/Toast';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import {Platform} from 'react-native';
import AuthService from '../../services/AuthService';
import {useUser} from '../../hooks/useUser';
import GlobalTripPollingService from '../../services/GlobalTripPollingService';

interface DirectionScreenProps {
  navigation: any;
}

interface TripDetails {
  source: LatLng;
  destination: LatLng;
  trip_distance: number;
  route: {
    polyline: string;
    durationInMinutes: number;
  };
}

const DEFAULT_LAT_DELTA = 0.1;
const DEFAULT_LNG_DELTA = 0.1;
const ITEM_HEIGHT = 80;

const Direction: React.FC<DirectionScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {pickupLocation, pickupAddress, dropLocation, whereto} =
    useLocationContext();
  const {showToast} = useToast();
  const {setTripId} = useRideDetails();
  const {user} = useUser();
  const mapViewRef = useRef<MapView>(null);
  const sheetRef = useRef<BottomSheet>(null);
  const flatListRef = useRef<BottomSheetFlatListMethods>(null);
  const [region, setRegion] = useState<LatLng | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [address, setAddress] = useState<string>('');
  const [selectedVehicle, setSelectedVehicle] = useState<any>({});
  const [filteredVehiclesData, setFilteredVehiclesData] = useState<any[]>([]);
  const [tripDetails, setTripDetails] = useState<TripDetails>();
  const [bottomSheetIndex, setBottomSheetIndex] = useState(0);
  const [isInitialRender, setIsInitialRender] = useState(true);
  const [loading, setLoading] = useState(false);
  const [loadingConfirm, setLoadingConfirm] = useState(false);
  const [userConfig, setUserConfig] = useState<{
    show_pickup_range: boolean;
  } | null>(null);

  const animatedPosition = useSharedValue(0);

  const mapContainerStyle = useAnimatedStyle(() => {
    const mapHeightPercentage = interpolate(
      animatedPosition.value,
      [0, 1],
      Platform.OS === 'ios' ? [0.65, 0.55] : [0.6, 0.55],
    );

    return {
      height: `${mapHeightPercentage * 100}%`,
    };
  });

  const transformVehicleData = useCallback((data: any) => {
    const transformedData: any[] = [];
    const vehicleGroups = new Map();

    Object.keys(data).forEach(vehicleType => {
      const vehicles = data[vehicleType];
      if (!vehicleGroups.has(vehicleType)) {
        vehicleGroups.set(vehicleType, vehicles);
      }
    });

    vehicleGroups.forEach((vehicles, vehicleType) => {
      if (vehicleType === 'AUTORICKSHAW') {
        const fares = vehicles.map((v: any) => v.fare);
        const minFare = Math.min(...fares);
        const maxFare = Math.max(...fares);
        const vehicle = vehicles[0];

        transformedData.push({
          id: vehicleType,
          name: vehicleType,
          minFare,
          maxFare,
          estimatedPickupTime: vehicle.estimated_pickup_time,
          seats: vehicle.seats ?? 3,
          image: images.auto,
        });
      }
    });

    return transformedData;
  }, []);

  const visibleVehicles = useMemo(() => {
    if (!isInitialRender && bottomSheetIndex === 0 && selectedVehicle.id) {
      const selectedVehicleData = filteredVehiclesData.find(
        vehicle => vehicle.id === selectedVehicle.id,
      );

      const otherVehicles = filteredVehiclesData.filter(
        vehicle => vehicle.id !== selectedVehicle.id,
      );

      return selectedVehicleData
        ? [selectedVehicleData, ...otherVehicles]
        : filteredVehiclesData;
    }
    return filteredVehiclesData;
  }, [
    filteredVehiclesData,
    selectedVehicle.id,
    bottomSheetIndex,
    isInitialRender,
  ]);

  const handleVehicleSelect = useCallback(
    (selectedVehicleId: string, selectedVehicleName: string) => {
      setSelectedVehicle({id: selectedVehicleId, name: selectedVehicleName});
      // Auto collapse the sheet when vehicle is selected
      if (bottomSheetIndex > 0) {
        sheetRef.current?.snapToIndex(0);
      }
    },
    [bottomSheetIndex],
  );

  const handleSheetChanges = useCallback(
    (index: number) => {
      setBottomSheetIndex(index);
      setIsInitialRender(false);
      animatedPosition.value = withSpring(index);

      if (routeCoordinates.length > 0) {
        setTimeout(() => {
          mapViewRef.current && zoomToRoute(mapViewRef, routeCoordinates);
        }, 300);
      }
    },
    [routeCoordinates, animatedPosition],
  );

  const renderItem = useCallback(
    ({
      item,
    }: {
      item: {
        estimatedPickupTime: any;
        minFare: number;
        maxFare: number;
        id: any;
        name: string;
        image: any;
        seats: string;
      };
    }) => (
      <TouchableOpacity
        accessible
        accessibilityLabel={`Select ${item.name}`}
        style={[
          styles.vehicleCard,
          selectedVehicle.id === item.id && styles.selectedVehicleCard,
        ]}
        onPress={() => handleVehicleSelect(item.id, item.name)}>
        <Image style={{width: 50, height: 50}} source={item.image} />
        <View style={styles.vehicleInfo}>
          <View style={styles.vehicleNameContainer}>
            <Text style={styles.vehicle}>
              {item.name === 'AUTORICKSHAW' ? 'Auto Rickshaw' : 'Cab'}
            </Text>
            <IconSvgView width={3} source={dot} />
          </View>
          <Text numberOfLines={1} style={styles.rate}>
            {t('rupee')}
            {user?.showPickupRange
              ? `${item.minFare} - ${item.maxFare}`
              : item.minFare}
          </Text>
        </View>
        <View style={{padding: spacing.md, maxWidth: '50%'}}>
          <Text numberOfLines={1} style={styles.seater}>
            {item.seats} {t('seats')}
          </Text>
          {/* <Text numberOfLines={1} style={styles.seater}>
            {item.estimatedPickupTime} {t('min')}
          </Text> */}
        </View>
      </TouchableOpacity>
    ),
    [selectedVehicle.id],
  );

  //mapviewref is null since ref is assigned only after that mapview is mounted
  useFocusEffect(
    useCallback(() => {
      if (routeCoordinates.length > 0) {
        const interval = setInterval(() => {
          if (mapViewRef.current) {
            zoomToRoute(mapViewRef, routeCoordinates);
            clearInterval(interval);
          } else {
            console.log('mapViewRef is not set');
          }
        }, 500);

        return () => clearInterval(interval);
      }
    }, [routeCoordinates, mapViewRef.current]),
  );

  const handleFocus = async () => {
    try {
      const noDrivers = await AsyncStorage.getItem('noDrivers');
      if (noDrivers === 'true') {
        console.log('🧹 Clearing noDrivers flag on Direction screen focus');
        await AsyncStorage.multiRemove(['noDrivers', 'noDriversTimestamp', 'pendingDirectNavigation']);

        // Stop any existing polling to ensure clean state
        if (Platform.OS === 'ios') {
          const globalPollingService = GlobalTripPollingService.getInstance();
          globalPollingService.stopPolling();
        }
      }
    } catch (error) {
      console.error('Error checking noDrivers flag:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      handleFocus();
      const initializeAndCalculate = async () => {
        try {
          // Store in AsyncStorage if state values exist
          if (pickupLocation) {
            await AsyncStorage.setItem(
              'pickup',
              JSON.stringify(pickupLocation),
            );
          }
          if (dropLocation) {
            await AsyncStorage.setItem('drop', JSON.stringify(dropLocation));
          }

          // Try loading from AsyncStorage if missing in state
          const storedPickup = pickupLocation
            ? pickupLocation
            : JSON.parse((await AsyncStorage.getItem('pickup')) || 'null');

          const storedDrop = dropLocation
            ? dropLocation
            : JSON.parse((await AsyncStorage.getItem('drop')) || 'null');

          // If still missing, go back to home
          if (!storedPickup || !storedDrop) {
            navigation.replace('BottomTab');
            return;
          }

          setLoading(true);

          const response = await FareService.calculateFare(
            storedPickup.latitude,
            storedPickup.longitude,
            storedDrop.latitude,
            storedDrop.longitude,
          );

          if (response.status === STATUS_CODE.ok) {
            const {estimates, route} = response.data.data;
            const transformedData = transformVehicleData(estimates);

            setFilteredVehiclesData(transformedData);
            setTripDetails(response.data.data);

            if (transformedData.length > 0 && !selectedVehicle.id) {
              setSelectedVehicle(transformedData[0]);
            }

            if (route?.polyline) {
              const decodedCoordinates = polyline
                .decode(route.polyline)
                .map(([latitude, longitude]: [number, number]) => ({
                  latitude,
                  longitude,
                }));
              setRouteCoordinates(decodedCoordinates);
              zoomToRoute(mapViewRef, decodedCoordinates);
            }
          } else if (response.status === STATUS_CODE.reset) {
            navigation.replace('BottomTab');
          }
        } catch (error) {
          console.error(
            'Error during initialization or fare calculation:',
            error,
          );
          navigation.replace('BottomTab');
        } finally {
          setLoading(false);
        }
      };

      initializeAndCalculate();
    }, [pickupLocation, dropLocation]),
  );

  const handleRideConfirmation = async () => {
    try {
      if (!pickupLocation || !dropLocation) {
        throw new Error('Pickup or drop location is missing');
      }
      setLoadingConfirm(true);

      // Clear any previous noDrivers flags before creating new trip
      await AsyncStorage.multiRemove(['noDrivers', 'noDriversTimestamp', 'pendingDirectNavigation']);

      const payload = {
        source: pickupLocation,
        destination: {
          latitude: dropLocation.latitude,
          longitude: dropLocation.longitude,
        },
        source_address: pickupAddress,
        destination_address: whereto,
        status: 'processing',
        vehicleType: selectedVehicle.id,
      };

      const response = await RideService.sendRideRequest(payload);
      console.log('Ride confirmation response:', response.data);

      if (response.status === STATUS_CODE.created) {
        if (response.data.data.trip.id) {
          await AsyncStorage.setItem(
            'tripId',
            JSON.stringify(response.data.data.trip.id),
          );
          console.log('catchingtrioid', response.data.data.trip.id);

          await AsyncStorage.setItem('rideStatus', 'INITIALISED');
          setTripId(response.data.data.trip.id);

          // Restart global polling for new trip
          if (Platform.OS === 'ios') {
            const globalPollingService = GlobalTripPollingService.getInstance();
            await globalPollingService.restartPollingForNewTrip();
          }

          navigation.replace('Confirm');
        }
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      console.log('Error during ride confirmation:', err.response.data);

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'pick_up_or_drop_negative' &&
          showToast(t('pick_up_or_drop_negative'), 'failure');
        code === 'ride_init_failed' &&
          showToast(t('ride_init_failed'), 'failure');
      }
    } finally {
      setLoadingConfirm(false);
    }
  };

  const snapPoints = useMemo(
    () => (Platform.OS === 'ios' ? ['35%', '45%'] : ['40%', '50%']),
    [],
  );

  return (
    <View style={{backgroundColor: colors.darkGrey, flex: 1}}>
      {loading ? (
        <View style={styles.loaderContainer}>
          <Image
            source={require('../../icons/LOADER.gif')}
            style={styles.loaderImage}
          />
        </View>
      ) : (
        <Animated.View style={mapContainerStyle}>
          <MapComponent
            ref={mapViewRef}
            marker={false}
            setAddress={setAddress}
            style={{flex: 1}}
            region={{
              latitude:
                tripDetails?.source?.latitude ?? pickupLocation?.latitude ?? 0,
              longitude:
                tripDetails?.source?.longitude ??
                pickupLocation?.longitude ??
                0,
              latitudeDelta: DEFAULT_LAT_DELTA,
              longitudeDelta: DEFAULT_LNG_DELTA,
            }}
            setRegion={setRegion}>
            {tripDetails?.source && (
              <Marker
                anchor={{x: 0.5, y: 0.5}}
                tracksViewChanges={false}
                coordinate={{
                  latitude: routeCoordinates[0]?.latitude,
                  longitude: routeCoordinates[0]?.longitude,
                }}
                title="Pickup Location">
                <IconSvgView size={25} source={pickupIcon} />
              </Marker>
            )}
            {tripDetails?.destination && (
              <Marker
                anchor={{x: 0.5, y: 0.5}}
                tracksViewChanges={false}
                coordinate={{
                  latitude:
                    routeCoordinates[routeCoordinates.length - 1]?.latitude,
                  longitude:
                    routeCoordinates[routeCoordinates.length - 1]?.longitude,
                }}
                title="Drop Location">
                <IconSvgView source={dropIcon} />
              </Marker>
            )}
            {routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor="#FFFFFF"
                strokeColors={['#FFFFFF']}
                strokeWidth={4}
              />
            )}
          </MapComponent>
        </Animated.View>
      )}

      <>
        <View style={styles.backContainer}>
          <TouchableOpacity
            onPress={() =>
              navigation.reset({
                index: 0,
                routes: [{name: 'BottomTab'}],
              })
            }>
            <IconSvgView source={back} />
          </TouchableOpacity>
        </View>
        <BottomSheet
          onChange={handleSheetChanges}
          ref={sheetRef}
          index={0}
          snapPoints={snapPoints}
          enablePanDownToClose={false}
          handleComponent={() => (
            <View style={{alignItems: 'center', backgroundColor: colors.black}}>
              <IconSvgView width={60} source={handler} />
            </View>
          )}>
          <View style={styles.backgroundImage}>
            {!loading && (
              <>
                <View style={{alignItems: 'center'}}>
                  <Text style={styles.title}>{t('destination_title')}</Text>
                  <View style={{flexDirection: 'row'}}>
                    <Text style={styles.distance}>
                      {tripDetails?.trip_distance} {t('km')}
                    </Text>
                    <IconSvgView
                      width={6}
                      svgStyle={{marginTop: spacing.sm}}
                      source={dot}
                    />
                    <Text style={styles.distance}>
                      {tripDetails?.route?.durationInMinutes} {t('mins')}
                    </Text>
                  </View>
                </View>
                <BottomSheetFlatList
                  ref={flatListRef}
                  data={visibleVehicles}
                  keyExtractor={item => item.id}
                  renderItem={renderItem}
                  contentContainerStyle={styles.contentContainer}
                  extraData={selectedVehicle.id}
                  initialNumToRender={visibleVehicles.length}
                  getItemLayout={(data, index) => ({
                    length: ITEM_HEIGHT,
                    offset: ITEM_HEIGHT * index,
                    index,
                  })}
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={5}
                />
              </>
            )}
          </View>
        </BottomSheet>
        <View style={styles.fixedBottomContainer}>
          {!loading && (
            <Text numberOfLines={2} style={styles.footerText}>
              {t('pay_driver')}
            </Text>
          )}
          {!loading && (
            <Button
              style={styles.confirmBtn}
              onPress={handleRideConfirmation}
              disabled={loadingConfirm || loading}
              title={
                loadingConfirm ? (
                  <Image
                    source={require('../../icons/LOADER.gif')}
                    style={{width: 30, height: 30}}
                  />
                ) : (
                  `${t('select')} ${
                    selectedVehicle.name === 'AUTORICKSHAW' && t('autoRickshaw')
                  }`
                )
              }
            />
          )}
        </View>
      </>
    </View>
  );
};

export default Direction;
