// RateCardModal.tsx
import React from 'react';
import { View, Text, Image, Modal, StatusBar } from 'react-native';
import { images } from '../../constants';
import Button from '../../components/Button/Button';
import styles from './RateCardModalStyle';
import { fareDetails } from '../../constants/constants';
import { Trans, useTranslation } from 'react-i18next';

const RateCardModal = ({
  selectedVehicle,
  modalVisible,
  onClose,
}: {
  selectedVehicle: any;
  modalVisible: boolean;
  onClose: () => void;
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      visible={modalVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}>
      <View style={styles.rateCard}>
        <StatusBar backgroundColor="rgba(0, 0, 0, 0.8)" translucent={true} />
        <View style={styles.modalContent}>
          <View style={styles.titleCard}>
            <Image
              style={styles.icons}
              source={selectedVehicle.image}
            />
            <Text style={styles.title}>{t('rate_card')}</Text>
            <Text style={styles.vechicleName}>{selectedVehicle.name}</Text>
          </View>
          <Image source={images.divider} style={styles.divider} />
          {fareDetails.map((item, index) => (
            <View style={styles.fareDetails} key={index}>
              <Text style={styles.labelTxt}>{item.label}</Text>
              <Text style={styles.valueTxt}>{item.value}</Text>
            </View>
          ))}
          <Text style={styles.note}>
            <Trans i18nKey="rate_card_optional_note">
              <Text style={styles.valueTxt}>optional</Text>.
            </Trans>
          </Text>
          <View style={styles.buttonContainer}>
            <Button title={t('understood')} onPress={onClose} />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default RateCardModal;
