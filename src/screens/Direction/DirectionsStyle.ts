import {Platform, StyleSheet, ViewStyle, Dimensions} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

const {height} = Dimensions.get('window');
const isSmallDevice = height < 700;

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
    backgroundColor: colors.black,
  },
  backContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
  },
  title: {
    color: colors.lightGrey,
    fontSize: sizes.h4,
    fontFamily: EBGaramondFont.EBbold,
    marginVertical: spacing.md,
    textAlign: 'center',
  },
  distance: {
    color: colors.lightGrey,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
  },
  vehicleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: spacing.xxs,
    overflow: 'hidden',
    paddingHorizontal: spacing.md,
    marginVertical: spacing.sm,
    marginBottom: Platform.OS === 'ios' ? spacing.lg : spacing.md,
  },
  selectedVehicleCard: {
    borderColor: colors.lightGrey,
    backgroundColor: colors.grayishBlack,
    padding: spacing.xs,
    borderWidth: 1,
    shadowColor: colors.white,
    shadowOffset: {width: 2, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  vehicleNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  vehicleInfo: {
    flex: 1,
    padding: spacing.md,
  },
  vehicle: {
    fontFamily: GeistFont.bold,
    fontSize: sizes.body,
    color: colors.white,
    marginRight: spacing.md,
  },
  seater: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    color: colors.lightGrey,
    marginLeft: spacing.md,
    minHeight: 25,
  },
  infoIcon: {
    resizeMode: 'contain',
    marginLeft: spacing.md,
  },
  rate: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    color: colors.lightGrey,
  },
  contentContainer: {
    paddingHorizontal: spacing.xl * 1.5,
    paddingBottom: Platform.OS === 'ios' 
      ? isSmallDevice ? spacing.xl * 6 : spacing.xl * 4 
      : isSmallDevice ? spacing.xl * 5 : spacing.xl * 3,
  },
  footerText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h3 / 2,
    color: colors.grey,
    marginVertical: spacing.md,
    textAlign: 'center',
  },
  fixedBottomContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    paddingHorizontal: spacing.xl,
    paddingBottom: Platform.OS === 'ios' ? spacing.xl : spacing.md, 
    paddingTop: spacing.md,
    backgroundColor: colors.black,
  },
  confirmBtn: Platform.select({
    ios: {
      marginBottom: isSmallDevice ? spacing.xl : spacing.lg,
    },
    android: {
      marginBottom: isSmallDevice ? spacing.md : spacing.sm,
    }
  }) as ViewStyle,
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});
