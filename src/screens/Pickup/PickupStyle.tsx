import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  mapContainer: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  locationPositionContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    margin: spacing.xl,
    zIndex: 10,
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.slateGray,
    borderRadius: 1,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
  },
  bottomSection: {
    backgroundColor: colors.black,
    borderTopLeftRadius: spacing.sm,
    borderTopRightRadius: spacing.sm,
    paddingTop: spacing.xs,
  },
  titleContainer: {
    paddingVertical: spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  title: {
    textAlign: 'center',
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
    paddingVertical: 0,
    marginVertical: 0,
  },
  contentContainer: {
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xs,
    paddingBottom: Platform.OS === 'android' ? spacing.md : 0,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  address: {
    width: '88%',
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: GeistFont.regular,
  },
  searchBtn: {
    width: '28%',
  },
  moveTxt: {
    color: colors.grey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    marginBottom: spacing.md,
    marginTop: spacing.xs,
  },
  confirmBtn: {
    marginTop: spacing.md,
    marginBottom: Platform.OS === 'ios' ? spacing.md : spacing.xs,
  },
  
  ridePointsContainer: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.xs,
  },
  ridePointsScrollView: {
    maxHeight: 200,
    marginTop: spacing.sm,
  },
  ridePointsScrollContent: {
    paddingBottom: spacing.sm,
  },
  pickupPoint: {
    padding: spacing.sm,
    paddingVertical: spacing.md,
    borderRadius: 2,
  },
  selectedPickupPoint: {
    backgroundColor: colors.darkCharcoal,
  },
  pickupText: {
    color: colors.lightGrey,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
  selectedPickupText: {
    color: colors.white,
  },
  zoneTitle: {
    fontFamily: GeistFont.bold,
    fontSize: sizes.h6,
    color: colors.white,
    marginTop: spacing.sm,
  },
  zoneSubtitle: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    color: colors.white,
    marginTop: spacing.xs,
  },
});