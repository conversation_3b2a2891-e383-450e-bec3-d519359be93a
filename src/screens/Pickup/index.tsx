import React, {useCallback, useMemo, useState} from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  ScrollView,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import {userLocationContext} from '../../utils/userLocationContext';
import styles from './PickupStyle';
import MapComponent from '../../components/Map/MapComponent';
import Button from '../../components/Button/Button';
import {useLocationContext} from '../../utils/LocationContext';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import close from '../../icons/close.svg';
import search from '../../icons/search.svg';
import myLocation from '../../icons/my_location.svg';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import RidePointService from '../../services/RidePointService';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import GeofenceService from '../../services/GeofenceService';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import useIPLocation from '../../hooks/useIPLocation';
import {spacing} from '../../constants/theme';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

interface PickupScreenProps {
  navigation: any;
  route: any;
}

interface RidePoint {
  id: string | number;
  name: string;
  latitude: number;
  longitude: number;
}

interface Coordinate {
  latitude: number;
  longitude: number;
}

const Pickup: React.FC<PickupScreenProps> = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const {mapViewRef, userLocation} = userLocationContext();
  const {
    pickupLocation,
    pickupAddress,
    dropLocation,
    setPickupAddress,
    setPickupLocation,
    whereto,
  } = useLocationContext();
  const setText = route.params?.setText;
  const [showLocation, setShowLocation] = useState<boolean>(true);
  const [mapMoved, setMapMoved] = useState<boolean>(false);
  const [ridePoints, setRidePoints] = useState<RidePoint[]>([]);
  const [boundary, setBoundary] = useState<Coordinate[]>([]);
  const [pointTitle, setPointTitle] = useState('');
  const [selectedPoint, setSelectedPoint] = useState<RidePoint | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [showPickupGeofence, setShowPickupGeofence] = useState(false);
  const ipLocation = useIPLocation();
  const [initialAnimationComplete, setInitialAnimationComplete] =
    useState(false);
  const [userMovedMap, setUserMovedMap] = useState(false);

  const insets = useSafeAreaInsets();
  const {height, width} = Dimensions.get('window');
  const isSmallDevice = height < 700;
  const isLargeDevice = height > 800;

  const {mapHeight, bottomHeight} = useMemo(() => {
    if (Platform.OS === 'ios') {
      const usableHeight = height - insets.top - insets.bottom;
      let mapHeightPercent;

      if (ridePoints.length > 0) {
        mapHeightPercent = isSmallDevice ? 0.55 : isLargeDevice ? 0.6 : 0.58;
      } else {
        mapHeightPercent = isSmallDevice ? 0.75 : isLargeDevice ? 0.78 : 0.76;
      }

      return {
        mapHeight: usableHeight * mapHeightPercent,
        bottomHeight: usableHeight * (1 - mapHeightPercent),
      };
    } else {
      let mapHeightPercent;

      if (ridePoints.length > 0) {
        mapHeightPercent = isSmallDevice ? 55 : isLargeDevice ? 60 : 58;
      } else {
        mapHeightPercent = isSmallDevice ? 75 : isLargeDevice ? 78 : 76;
      }

      return {
        mapHeight: hp(`${mapHeightPercent}%`),
        bottomHeight: hp(`${100 - mapHeightPercent}%`),
      };
    }
  }, [height, isSmallDevice, isLargeDevice, ridePoints.length]);

  const parseCoordinate = (value: any): number => {
    if (value === null || value === undefined) {
      return 0;
    }

    if (typeof value === 'number') {
      return value;
    }

    try {
      const parsed = parseFloat(value);
      if (isNaN(parsed)) {
        console.warn('Invalid coordinate value:', value);
        return 0;
      }
      return parsed;
    } catch (e) {
      console.warn('Error parsing coordinate:', e);
      return 0;
    }
  };

  const checkGeofence = async (selectedPlace: any) => {
    try {
      if (
        !selectedPlace ||
        !selectedPlace.latitude ||
        !selectedPlace.longitude
      ) {
        console.error('Invalid place for geofence check:', selectedPlace);
        return false;
      }

      const placeToCheck = {
        latitude: parseCoordinate(selectedPlace.latitude),
        longitude: parseCoordinate(selectedPlace.longitude),
      };

      const response = await GeofenceService.checkGeofence(placeToCheck);
      const responseData: any = response;
      const isInside = responseData.data.data.isInside;
      return isInside;
    } catch (error) {
      console.log('Error checking geofence:', error);
      return false;
    }
  };

  const handleConfirm = async () => {
    try {
      const isInside = await checkGeofence(pickupLocation);
      if (!isInside) {
        setShowPickupGeofence(!isInside);
      } else {
        setText && (!dropLocation || !whereto)
          ? navigation.navigate('Location', {focusDropInput: true})
          : navigation.navigate('Direction');
      }
    } catch (error) {
      console.error('Error in handleConfirm:', error);
      setText && (!dropLocation || !whereto)
        ? navigation.navigate('Location', {focusDropInput: true})
        : navigation.navigate('Direction');
    }
  };

  const fetchRidePoints = async (latitude: number, longitude: number) => {
    try {
      const response = await RidePointService.getRidePoints(
        latitude,
        longitude,
      );
      const data = response.data.data;
      setPointTitle(data.zoneName || '');

      // Validate and normalize ride points data - ensure numeric coordinates
      const validPoints = (data.points || []).map((point: any) => ({
        id: point.id || Math.random().toString(),
        name: point.name || 'Unnamed Point',
        latitude: parseCoordinate(point.latitude),
        longitude: parseCoordinate(point.longitude),
      }));

      // Validate and normalize boundary data - ensure numeric coordinates
      const validBoundary = (data.geoboundary || []).map((coord: any) => {
        const lat = parseCoordinate(coord.latitude || coord.lat);
        const lng = parseCoordinate(coord.longitude || coord.lng);

        return {
          latitude: lat,
          longitude: lng,
        };
      });

      setRidePoints(validPoints);
      setBoundary(validBoundary);
    } catch (error) {
      console.log('Error fetching ride points:', error);
      setRidePoints([]);
      setBoundary([]);
    }
  };

  useFocusEffect(
    useCallback(() => {
      let targetLocation;

      if (pickupLocation) {
        targetLocation = pickupLocation;
        setMapMoved(true);
      } else {
        targetLocation = userLocation ?? ipLocation;
        setMapMoved(false);
      }

      if (targetLocation && isInitialLoad) {
        const {latitude, longitude} = targetLocation;
        setInitialAnimationComplete(false);

        setTimeout(() => {
          if (mapViewRef.current) {
            mapViewRef.current.animateToRegion(
              {
                latitude: parseCoordinate(latitude),
                longitude: parseCoordinate(longitude),
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              },
              100,
            );
            setTimeout(() => {
              setInitialAnimationComplete(true);
            }, 200);
          }
        }, 500);

        fetchRidePoints(parseCoordinate(latitude), parseCoordinate(longitude));
        setIsInitialLoad(false);
        setUserMovedMap(true);
      }
    }, [pickupLocation, isInitialLoad]),
  );

  useFocusEffect(
    React.useCallback(() => {
      if (mapMoved && pickupLocation) {
        const {latitude, longitude} = pickupLocation;
        fetchRidePoints(parseCoordinate(latitude), parseCoordinate(longitude));
        setMapMoved(false);
      }

      setShowLocation(true);
      return () => setShowLocation(false);
    }, [mapMoved, pickupLocation]),
  );

  const handleGetLocation = () => {
    if (userLocation) {
      const {latitude, longitude} = userLocation;

      setTimeout(() => {
        mapViewRef.current?.animateToRegion(
          {
            latitude: parseCoordinate(latitude),
            longitude: parseCoordinate(longitude),
            latitudeDelta: 0.008,
            longitudeDelta: 0.008,
          },
          300,
        );
      }, 50);

      setPickupLocation({
        latitude: parseCoordinate(latitude),
        longitude: parseCoordinate(longitude),
      });

      setUserMovedMap(true);
      setMapMoved(true);
      setSelectedIndex(null);
      setSelectedPoint(null);
    }
  };

  const handleSelectPoint = (point: RidePoint, index: number) => {
    try {
      if (!point) {
        console.error('Point is null or undefined');
        return;
      }
      const lat = parseCoordinate(point.latitude);
      const lng = parseCoordinate(point.longitude);

      if (lat === 0 || lng === 0) {
        console.error('Invalid coordinates:', lat, lng);
        Alert.alert(
          'Error',
          'This pickup point has invalid coordinates. Please select another one.',
        );
        return;
      }
      if (mapViewRef.current) {
        mapViewRef.current.animateToRegion(
          {
            latitude: lat,
            longitude: lng,
            latitudeDelta: 0.008,
            longitudeDelta: 0.008,
          },
          1000,
        );
      }
      setSelectedPoint({
        ...point,
        latitude: lat,
        longitude: lng,
      });
      setSelectedIndex(index);
      setPickupLocation({
        latitude: lat,
        longitude: lng,
      });
      setPickupAddress(point.name || '');
      setUserMovedMap(true);
    } catch (error) {
      console.error('Error selecting point:', error);
      Alert.alert(
        'Error',
        'Unable to select this pickup point. Please try another one.',
      );
    }
  };

  const renderRidePoint = (point: RidePoint, index: number) => {
    return (
      <TouchableOpacity
        style={[
          styles.pickupPoint,
          selectedPoint?.id === point.id ? styles.selectedPickupPoint : {},
        ]}
        key={point.id || index}
        onPress={() => handleSelectPoint(point, index + 1)}>
        <Text
          style={[
            styles.pickupText,
            selectedPoint?.id === point.id ? styles.selectedPickupText : {},
          ]}>
          {index + 1}. {point.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={[styles.mapContainer, {height: mapHeight}]}>
        <MapComponent
          ref={mapViewRef}
          setAddress={setPickupAddress}
          showLocation={showLocation}
          onMapMove={() => {
            setUserMovedMap(true);
            setMapMoved(true);
          }}
          mapPinText={t('pickup_here')}
          region={pickupLocation}
          setRegion={setPickupLocation}
          initialAnimationComplete={initialAnimationComplete}
          boundary={boundary}
          ridePoints={ridePoints}
          selectedLocation={selectedPoint}
          selectedIndex={selectedIndex}
          preserveAddress={pickupAddress !== '' || !mapMoved}
        />

        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => {
            navigation.navigate('Location', {
              focusPickupInput: true,
            });
          }}>
          <IconSvgView width={16} source={close} />
        </TouchableOpacity>

        <View style={styles.locationPositionContainer}>
          <TouchableOpacity
            style={styles.locationContainer}
            onPress={handleGetLocation}>
            <IconSvgView width={24} source={myLocation} />
          </TouchableOpacity>
        </View>
      </View>

      <SafeAreaView
        style={[styles.bottomSection, {minHeight: bottomHeight}]}
        edges={['bottom', 'left', 'right']}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title]}>
            {setText ? t('set_pickup') : t('confirm_pickup')}
          </Text>
        </View>

        <FadingHorizontalLine style={{marginBottom: spacing.md}} />

        {ridePoints.length > 0 && (
          <View style={styles.ridePointsContainer}>
            <Text
              style={styles.zoneTitle}
              numberOfLines={1}
              ellipsizeMode="tail">
              {pointTitle}
            </Text>
            <Text style={styles.zoneSubtitle}>{t('preset_pickup')}</Text>

            <ScrollView
              style={styles.ridePointsScrollView}
              contentContainerStyle={styles.ridePointsScrollContent}>
              {ridePoints.map(renderRidePoint)}
            </ScrollView>

            <FadingHorizontalLine
              style={{marginTop: spacing.md, marginBottom: spacing.md}}
            />
          </View>
        )}

        {/* Content */}
        <View style={styles.contentContainer}>
          {userMovedMap ? (
            <View style={styles.addressRow}>
              <Text
                style={styles.address}
                numberOfLines={1}
                ellipsizeMode="tail">
                {pickupAddress}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate('Location', {
                    focusPickupInput: true,
                  });
                }}
                style={styles.searchBtn}>
                <IconSvgView width={30} source={search} />
              </TouchableOpacity>
            </View>
          ) : (
            <Text style={styles.moveTxt} numberOfLines={1}>
              {t('move_map')}
            </Text>
          )}

          <Button
            style={styles.confirmBtn}
            disabled={!userMovedMap}
            title={t('set_pickup')}
            onPress={handleConfirm}
          />
        </View>
      </SafeAreaView>

      {showPickupGeofence && (
        <BottomUpModal
          showModal={showPickupGeofence}
          onClose={() => setShowPickupGeofence(false)}
          title={t('pickup_not_serviceable')}
          description={t('another_pickup_search')}
          buttonText={t('select_pickup')}
          onButtonClick={() => {
            setShowPickupGeofence(false);
            setPickupLocation(null);
            navigation.navigate('Location', {
              focusPickupInput: true,
            });
          }}
          forceUpdate={false}
        />
      )}
    </View>
  );
};

export default Pickup;
