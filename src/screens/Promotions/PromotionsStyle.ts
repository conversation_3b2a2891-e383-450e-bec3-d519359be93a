import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    marginBottom: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
  },

  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
    lineHeight: sizes.h4 * 1.5,
  },

  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },

  content: {
    flex: 1,
    marginTop: spacing.lg,
  },
  promotionCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  promotionTitle: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  promotionDescription: {
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    color: colors.white,
    lineHeight: sizes.h4 * 1.2,
  },
  codeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  codeText: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    color: colors.green,
  },
  applyButton: {
    width: 100,
  },
  expiryText: {
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    color: colors.lightGrey,
    lineHeight: sizes.h4 * 1.5,
  },
});
