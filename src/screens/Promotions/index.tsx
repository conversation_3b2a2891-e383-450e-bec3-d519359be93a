import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import back from '../../icons/back.svg';
import styles from './PromotionsStyle';
import {SafeAreaView} from 'react-native-safe-area-context';
interface PromotionItem {
  cashbackEarned: string;
  type: string;
  endDate: string;
  name: string;
  id: string;
  title: string;
  description: string;
  code: string;
  expiryDate: string;
}

import {NativeStackScreenProps} from '@react-navigation/native-stack';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import { colors, GeistFont, images } from '../../constants';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import IconSvgView from '../../components/IconSvgView/IconSvgView';


type RootStackParamList = {
  Promotions: {promotions: PromotionItem[]};
};

type PromotionsProps = NativeStackScreenProps<RootStackParamList, 'Promotions'>;

const Promotions = ({navigation, route}: PromotionsProps) => {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const promotions = route.params.promotions;
  console.log(promotions);

  const formatDate = (isoString: string | number | Date) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
  };
  const renderPromotion = (item: PromotionItem) => (
    <View key={item.id} style={styles.promotionCard}>
      <Text style={styles.promotionTitle}>
        {item.type === 'referral'
          ? t('user_referral_title')
          : t('triple_treat_bonus_title')}
      </Text>
      <Text style={styles.promotionDescription}>
        {item.type === 'referral'
          ? t('user_referral_description')
          : t('triple_treat_bonus_description')}
      </Text>
      {/* <View style={styles.codeContainer}>
        <Text style={styles.codeText}>{item.code}</Text>
      </View> */}
      <Text style={styles.expiryText}>
        {t('expires')}: {formatDate(item.endDate)}
      </Text>
    </View>
  );

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('promotions')}</Text>
        </View>
        <FadingHorizontalLine />

        {promotions ? (
          <ScrollView style={styles.content}>
            {promotions.map(renderPromotion)}
          </ScrollView>
        ) : (
          <FlexContainer justifyContent="center" alignItems="center">
            <Text
              style={{
                color: colors.lightGrey,
                fontFamily: GeistFont.regular,
              }}>
              {t('no_promotions_available')}
            </Text>
          </FlexContainer>
        )}
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Promotions;
