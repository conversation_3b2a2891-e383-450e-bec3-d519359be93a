import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {Image, ImageBackground, StyleSheet, View} from 'react-native';
import {images} from '../../constants';
import {SafeAreaView} from 'react-native-safe-area-context';
import {spacing} from '../../constants/theme';

interface SplashScreenProps {
  navigation?: any;
  route?: any;
}

const SplashScreen: React.FC<SplashScreenProps> = ({navigation}) => {
  const {t} = useTranslation();

  useEffect(() => {
    const checkAuth = async () => {
      const navigateAfterDelay = async () => {
        navigation.replace('NavigationGateway');
      };
      setTimeout(navigateAfterDelay, 3000);
    };
    checkAuth();
  }, []);

  return (
    <ImageBackground source={images.bg1} style={styles.background}>
      <SafeAreaView style={styles.safearea}>
        <View style={styles.loaderContainer}>
          <Image
            source={require('../../icons/mapto.gif')}
            style={styles.appGif}
          />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: 'cover',
  },
  safearea: {
    flex: 1,
    margin: spacing.xl * 1.5,
  },
  appGif: {
    width: 350,
    height: 350,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SplashScreen;
