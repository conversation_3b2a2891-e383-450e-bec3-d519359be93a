import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.xxl * 1.5,
  },
  icon: {
    marginTop: spacing.xl * 2,
  },
  diamondIcon: {
    marginTop: spacing.xl * 2,
    marginRight: spacing.xl,
  },
  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },
  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    color: colors.white,
    marginBottom: spacing.xl,
  },
  resendTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.grey,
  },
  resendOtp: {
    fontSize: sizes.h6,
    fontWeight: '400',
    fontFamily: GeistFont.regular,
    color: colors.white,
    textDecorationLine: 'underline',
    textDecorationColor: colors.grey,
    textDecorationStyle: 'solid',
    paddingLeft: spacing.sm,
  },
  continueBtn: {
    marginBottom: spacing.xl,
  },
});
