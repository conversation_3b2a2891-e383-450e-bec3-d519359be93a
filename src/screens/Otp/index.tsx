import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import {images} from '../../constants';
import styles from './OtpStyle';
import {useToast} from '../../components/Toast/Toast';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import OTPInput from '../../components/OtpInput/OtpInput';
import Button from '../../components/Button/Button';
import Back from '../../icons/back.svg';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import {OTP_TIMEOUT, STATUS_CODE} from '../../constants/constants';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondInactive.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useLoader} from '../../hooks/useLoader';
import {useAuth} from '../../hooks/useAuth';
import {useUser} from '../../hooks/useUser';
import AuthService from '../../services/AuthService';
import {debounce} from 'lodash';
import BackgroundTimer from 'react-native-background-timer';
import TokenService from '../../services/TokenService';

interface OtpScreenProps {
  navigation: any;
  route: any;
}

const Otp: React.FC<OtpScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const {fetchUser} = useUser();
  const {checkAuthentication} = useAuth();
  const [isOtpComplete, setIsOtpComplete] = useState<boolean>(false);
  const [otp, setOtp] = useState<string>('');
  const mobile = route.params?.mobile || '';
  const login = route.params?.login || '';
  const [error, setError] = useState<boolean>(false);
  const [timer, setTimer] = useState<number>(0);
  const otpInputRef = useRef<any>(null);
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(false);
  const [otpKey, setOtpKey] = useState<number>(0);
  const timerIdRef = useRef<number | null>(null);

  useEffect(() => {
    return () => {
      if (timerIdRef.current !== null) {
        BackgroundTimer.clearInterval(timerIdRef.current);
        timerIdRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const checkExistingTimer = async () => {
      try {
        const storedExpiryTime = await AsyncStorage.getItem('otpTimerExpiry');
        if (storedExpiryTime) {
          const expiryTime = parseInt(storedExpiryTime);
          const now = Date.now();
          if (expiryTime > now) {
            const remainingTime = Math.floor((expiryTime - now) / 1000);
            setTimer(remainingTime);
            setIsResendDisabled(true);
            startBackgroundTimer(remainingTime);
          } else {
            AsyncStorage.removeItem('otpTimerExpiry');
            setTimer(0);
            setIsResendDisabled(false);
          }
        } else if (route.params?.remainingTime) {
          startBackgroundTimer(route.params.remainingTime);
        } else {
          setTimer(OTP_TIMEOUT);
          setIsResendDisabled(true);
          startBackgroundTimer(OTP_TIMEOUT);
        }
      } catch (error) {
        console.error('Error restoring timer state:', error);
      }
    };

    checkExistingTimer();

    if (!mobile) {
      showToast(t('mobile_not_found_error'), 'failure');
      navigation.replace('Start');
    }
  }, [mobile]);

  const startBackgroundTimer = (duration: number) => {
    if (timerIdRef.current !== null) {
      BackgroundTimer.clearInterval(timerIdRef.current);
    }

    setTimer(duration);
    setIsResendDisabled(true);

    const expiryTime = Date.now() + duration * 1000;
    AsyncStorage.setItem('otpTimerExpiry', expiryTime.toString());

    timerIdRef.current = BackgroundTimer.setInterval(() => {
      setTimer(prevTimer => {
        const newValue = prevTimer - 1;

        if (newValue <= 0) {
          if (timerIdRef.current !== null) {
            BackgroundTimer.clearInterval(timerIdRef.current);
            timerIdRef.current = null;
          }
          AsyncStorage.removeItem('otpTimerExpiry');
          setIsResendDisabled(false);
          return 0;
        }

        return newValue;
      });
    }, 1000);
  };

  const handleChange = (otpValue: string) => {
    setOtp(otpValue);
    setIsOtpComplete(otpValue.length === 6);
  };

  const handleOtpComplete = useCallback(
    debounce((complete: boolean) => {
      if (complete) {
        setIsOtpComplete(true);
      } else {
        setIsOtpComplete(false);
      }
    }, 300),
    [],
  );

  const resetOtpInput = () => {
    setOtp('');
    setIsOtpComplete(false);
    setOtpKey(prevKey => prevKey + 1);

    if (otpInputRef.current && otpInputRef.current.resetFocus) {
      setTimeout(() => {
        otpInputRef.current.resetFocus();
      }, 50);
    }
  };

  const sendOTP = async (phone: string) => {
    try {
      showLoader();
      setError(false);
      resetOtpInput();

      const response = await AuthService.login({phone: phone});
      if (response.status === STATUS_CODE.created) {
        showToast(t('otp_send_sucess'), 'success');
        startBackgroundTimer(OTP_TIMEOUT);
      } else {
        throw new Error(t('unexpected_error'));
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      const expiryTime = err?.response?.data?.response?.remainingTime;

      if (expiryTime) {
        startBackgroundTimer(Math.floor(expiryTime));
      }

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request === status) {
        code === 'otp_cooldown' &&
          showToast(t('wait_before_resend'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  const verifyOTP = async () => {
    try {
      showLoader();
      const response = await AuthService.verifyOtp(mobile, otp);

      if (response?.data?.data.verified) {
        if (timerIdRef.current !== null) {
          BackgroundTimer.clearInterval(timerIdRef.current);
          timerIdRef.current = null;
        }
        AsyncStorage.removeItem('otpTimerExpiry');

        await AsyncStorage.setItem(
          'accessToken',
          response.data?.data.accessToken,
        );
        await AsyncStorage.setItem(
          'refreshToken',
          response.data?.data.refreshToken,
        );

        await TokenService.initTokenManagement();

        await checkAuthentication();
        await fetchUser();
        navigation.replace('NavigationGateway');
        showToast(t('otp_verified_success'), 'success');
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request === status) {
        code === 'otp_not_matching' && showToast(t('incorrect_otp'), 'failure');
        code === 'otp_expired' && showToast(t('otp_expired'), 'failure');
        code === 'verification_code_not_found' &&
          showToast(t('verification_code_not_found'), 'failure');
      }
    } finally {
      hideLoader();
    }
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{flex: 1}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            keyboardShouldPersistTaps="handled">
            <View style={styles.iconContainer}>
              <TouchableOpacity onPress={() => navigation.replace('Start')}>
                <IconSvgView svgStyle={styles.icon} source={Back} />
              </TouchableOpacity>
              <FlexContainer direction="row" justifyContent="center">
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                {!login && (
                  <IconSvgView
                    svgStyle={styles.diamondIcon}
                    source={diamondInactive}
                  />
                )}
              </FlexContainer>
            </View>
            <Text numberOfLines={2} style={styles.title}>
              {t('enter_otp')}
              {'\n'}
              {mobile}
            </Text>
            <OTPInput
              key={otpKey}
              ref={otpInputRef}
              otp={otp}
              onOtpChange={handleChange}
              defaultValue={6}
              onOtpComplete={handleOtpComplete}
            />
            <FlexContainer flex={1} direction="row">
              {timer > 0 && (
                <Text numberOfLines={2} style={styles.resendTxt}>
                  {`${t('resend_otp_in')} ${timer}s`}
                </Text>
              )}
              {timer === 0 && !isResendDisabled && (
                <FlexContainer direction="row">
                  <Text numberOfLines={2} style={styles.resendTxt}>
                    {t("didn't_get")}
                  </Text>
                  <TouchableOpacity
                    testID="resend-otp"
                    disabled={timer > 0}
                    onPress={() => {
                      resetOtpInput();
                      sendOTP(mobile);
                    }}>
                    <Text numberOfLines={1} style={styles.resendOtp}>
                      {t('resend_otp')}
                    </Text>
                  </TouchableOpacity>
                </FlexContainer>
              )}
            </FlexContainer>
            <Button
              title={t('continue')}
              style={styles.continueBtn}
              disabled={!isOtpComplete || error}
              onPress={verifyOTP}
            />
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Otp;
