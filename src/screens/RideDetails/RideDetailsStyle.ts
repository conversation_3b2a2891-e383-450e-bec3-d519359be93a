import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.md * 1.5,
    paddingBottom: spacing.md * 1.5,
    backgroundColor: colors.black,
  },
  backContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
  },
  scrollViewContent: {
    flexGrow: 1,
    backgroundColor: colors.black,
  },
  arriveTxt: {
    color: colors.grey,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    textAlign: 'center',
    minHeight: 25,
  },
  pinTxt: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    width: '50%',
  },
  pinCard: {
    backgroundColor: colors.white,
    textAlign: 'center',
    width: spacing.xxl,
    height: spacing.xxl,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
    marginVertical: spacing.md,
  },
  pinNumber: {
    color: colors.black,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
  },
  vehicleContainer: {
    paddingVertical: spacing.md,
  },
  makeTxt: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginBottom: spacing.md,
    minHeight: 20,
  },
  vehicleNo: {
    color: colors.white,
    fontFamily: GeistFont.regular,
    backgroundColor: 'rgba(45, 46, 50, 1)',
    paddingHorizontal: spacing.sm,
    fontSize: sizes.body,
  },
  imageContainer: {
    width: 60,
    height: 60,
    resizeMode: 'cover',
    borderWidth: 2,
    borderColor: colors.white,
  },
  driverName: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
  },
  ratingContainer: {
    color: colors.white,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    marginTop: spacing.sm,
  },
  ratingText: {
    color: colors.white,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.xs,
  },
  rateTxt: {
    color: colors.white,
    fontSize: sizes.h3,
    fontFamily: EBGaramondFont.EBbold,
    marginLeft: spacing.xs,
  },
  locationLabel: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.variable,
    marginTop: spacing.lg,
  },
  location: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },
  cancelBtn: {
    backgroundColor: 'rgba(192, 104, 104, 0.04)',
    marginTop: spacing.xl,
    marginBottom: Platform.OS === 'ios' ? 30 : 20,
  },
  locationPositionContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    margin: spacing.xl,
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.davyGrey,
    borderRadius: 5,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
    marginVertical: spacing.sm,
  },
  autoIcon:{
    width: 60,
    height: 60,
    resizeMode: 'contain',
  }
});
