import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Button from '../../components/Button/Button';
import {useTranslation} from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TripService from '../../services/TripService';
import {useUser} from '../../hooks/useUser';
import styles from './RatingStyle';
import close from '../../icons/close.svg';
import images from '../../constants/images';
import {colors} from '../../constants';
import {useRideDetails} from '../../hooks/useRideDetails';
import RideService from '../../services/RideService';
import {STATUS_CODE} from '../../constants/constants';
import {useToast} from '../../components/Toast/Toast';
import {useLoader} from '../../hooks/useLoader';
import {useFocusEffect} from '@react-navigation/native';

const {height} = Dimensions.get('window');

interface RatingProps {
  showModal: boolean;
  onClose: () => void;
}

const Rating: React.FC<RatingProps> = ({showModal, onClose}) => {
  const {t} = useTranslation();
  const {user} = useUser();
  const {showToast} = useToast();
  const {showLoader, hideLoader} = useLoader();
  const [rating, setRating] = useState<number>(0);
  const [feedback, setFeedback] = useState<string>('');
  const [imageError, setImageError] = useState(false);
  const {tripDetails, setTripDetails} = useRideDetails();
  const [driverDetails, setDriverDetails] = useState<any>();
  const scrollViewRef = useRef<ScrollView>(null);
  const {fetchDriverDetails} = useRideDetails();

  const fetchLastRide = async () => {
    const response = await RideService.getLastRide();

    if (response.status === STATUS_CODE.ok) {
      console.log('rating', response.data.data.lastTrip);
      setTripDetails(response.data.data.lastTrip);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        await fetchLastRide();
        await fetchDriverDetails();
      };
      fetchData();
    }, []),
  );

  const handleRating = async (rate: number) => {
    setRating(rate);
    console.log(driverDetails);

    try {
      const response = await TripService.createRatings({
        score: rate,
        userId: user?.id,
        driverId: tripDetails?.driverId,
        tripId: tripDetails?.id,
        comment: feedback,
      });

      if (response.data?.data) {
        await AsyncStorage.removeItem('rideCompleted');
        await AsyncStorage.removeItem('trip');
        showToast(t('rating_success'), 'success');
        onClose();
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;
      console.log(err.response.data.response);

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'unknown_error' &&
          showToast(t('something_went_wrong'), 'failure');
      }
    }
  };

  const renderStars = () => {
    return (
      <View style={styles.starContainer}>
        {Array.from({length: 5}, (_, index) => (
          <TouchableOpacity
            style={{alignItems: 'center'}}
            key={index}
            onPress={() => setRating(index + 1)}>
            <Text style={styles.star}>{index < rating ? '★' : '☆'}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const handleFeedbackFocus = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({animated: true});
    }, 100);
  };

  return (
    <Modal
      transparent={true}
      animationType="slide"
      visible={showModal}
      onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        style={{flex: 1}}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: Platform.OS === 'ios' ? 20 : 80,
          }}
          keyboardShouldPersistTaps="handled">
          <SafeAreaView
            style={styles.safeArea}
            edges={['top', 'left', 'right']}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text style={[styles.title]}>{t('rate_driver_title')}</Text>
              <TouchableOpacity onPress={onClose}>
                <IconSvgView width={14} source={close} />
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                marginVertical: 10,
              }}>
              <Image
                source={
                  imageError || !driverDetails?.profile_photo
                    ? images.user
                    : {uri: driverDetails?.profile_photo}
                }
                style={styles.imageContainer}
                resizeMode="cover"
                onError={() => setImageError(true)}
              />
            </View>
            <Text style={styles.riderName}>
              {tripDetails?.driverDetails?.name}
            </Text>
            <Text style={styles.location} numberOfLines={3}>
              {tripDetails?.source_address
                ?.split(' ')
                .slice(0, 6)
                .join(' ')
                .replace(/,$/, '') ?? ''}{' '}
              to{' '}
              {tripDetails?.destination_address
                ?.split(' ')
                .slice(0, 6)
                .join(' ')
                .replace(/,$/, '') ?? ''}
            </Text>
            <View style={styles.starContainer}>{renderStars()}</View>
            <TextInput
              style={styles.feedbackInput}
              placeholder={t('feedback')}
              value={feedback}
              onChangeText={setFeedback}
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              onFocus={handleFeedbackFocus}
            />
            <Button
              style={styles.rateBtn}
              disabled={rating === 0}
              title={t('submit')}
              onPress={() => handleRating(rating)}
            />
          </SafeAreaView>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default Rating;
