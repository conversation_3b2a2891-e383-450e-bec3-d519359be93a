import {Platform, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  title: {
    marginVertical: spacing.lg,
    fontSize: sizes.h6,
    color: colors.white,
    fontFamily: GeistFont.regular,
    textAlign: 'center',
    flex: 1,
  },
  rateBtn: {
    marginVertical: spacing.xl,
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl * 1.5,
    backgroundColor: colors.darkCharcoal,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: spacing.sm,
    borderTopRightRadius: spacing.sm,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  star: {
    fontSize: sizes.h4 * 2,
    color: colors.lightGrey,
    marginHorizontal: spacing.sm,
  },
  riderName: {
    fontSize: sizes.h5,
    color: colors.white,
    fontFamily: EBGaramondFont.EBbold,
    textAlign: 'center',
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: spacing.xl * 0.5,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },

  feedbackInput: {
    borderColor: colors.lightGrey,
    backgroundColor: colors.davyGrey,
    borderWidth: 1,
    borderRadius: spacing.xs,
    padding: spacing.md,
    marginTop: spacing.md,
    width: '100%',
    height: 80,
    color: colors.white,
  },
  imageContainer: {
    width: 50,
    height: 50,
    resizeMode: 'cover',
    borderWidth: 2,
    borderColor: colors.white,
    justifyContent: 'center',
  },
  location: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    marginVertical: spacing.md,
    textAlign: 'center',
  },
});
