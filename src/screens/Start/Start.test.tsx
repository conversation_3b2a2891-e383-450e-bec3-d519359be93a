import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Start from '.';

jest.mock('react-native-linear-gradient', () => 'LinearGradient');


describe('Start component', () => {
    it('renders the Start component correctly', () => {
        const navigationMock = { navigate: jest.fn() };
        const { getByText, getByTestId } = render(<Start navigation={navigationMock} />);

        expect(getByText('Welcome to')).toBeTruthy();
        expect(getByText('mapto')).toBeTruthy();
        expect(getByTestId('car-image')).toBeTruthy();
        expect(getByText('Get Started')).toBeTruthy();
    });

    it('navigates to Phone screen when "Get Started" button is pressed', () => {
        const navigationMock = { navigate: jest.fn() };
        const { getByText } = render(<Start navigation={navigationMock} />);

        const getStartedButton = getByText('Get Started');
        fireEvent.press(getStartedButton);

        expect(navigationMock.navigate).toHaveBeenCalledWith('Phone');
    });

});
