import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    marginTop: spacing.xxl,
  },
  closeBtn: {
    marginTop: spacing.xl,
  },
  locationContainer: {
    width: '100%',
    padding: spacing.xl,
    height: 150,
  },
  pickupIcon: {
    marginTop: spacing.xl,
  },
  dropIcon: {
    marginBottom: spacing.md,
  },
  locationLabel: {
    color: colors.metallicSilver,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.variable,
    marginTop: spacing.sm,
  },
  loction: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },
  title: {
    marginTop: spacing.md,
    marginBottom: spacing.xl,
    marginHorizontal: spacing.md,
    textAlign: 'center',
    fontSize: sizes.h3,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },
  cancelBtn: {
    backgroundColor: 'rgba(192, 104, 104, 0.04)',
    marginBottom: spacing.xl,
    marginHorizontal: spacing.xl,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xxl * 5,
  },
  loaderImage: {
    resizeMode: 'cover',
    height: 120,
    width: '100%',
    position: 'relative',
  },
});
