import React, {useCallback, useEffect, useState} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {AppState, Image, ImageBackground, Text, View} from 'react-native';
import {colors, images} from '../../constants';
import styles from './ConfirmStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingLine from '../../components/FadingLine/FadingLine';
import Button from '../../components/Button/Button';
import {useLocationContext} from '../../utils/LocationContext';
import {SafeAreaView} from 'react-native-safe-area-context';
import {spacing} from '../../constants/theme';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamondInactive from '../../icons/diamondGrey.svg';
import ellipseActive from '../../icons/ellipseActive.svg';
import RideService from '../../services/RideService';
import {useRideDetails} from '../../hooks/useRideDetails';
import {STATUS_CODE} from '../../constants/constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useToast} from '../../components/Toast/Toast';
import TripService from '../../services/TripService';
import {navigationRef} from '../../router/navigationService';
import {drop} from 'lodash';

interface ConfirmScreenProps {
  navigation: any;
  route: any;
}

const Confirm: React.FC<ConfirmScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showToast} = useToast();
  const {pickupAddress, whereto, pickupLocation, dropLocation} =
    useLocationContext();
  const {
    setTripDetails,
    tripDetails,
    tripId,
    fetchTripDetails,
    fetchDriverDetails,
    clearLocalStorage,
  } = useRideDetails();
  const [isLoading, setIsLoading] = useState(true);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [tripStatus, setTripStatus] = useState<string | null>(null);

  useEffect(() => {
    const noDrivers = AsyncStorage.removeItem('noDrivers');
    const checkNoDriversFlag = async () => {
      try {
        const noDrivers = await AsyncStorage.getItem('noDrivers');
        console.log('noDrivers flag:', noDrivers);

        if (noDrivers === 'true') {
          await AsyncStorage.removeItem('noDrivers');
          await clearLocalStorage();
          navigation.reset({
            index: 0,
            routes: [{name: 'Direction'}],
          });
        }
        if (noDrivers === 'null') {
        }
      } catch (error) {
        console.error('Error checking noDrivers flag:', error);
      }
    };

    checkNoDriversFlag();

    const interval = setInterval(checkNoDriversFlag, 500);

    return () => {
      clearInterval(interval);
    };
  }, []);

  // Removed polling - now handled by GlobalTripPollingService
  useFocusEffect(
    useCallback(() => {
      const checkInitialStatus = async () => {
        try {
          const response = await TripService.getActiveRide();
          const activeRide = response?.data?.data?.activeRide;

          if (!activeRide) {
            console.log(
              '[Confirm] No active ride found, navigating to Direction',
            );
            await clearLocalStorage();
            navigation.reset({
              index: 0,
              routes: [{name: 'Direction'}],
            });
            return;
          }

          setTripDetails(activeRide);
        } catch (error) {
          console.error('[Confirm] Initial status check error:', error);
        }
      };

      // Only check once on focus, no polling needed
      checkInitialStatus();
    }, []),
  );

  useEffect(() => {
    const handleAppStateChange = async () => {
      const storedTripId = await AsyncStorage.getItem('tripId');
      const noDrivers = await AsyncStorage.getItem('noDrivers');
      const rideStatus = await AsyncStorage.getItem('rideStatus');
      if (storedTripId) {
        try {
          const response = await TripService.getActiveRide();
          console.log('inside confirm', response.data.data);

          if (response.status === STATUS_CODE.ok) {
            setTripDetails(response.data.data.activeRide);
            setTripStatus(response.data.data.activeRide.status);
          }
        } catch (err: any) {
          console.log(err);
        }
      }
      if (noDrivers) {
        await clearLocalStorage();
        navigationRef.current?.reset({routes: [{name: 'Direction'}]});
      } else if (rideStatus === 'ONPICKUP') {
        storedTripId && (await fetchDriverDetails());
        navigationRef.current?.reset({routes: [{name: 'RideDetails'}]});
      } else if (rideStatus === 'ONRIDE') {
        storedTripId && (await fetchDriverDetails());
        navigationRef.current?.reset({routes: [{name: 'RideRoute'}]});
      } else if (rideStatus === 'COMPLETED') {
        storedTripId && (await fetchDriverDetails());
        navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => {
      subscription.remove();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const initializeRideStatus = async () => {
        setIsLoading(true);
        await AsyncStorage.removeItem('driverCanceled');
        const noDrivers = await AsyncStorage.getItem('noDrivers');
        if (noDrivers === 'true') {
          await AsyncStorage.removeItem('noDrivers');
          navigation.reset({
            index: 0,
            routes: [{name: 'Direction'}],
          });
          return;
        }

        await fetchTripDetails();
        setIsLoading(false);
      };

      initializeRideStatus();
    }, []),
  );

  const handleCancel = async (e: any) => {
    try {
      const response = await RideService.cancelRide(tripId);
      if (response.status === STATUS_CODE.ok) {
        await AsyncStorage.removeItem('noDrivers');
        // navigation.reset({
        //   index: 0,
        //   routes: [{name: 'Direction'}],
        // });
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'trip_already_cancelled' &&
          showToast(t('trip_already_cancelled'), 'failure');
      }
    }
  };

  if (isLoading) {
    return (
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView
          style={[
            styles.safeArea,
            {justifyContent: 'center', alignItems: 'center'},
          ]}></SafeAreaView>
      </ImageBackground>
    );
  }

  const sourceAddress =
    pickupAddress ||
    (tripDetails && tripDetails.source_address) ||
    t('loading');
  const destinationAddress =
    whereto || (tripDetails && tripDetails.destination_address) || t('loading');

  return (
    <>
      <ImageBackground source={images.bg2} style={styles.backgroundImage}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.locationContainer}>
            <FlexContainer direction="row">
              <View style={{alignItems: 'center'}}>
                <IconSvgView
                  width={16}
                  svgStyle={styles.pickupIcon}
                  source={diamondInactive}
                />
                <View style={{marginBottom: spacing.sm, flex: 1}}>
                  <FadingLine
                    startX={0}
                    startY={1}
                    endX={1}
                    endY={0}
                    width={1}
                    height={'100%'}
                  />
                </View>
                <IconSvgView
                  width={12}
                  svgStyle={styles.dropIcon}
                  source={ellipseActive}
                />
              </View>
              <View style={{marginLeft: spacing.md}}>
                <Text style={styles.locationLabel}>{t('pickup_location')}</Text>
                <Text style={styles.loction} numberOfLines={1}>
                  {sourceAddress}
                </Text>
                <View style={{marginTop: spacing.lg}}>
                  <Text style={styles.locationLabel}>{t('where_to')}</Text>
                  <Text style={styles.loction} numberOfLines={1}>
                    {destinationAddress}
                  </Text>
                </View>
              </View>
            </FlexContainer>
          </View>
        </SafeAreaView>
        <FlexContainer
          direction="column"
          alignItems="center"
          style={styles.loaderContainer}>
          <View style={{width: '100%', alignItems: 'center'}}>
            <View style={{width: '100%'}}>
              <Image
                source={require('../../icons/searching.gif')}
                style={styles.loaderImage}
              />
            </View>
            <Text numberOfLines={2} style={styles.title}>
              {t('finding_drivers_nearby')}
            </Text>
          </View>
        </FlexContainer>
        <Button
          onPress={handleCancel}
          title={t('cancel')}
          style={styles.cancelBtn}
          textColor={colors.red}
        />
      </ImageBackground>
    </>
  );
};

export default Confirm;
