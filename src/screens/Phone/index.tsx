import React, {useCallback, useState} from 'react';
import {
  ImageBackground,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  View,
  Keyboard,
  Linking,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import {images} from '../../constants';
import styles from './PhoneStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import Input from '../../components/Input/Input';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondInactive.svg';
import {useTranslation} from 'react-i18next';
import {SafeAreaView} from 'react-native-safe-area-context';
import AuthService from '../../services/AuthService';
import {STATUS_CODE} from '../../constants/constants';
import {useLoader} from '../../hooks/useLoader';
import {useToast} from '../../components/Toast/Toast';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import PolicyService from '../../services/PolicyService';

interface PhoneScreenProps {
  navigation: any;
}

const Phone: React.FC<PhoneScreenProps> = ({navigation}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();
  const [mobile, setMobile] = useState('+91 ');
  const [showDeletedModal, setShowDeletedModal] = useState(false);

  const handlePress = async () => {
    const mobileToSend = mobile.replace(/\s/g, '');

    try {
      showLoader();
      const response = await AuthService.login({phone: mobileToSend});

      if (response.status === STATUS_CODE.created) {
        showToast(t('otp_sent'), 'success');
        navigation.navigate('Otp', {
          mobile: mobileToSend,
          otpSent: true,
        });
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err?.response?.data?.response?.code;
      const expiryTime = err?.response?.data?.response?.remainingTime;

      if (code === 'user_deleted') {
        setShowDeletedModal(true);
        return;
      }

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        showToast(t('unexpected_error'), 'failure');
        return;
      } else if (STATUS_CODE.bad_request === status) {
        if (code === 'otp_cooldown') {
          showToast(t('wait_before_resend'), 'failure');
          navigation.navigate('Otp', {
            mobile: mobileToSend,
            remainingTime: expiryTime ? Math.floor(expiryTime) : 0,
          });
        } else {
          showToast(t('unexpected_error'), 'failure');
        }
      } else {
        navigation.navigate('Otp', {mobile: mobileToSend});
      }
    } finally {
      hideLoader();
    }
  };

  const handleCloseModal = () => {
    setShowDeletedModal(false);
  };

  const handleEmailSupport = () => {
    const emailSubject = encodeURIComponent(`Account Reactivation - ${mobile}`);
    console.log('Email subject:', `Account Reactivation - ${mobile}`);
    Linking.openURL(`mailto:<EMAIL>?subject=${emailSubject}`);
  };

  const handleTermsPress = useCallback(async () => {
    try {
      const response = await PolicyService.getPolicies();
      if (response.data && response.data.data) {
        const policies = response.data.data;
        console.log('Policies fetched:', policies);

        const termsItem = policies.find(
          policy => policy.name.toLowerCase() === 'terms',
        );

        console.log('Terms policy found:', termsItem);

        if (termsItem && termsItem.link) {
          Linking.openURL(termsItem.link).catch(() => {
            showToast(t('could_not_open_link'), 'failure');
          });
        } else {
          console.log('Terms policy not found in:', policies);
          navigation.navigate('Performance');
        }
      }
    } catch (err) {
      console.error('Error fetching policies:', err);
      showToast(t('could_not_open_link'), 'failure');
    }
  }, [navigation, showToast, t]);

  const handleChange = (input: string) => {
    if (!input.startsWith('+91')) {
      input = '+91' + input.slice(3);
    }

    if (input.length >= 3 && input.charAt(3) !== ' ') {
      input = '+91 ' + input.slice(3);
    }

    if (input === '+91 ' || input === '+91') {
      setMobile('+91 ');
      return;
    }

    const prefix = '+91 ';
    const digitsAfterPrefix = input.slice(prefix.length).replace(/\D/g, '');

    const formattedNumber = prefix + digitsAfterPrefix.slice(0, 10);
    setMobile(formattedNumber);
  };

  const isMobileNumberValid = /^\+91\s[0-9]{10}$/.test(mobile);
  const DeletedAccountDescription = () => {
    const email = '<EMAIL>';
    const message = t('account_deleted_message');
    const parts = message.split(email);

    return (
      <Text style={richTextStyles.description}>
        {parts[0]}
        <Text style={richTextStyles.emailLink} onPress={handleEmailSupport}>
          {email}
        </Text>
        {parts[1]}
      </Text>
    );
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            style={{flex: 1}}>
            <ScrollView
              contentContainerStyle={{flexGrow: 1}}
              keyboardShouldPersistTaps="handled">
              <View style={styles.iconContainer}>
                <IconSvgView svgStyle={styles.diamondIcon} source={diamond} />
                <IconSvgView
                  svgStyle={styles.diamondIcon}
                  source={diamondInactive}
                />
                <IconSvgView
                  svgStyle={styles.diamondIcon}
                  source={diamondInactive}
                />
              </View>
              <View>
                <Text style={styles.title}>{t('trip_ready')} </Text>
              </View>
              <Input
                inputTitle={t('enter_mobile')}
                onChange={handleChange}
                value={mobile}
                keyboardType="numeric"
                maxLength={14}
              />

              <FlexContainer justifyContent="flex-end">
                <Button
                  title={t('continue')}
                  style={styles.continueBtn}
                  disabled={!isMobileNumberValid}
                  onPress={handlePress}
                />
              </FlexContainer>
              <View style={styles.termsContainer}>
                <Text
                  style={styles.termsText}
                  adjustsFontSizeToFit={true}
                  numberOfLines={2}>
                  {t('by_clicking_continue_you_agree_to_our')}{' '}
                  <Text
                    style={styles.termsHighlight}
                    onPress={handleTermsPress}>
                    {t('terms_conditions')}
                  </Text>
                </Text>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>

        <BottomUpModal
          showModal={showDeletedModal}
          onClose={handleCloseModal}
          title={t('account_deleted_title')}
          description={t('account_deleted_message')}
          descriptionComponent={<DeletedAccountDescription />}
          buttonText={t('ok')}
          onButtonClick={handleCloseModal}
          forceUpdate={false}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

const richTextStyles = StyleSheet.create({
  description: {
    fontSize: 16,
    color: 'white',
    lineHeight: 24,
  },
  emailLink: {
    fontWeight: 'bold',
    color: 'white',
    textDecorationLine: 'underline',
  },
});

const additionalStyles = StyleSheet.create({
  referralTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  successMessage: {
    color: '#4BB543',
    marginTop: 8,
    fontSize: 14,
  },
});

export default Phone;
