import React, {useEffect, useRef, useState, useDeferredValue} from 'react';
import {
  ImageBackground,
  Keyboard,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  TouchableWithoutFeedback,
} from 'react-native';
import Config from 'react-native-config';
import {SafeAreaView} from 'react-native-safe-area-context';
import styles from './LocationStyle';
import {colors, images} from '../../constants';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import FadingLine from '../../components/FadingLine/FadingLine';
import ClearableTextInput from '../../components/ClearableInput/ClearableInput';
import _, {debounce} from 'lodash';
import {userLocationContext} from '../../utils/userLocationContext';
import {useLocationContext} from '../../utils/LocationContext';
import {handleSearch} from '../../utils/MapUtils';
import MapComponent from '../../components/Map/MapComponent';
import {spacing} from '../../constants/theme';
import {useTranslation} from 'react-i18next';
import diamond from '../../icons/diamond.svg';
import diamondInactive from '../../icons/diamondGrey.svg';
import ellipseActive from '../../icons/ellipseActive.svg';
import ellipse from '../../icons/ellipse.svg';
import close from '../../icons/close.svg';
import loaderDot from '../../icons/loaderDot.svg';
import locationPin from '../../icons/locationPin.svg';
import setLocation from '../../icons/setLocation.svg';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {ScrollView} from 'react-native-gesture-handler';
import {useFocusEffect} from '@react-navigation/native';
import GeofenceService from '../../services/GeofenceService';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import useIPLocation from '../../hooks/useIPLocation';
import {useUser} from '../../hooks/useUser';

interface LocationScreenProps {
  navigation: any;
  route: any;
}

const Location: React.FC<LocationScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const user = useUser();
  const {
    setDropLocation,
    whereto,
    setPickupLocation,
    setWhereto,
    pickupAddress,
    setPickupAddress,
    activeInput,
    pickupLocation,
    dropLocation,
    setActiveInput,
  } = useLocationContext();

  const [shouldFocusPickup, setShouldFocusPickup] = useState(false);
  const [shouldFocusDrop, setShouldFocusDrop] = useState(false);
  const [clearText, setClearText] = useState(route?.params?.clearText || false);
  const [clearPickupText, setClearPickupText] = useState(
    route?.params?.clearPickupText || false,
  );
  const [clearDropText, setClearDropText] = useState(
    route?.params?.clearDropText || false,
  );
  const [preservePickupLocation, setPreservePickupLocation] = useState(
    route?.params?.preservePickupLocation || false,
  );
  const [preserveDropLocation, setPreserveDropLocation] = useState(
    route?.params?.preserveDropLocation || false,
  );

  const {userLocation} = userLocationContext();
  const pickupInputRef = useRef<TextInput>(null);
  const dropInputRef = useRef<TextInput>(null);
  const mapComponentRef = useRef<any>(null);
  const [places, setPlaces] = useState<Record<string, any>[]>([]);
  const [loading, setLoading] = useState(false);
  const [noResultsFound, setNoResultsFound] = useState(false);
  const [showSetLocation, setShowSetLocation] = useState(true);
  const [showPickupGeofence, setShowPickupGeofence] = useState(false);
  const [showDropGeofence, setShowDropGeofence] = useState(false);
  const location = useIPLocation();
  const [sameLocationError, setSameLocationError] = useState(false);
  const [showMinCharMessage, setShowMinCharMessage] = useState(false);

  useFocusEffect(
    React.useCallback(() => {
      const routeClearText = route?.params?.clearText || false;
      const routeClearPickupText = route?.params?.clearPickupText || false;
      const routeClearDropText = route?.params?.clearDropText || false;
      const routePreservePickupLocation =
        route?.params?.preservePickupLocation || false;
      const routePreserveDropLocation =
        route?.params?.preserveDropLocation || false;

      setClearText(routeClearText);
      setClearPickupText(routeClearPickupText);
      setClearDropText(routeClearDropText);
      setPreservePickupLocation(routePreservePickupLocation);
      setPreserveDropLocation(routePreserveDropLocation);

      if (routeClearText) {
        setPickupAddress('');
        setWhereto('');
        if (!routePreservePickupLocation) {
          setPickupLocation(null);
        }
        if (!routePreserveDropLocation) {
          setDropLocation(null);
        }
      } else if (routeClearPickupText && !routePreservePickupLocation) {
        setPickupAddress('');
        setPickupLocation(null);
      } else if (routeClearDropText && !routePreserveDropLocation) {
        setWhereto('');
        setDropLocation(null);
      }

      if (route?.params?.focusPickupInput) {
        setShouldFocusPickup(true);
        setShouldFocusDrop(false);
      } else if (route?.params?.focusDropInput) {
        setShouldFocusPickup(false);
        setShouldFocusDrop(true);
      }
    }, [route?.params]),
  );

  useFocusEffect(
    React.useCallback(() => {
      if (preservePickupLocation) {
        return;
      }

      if (!userLocation) {
        setShouldFocusPickup(true);
      }
      if (
        userLocation?.latitude &&
        userLocation?.longitude &&
        !clearText &&
        !clearPickupText &&
        !clearDropText &&
        !pickupLocation &&
        pickupAddress === '' 
      ) {
        setPickupAddress(`Current Location`);
        setPickupLocation(userLocation);
      }
    }, [
      userLocation,
      clearText,
      clearPickupText,
      clearDropText,
      pickupLocation,
      preservePickupLocation,
      pickupAddress,
    ]),
  );

  const focusInput = () => {
    if (clearText && !preservePickupLocation && !preserveDropLocation) {
      setPickupAddress('');
      setWhereto('');
    } else if (clearPickupText && !preservePickupLocation) {
      setPickupAddress('');
    } else if (clearDropText && !preserveDropLocation) {
      setWhereto('');
    }

    if (shouldFocusPickup) {
      pickupInputRef.current?.focus();
      setActiveInput('pickup');
      setShouldFocusPickup(false);
    } else if (shouldFocusDrop) {
      dropInputRef.current?.focus();
      setActiveInput('drop');
      setShouldFocusDrop(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const timer = setTimeout(focusInput, 300);

      return () => clearTimeout(timer);
    }, [
      shouldFocusPickup,
      shouldFocusDrop,
      clearText,
      clearPickupText,
      clearDropText,
      preservePickupLocation,
      preserveDropLocation,
    ]),
  );

  useFocusEffect(
    React.useCallback(() => {
      if (clearText && !preservePickupLocation && !preserveDropLocation) {
        setPickupAddress('');
        setWhereto('');
        setPlaces([]);
        setShowSetLocation(true);

        setPickupLocation(null);
        setDropLocation(null);

        const timer = setTimeout(() => {
          if (clearText && !preservePickupLocation) {
            setPickupAddress('');
            setPickupLocation(null);
          }
        }, 100);

        return () => clearTimeout(timer);
      }
    }, [clearText, preservePickupLocation, preserveDropLocation]),
  );

  const handlePickupSearch = debounce(async (text: string) => {
    const userCoords =
      userLocation && userLocation.latitude && userLocation.longitude
        ? userLocation
        : location?.latitude && location?.longitude
        ? {latitude: location.latitude, longitude: location.longitude}
        : null;

    if (!userCoords) return;
    const searchRadius = user?.user?.searchRadius;

    await handleSearch(
      text,
      userCoords,
      setPlaces,
      setLoading,
      setNoResultsFound,
      searchRadius,
    );
  }, 500);

  const handleDestinationSearch = debounce(async (text: string) => {
    const userCoords =
      userLocation && userLocation.latitude && userLocation.longitude
        ? userLocation
        : location?.latitude && location?.longitude
        ? {latitude: location.latitude, longitude: location.longitude}
        : null;

    if (!userCoords) return;
    const searchRadius = user?.user?.searchRadius;

    await handleSearch(
      text,
      userCoords,
      setPlaces,
      setLoading,
      setNoResultsFound,
      searchRadius,
    );
  }, 500);

  const handleInputChange = (text: string, inputType: 'pickup' | 'drop') => {
    setSameLocationError(false);
    if (inputType === 'pickup') {
      setPickupAddress(text);
      setActiveInput('pickup');
      if (text.length >= 4) {
        setShowSetLocation(false);
        handlePickupSearch(text);
      } else {
        setShowSetLocation(true);
        setPlaces([]);
      }
    } else {
      setWhereto(text);
      setActiveInput('drop');
      if (text.length >= 4) {
        setShowSetLocation(false);
        handleDestinationSearch(text);
      } else {
        setShowSetLocation(true);
        setPlaces([]);
      }
    }
  };

  useEffect(() => {
    if (activeInput === 'pickup') {
      setShowMinCharMessage(
        pickupAddress.length > 0 && pickupAddress.length < 4,
      );
    } else if (activeInput === 'drop') {
      setShowMinCharMessage(whereto.length > 0 && whereto.length < 4);
    } else {
      setShowMinCharMessage(false);
    }
  }, [pickupAddress, whereto, activeInput]);

  const checkGeofence = async (selectedPlace: any) => {
    const {data} = await GeofenceService.checkGeofence(selectedPlace);
    const isInside = data.data.isInside;
    if (isInside === undefined) {
      console.warn('Geofence check returned undefined');
      return;
    }

    return isInside;
  };

  const handlePlaceClick = async (selectedPlace: any) => {
    Keyboard.dismiss();
    let error: boolean = false;
    
    if (activeInput === 'pickup') {
      // Always store the original place name to show in the Location screen
      setPickupAddress(`${selectedPlace.name}`);
      
      // If this is the user's current location, store the coordinates but keep the display as "Current Location"
      if (selectedPlace.name === 'Current Location' && 
          selectedPlace.latitude === userLocation?.latitude && 
          selectedPlace.longitude === userLocation?.longitude) {
        // The real address will be fetched when navigating to the next screen
        // We don't need to change pickupAddress here since we want to keep showing "Current Location"
        console.log("Using current location, address will be fetched in the next screen");
      }
      
      setPickupLocation(selectedPlace);
      if (
        selectedPlace.latitude == dropLocation?.latitude &&
        selectedPlace.longitude == dropLocation?.longitude
      ) {
        setSameLocationError(true);
        error = true;
      }
      const isInside = await checkGeofence(selectedPlace);
      setShowPickupGeofence(!isInside);
      error = !isInside || error;

      if (!error && isInside) {
        setTimeout(() => {
          dropInputRef.current?.focus();
          setActiveInput('drop');
        }, 100);
      }
    } else if (activeInput === 'drop') {
      if (
        selectedPlace.latitude == pickupLocation?.latitude &&
        selectedPlace.longitude == pickupLocation?.longitude
      ) {
        setSameLocationError(true);
        error = true;
      }
      setWhereto(`${selectedPlace.name}`);
      setDropLocation(selectedPlace);
      const isInside = await checkGeofence(selectedPlace);
      setShowDropGeofence(!isInside);
      error = !isInside || error;
    }

    setPlaces([]);
    setShowSetLocation(true);
    if (showPickupGeofence || showDropGeofence) return;

    setTimeout(() => {
      if (
        pickupAddress &&
        whereto &&
        !showDropGeofence &&
        !showPickupGeofence &&
        !error
      ) {
        navigation.navigate('Pickup', {setText: ''});
      }
    }, 300);
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  // Hidden MapComponent for geocoding
  const renderHiddenMapComponent = () => {
    return (
      <View style={{ height: 1, width: 1, opacity: 0, position: 'absolute' }}>
        <MapComponent
          ref={mapComponentRef}
          setAddress={() => {}}
          region={null}
          setRegion={() => {}}
        />
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      {renderHiddenMapComponent()}
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={{flex: 1}}>
          <ImageBackground source={images.bg2} style={styles.backgroundImage}>
            <SafeAreaView style={styles.safeArea}>
              <TouchableOpacity
                onPress={() => {
                  navigation.replace('BottomTab');
                  setPickupAddress('');
                  setWhereto('');
                }}>
                <IconSvgView
                  width={16}
                  source={close}
                  svgStyle={styles.closeBtn}
                />
              </TouchableOpacity>

              <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
                <View style={styles.locationContainer}>
                  <View style={{alignItems: 'center'}}>
                    {activeInput === '' ||
                      (activeInput === 'drop' && (
                        <>
                          <IconSvgView
                            width={14}
                            svgStyle={styles.pickupIcon}
                            source={diamondInactive}
                          />
                          <View style={{marginBottom: spacing.sm, flex: 1}}>
                            <FadingLine
                              startX={0}
                              startY={1}
                              endX={1}
                              endY={0}
                              width={1}
                              height={'100%'}
                            />
                          </View>
                          <IconSvgView
                            width={10}
                            svgStyle={styles.dropIcon}
                            source={ellipseActive}
                            stroke={colors.white}
                            strokeOpacity={0.5}
                            strokeWidth={2}
                          />
                        </>
                      ))}
                    {activeInput === 'pickup' && (
                      <>
                        <IconSvgView
                          width={14}
                          svgStyle={styles.pickupIcon}
                          source={diamond}
                          stroke={colors.white}
                          strokeOpacity={0.4}
                          strokeWidth={1.5}
                        />
                        <View style={{marginBottom: spacing.sm, flex: 1}}>
                          <FadingLine
                            startX={1}
                            startY={0}
                            endX={0}
                            endY={1}
                            width={1}
                            height={'100%'}
                          />
                        </View>
                        <IconSvgView
                          width={10}
                          svgStyle={styles.dropIcon}
                          source={ellipse}
                        />
                      </>
                    )}
                  </View>

                  <View style={styles.locationNameContainer}>
                    <View style={{marginTop: spacing.lg}}>
                      <Text numberOfLines={1} style={styles.locationLabel}>
                        {t('pickup_location')}
                      </Text>
                      <ClearableTextInput
                        ref={pickupInputRef}
                        value={pickupAddress}
                        onChangeText={(text: string) =>
                          handleInputChange(text, 'pickup')
                        }
                        onClear={() => {
                          setPickupAddress('');
                          setPickupLocation(null);
                          setPreservePickupLocation(true);
                        }}
                        onFocus={() => setActiveInput('pickup')}
                      />
                    </View>
                    <View style={{marginTop: spacing.md}}>
                      <Text numberOfLines={1} style={styles.locationLabel}>
                        {t('where_to')}
                      </Text>
                      <ClearableTextInput
                        ref={dropInputRef}
                        value={whereto}
                        onChangeText={(text: string) =>
                          handleInputChange(text, 'drop')
                        }
                        onClear={() => {
                          setWhereto('');
                          setDropLocation(null);
                        }}
                        onFocus={() => setActiveInput('drop')}
                      />
                    </View>
                  </View>
                </View>
              </TouchableWithoutFeedback>

              {showMinCharMessage && (
                <Text style={styles.minCharMessage}>
                  {t('min_char_message')}
                </Text>
              )}

              <ScrollView
                keyboardShouldPersistTaps="always"
                keyboardDismissMode="none"
                onScrollBeginDrag={dismissKeyboard}>
                {places?.slice(0, 5).map((place, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handlePlaceClick(place)}>
                    <View style={styles.placesList}>
                      <IconSvgView
                        source={locationPin}
                        svgStyle={styles.locationIcon}
                      />
                      <View>
                        <Text numberOfLines={1} style={styles.placeName}>
                          {place.name}
                        </Text>
                        <Text numberOfLines={1} style={styles.vicinity}>
                          {place.vicinity}
                        </Text>
                      </View>
                    </View>
                    <FadingHorizontalLine />
                  </TouchableOpacity>
                ))}

                <TouchableOpacity
                  style={styles.setLocationContainer}
                  onPress={() => {
                    dismissKeyboard();
                    activeInput === 'pickup'
                      ? navigation.navigate('Pickup', {setText: 'set'})
                      : navigation.navigate('Destination', {fromSearch: true});
                  }}>
                  <IconSvgView source={setLocation} />
                  <Text numberOfLines={1} style={styles.setLocationTxt}>
                    {t('set_location')}
                  </Text>
                </TouchableOpacity>

                {loading && (
                  <View style={styles.loaderContainer}>
                    <IconSvgView size={36} source={loaderDot} />
                  </View>
                )}
                {noResultsFound && (
                  <TouchableWithoutFeedback onPress={dismissKeyboard}>
                    <Text numberOfLines={1} style={styles.placeName}>
                      {t('no_results')}
                    </Text>
                  </TouchableWithoutFeedback>
                )}

                {sameLocationError && (
                  <View style={styles.errorContainer}>
                    <Text numberOfLines={1} style={styles.errorTitle}>
                      {t('same_location')}
                    </Text>
                    <Text numberOfLines={2} style={styles.errorMessage}>
                      {t('change_same_location')}
                    </Text>
                  </View>
                )}
              </ScrollView>

              {showPickupGeofence && (
                <BottomUpModal
                  showModal={showPickupGeofence}
                  onClose={() => setShowPickupGeofence(false)}
                  title={t('pickup_not_serviceable')}
                  description={t('another_pickup_search')}
                  buttonText={t('select_pickup')}
                  onButtonClick={() => {
                    setShowPickupGeofence(false);
                    navigation.navigate('Location', {
                      focusPickupInput: true,
                      clearPickupText: true,
                    });
                  }}
                  forceUpdate={true}
                />
              )}
              {showDropGeofence && (
                <BottomUpModal
                  showModal={showDropGeofence}
                  onClose={() => setShowDropGeofence(false)}
                  title={t('drop_not_serviceable')}
                  description={t('another_drop_search')}
                  buttonText={t('select_whereto')}
                  onButtonClick={() => {
                    setShowDropGeofence(false);
                    navigation.navigate('Location', {
                      focusDropInput: true,
                      clearDropText: true,
                    });
                  }}
                  forceUpdate={true}
                />
              )}
            </SafeAreaView>
          </ImageBackground>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

export default Location;
