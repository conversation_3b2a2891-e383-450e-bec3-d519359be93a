import {Platform, StyleSheet, TextStyle, ViewStyle} from 'react-native';
import {sizes, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.xl * 1.5,
  },
  closeBtn: {
    marginTop: spacing.xl,
  },
  locationContainer: {
    backgroundColor: colors.darkCharcoal,
    width: '100%',
    marginTop: spacing.xl,
    paddingHorizontal: spacing.xl,
    flexDirection: 'row',
    ...(Platform.OS === 'ios' && {
      paddingBottom: spacing.xl,
    }),
  },
  pickupIcon: {
    marginBottom: spacing.sm,
    marginTop: spacing.xl,
    ...(Platform.OS === 'android' && {
      marginTop: spacing.xxl,
    }),
  },

  dropIcon: Platform.select({
    android: {
      marginBottom: spacing.xl * 1.5,
    },
    ios: {
      marginBottom: spacing.sm,
    },
  }) as ViewStyle | TextStyle,

  locationNameContainer: {
    marginLeft: spacing.md,
    flex: 1,
  },
  locationLabel: {
    color: colors.metallicSilver,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.variable,
    ...(Platform.OS === 'ios' && {
      marginBottom: spacing.sm,
    }),
    ...(Platform.OS === 'android' && {
      marginBottom: -spacing.md,
    }),
  },
  setLocationContainer: {
    marginTop: spacing.xxl,
    flexDirection: 'row',
    alignItems: 'center',
  },
  setLocationTxt: {
    fontSize: sizes.body,
    fontFamily: GeistFont.variable,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  placesList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    marginEnd: spacing.md,
    marginTop: spacing.xl / 2,
  },
  placeName: {
    color: colors.white,
    marginTop: spacing.xl,
  },
  vicinity: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginBottom: spacing.lg,
  },
  confirmBtn: {
    marginBottom: spacing.xl,
  },
  errorContainer: {
    marginTop: spacing.xxl,
    backgroundColor: 'rgba(190, 3, 3, 0.1)',
    borderWidth: 1,
    borderColor: colors.red,
    padding: spacing.sm,
    borderRadius: spacing.xs,
    justifyContent: 'center',
  },
  errorTitle: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    marginVertical: spacing.xs,
  },
  errorMessage: {
    color: colors.white,
    fontSize: sizes.body / 1.2,
    fontFamily: GeistFont.regular,
    marginVertical: spacing.xs,
  },
  minCharMessage: {
    color: colors.metallicSilver,
    fontSize: sizes.body / 1.1,
    fontFamily: GeistFont.regular,
    marginTop: spacing.sm,
  },
  loaderContainer:{
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.xxl,
  }
});
