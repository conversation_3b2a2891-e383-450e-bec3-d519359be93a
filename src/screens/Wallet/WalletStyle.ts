import {StyleSheet} from 'react-native';
import {colors, EBGaramondFont, GeistFont, sizes} from '../../constants/fonts';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  titleContainer: {
    marginBottom: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
  },

  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
    marginLeft: spacing.xl,
  },

  safeArea: {
    flex: 1,
    padding: spacing.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  backButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    fontSize: sizes.h2,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacing.md,
  },
  content: {
    flex: 1,
  },
  cashCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.lg,
    marginVertical: spacing.md,
  },
  cashCardTitle: {
    fontSize: sizes.h4,
    color: colors.white,
    marginBottom: spacing.md,
    fontFamily: GeistFont.bold,
    lineHeight: sizes.h4 * 1.5,
  },
  balanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  balanceText: {
    fontSize: sizes.largeTitle,
    fontWeight: 'bold',
    color: colors.white,
  },
  giftCardButton: {
    alignSelf: 'flex-start',
  },
  giftCardButtonText: {
    fontSize: sizes.body,
    fontWeight: '600',
    color: colors.black,
  },
  giftSection: {
    backgroundColor: '#1C1C1E',
    borderRadius: 12,
    padding: spacing.lg,
    marginVertical: spacing.md,
  },
  giftSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  giftSectionTitle: {
    fontSize: sizes.h3,
    fontWeight: 'bold',
    color: colors.white,
  },
  giftIconContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  giftIcon: {
    width: 30,
    height: 30,
    backgroundColor: '#00C853',
    borderRadius: 4,
  },
  giftSectionDescription: {
    fontSize: sizes.body,
    color: colors.grey,
    marginBottom: spacing.lg,
  },
  sendGiftButton: {
    backgroundColor: '#2C2C2E',
    borderRadius: 24,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  sendGiftButtonText: {
    fontSize: sizes.body,
    fontWeight: '600',
    color: colors.white,
  },
  paymentMethodsSection: {
    marginVertical: spacing.md,
  },
  paymentMethodsTitle: {
    fontSize: sizes.h4,
    fontFamily: GeistFont.bold,
    lineHeight: sizes.h4 * 1.5,
    color: colors.white,
    marginBottom: spacing.md,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    width: 30,
    height: 30,
    backgroundColor: '#E8E8E8',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  paymentMethodText: {
    fontSize: sizes.body,
    color: colors.white,
    fontFamily: GeistFont.regular,
    lineHeight: sizes.h4 * 1.5,
  },
  chevronRight: {
    fontSize: 24,
    color: colors.grey,
  },
  transactionsCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  transactionsTitle: {
    fontSize: sizes.h4,
    color: colors.white,
    marginVertical: spacing.lg,
    fontFamily: GeistFont.bold,
    lineHeight: sizes.h4 * 1.5,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: sizes.body,
    color: colors.white,
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: sizes.body - 2,
    color: colors.grey,
    marginBottom: 2,
  },
  transactionExpiry: {
    fontSize: sizes.body,
    color: colors.lightGrey,
  },
  transactionAmount: {
    fontSize: sizes.body,
    fontWeight: '600',
    color: colors.white,
  },
  positiveAmount: {
    color: '#00C853',
  },
  seeAllButton: {
    alignItems: 'center',
    marginTop: spacing.md,
  },
  seeAllText: {
    fontSize: sizes.body,
    color: colors.white,
    fontFamily: GeistFont.bold,
    lineHeight: sizes.h4 * 1.5,
  },
  promotionsCard: {
    backgroundColor: colors.darkCharcoal,
    borderRadius: spacing.xs,
    marginBottom: spacing.lg,
    overflow: 'hidden',
  },
  promotionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
  },
  promotionsListInner: {
    padding: spacing.md,
  },
  promotionItem: {
    paddingVertical: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noPromotionsText: {
    fontSize: sizes.h5,
    fontFamily: GeistFont.regular,
    color: colors.lightGrey,
    textAlign: 'center',
    padding: spacing.md,
  },
  promotionTitle: {
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    color: colors.white,
  },
  minimumRedeemText: {
    color: colors.grey,
    fontSize: sizes.body,
    marginTop: spacing.md,
    fontFamily: GeistFont.regular,
  },
});
