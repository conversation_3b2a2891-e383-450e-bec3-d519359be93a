import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import back from '../../icons/back.svg';
import styles from './WalletStyle';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors, images} from '../../constants';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import Button from '../../components/Button/Button';
import WalletService, {
  WalletBalance,
  WalletTransaction,
} from '../../services/WalletService';
import {useToast} from '../../components/Toast/Toast';
import {useLoader} from '../../hooks/useLoader';
import OfferService from '../../services/OfferService';
import {useUser} from '../../hooks/useUser';
import HollowButton from '../../components/Button/HollowButton/HollowButton';
import chevronDown from '../../icons/dropChevron.svg';
import chevronSide from '../../icons/chevron-right.svg';

const Wallet: React.FC<{navigation: any}> = ({navigation}) => {
  const {t} = useTranslation();
  const {user} = useUser();
  const [walletData, setWalletData] = useState<WalletBalance | null>(null);
  const [walletTransactions, setWalletTransactions] = useState<
    WalletTransaction[] | null
  >(null);
  const [promotions, setPromotions] = useState<any>();
  const {showLoader, hideLoader} = useLoader();
  const {showToast} = useToast();

  const navigateToPromotion = (promotion: any) => {
    navigation.navigate('Promotions', {promotions: [promotion]});
  };

  useEffect(() => {
    fetchWalletBalance();
    fetchWalletTransactions();
    fetchPromotions();
  }, []);

  const fetchPromotions = async () => {
    const response = await OfferService.getPromotions();
    setPromotions(response.data.data);
  };

  const fetchWalletBalance = async () => {
    try {
      showLoader();
      const response = await WalletService.getWalletBalance();
      if (response.data && response.data.data) {
        console.log(response.data.data);
        setWalletData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      showToast(t('failed_to_fetch_wallet'), 'failure');
    } finally {
      hideLoader();
    }
  };

  const fetchWalletTransactions = async () => {
    try {
      showLoader();
      const response = await WalletService.getWalletTransactions();
      if (response.data && response.data.data) {
        console.log('', response.data.data);
        setWalletTransactions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      showToast(t('failed_to_fetch_wallet'), 'failure');
    } finally {
      hideLoader();
    }
  };

  const formatDate = (isoString: string | number | Date) => {
    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;

    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
  };

  const renderTransaction = (transaction: any) => {
    let title = '';
    let amountText = '';

    if (transaction.type === 'credit') {
      title = `#${transaction.source}`;
      amountText = `+₹${transaction.amount}`;
    } else if (transaction.type === 'debit') {
      title = `#${transaction.id}`;
      amountText = `- ₹${transaction.amount}`;
    }

    return (
      <View key={transaction.id} style={styles.transactionItem}>
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionTitle}>{title}</Text>
          <Text style={styles.transactionDate}>
            {formatDate(transaction.createdAt)}
          </Text>
          {transaction.expires && (
            <Text style={styles.transactionExpiry}>
              {t('expires')}: {transaction.expires}
            </Text>
          )}
        </View>
        <Text
          style={[
            styles.transactionAmount,
            transaction.type === 'credit' ? styles.positiveAmount : {},
          ]}>
          {amountText}
        </Text>
      </View>
    );
  };

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.titleContainer}>
          <TouchableOpacity
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
            onPress={() => navigation.goBack()}>
            <IconSvgView source={back} />
          </TouchableOpacity>
          <Text style={styles.title}>{t('wallet')}</Text>
        </View>
        <FadingHorizontalLine />
        <ScrollView style={styles.content}>
          {/* Cash Card */}
          <View style={styles.cashCard}>
            <Text style={styles.cashCardTitle}>{t('mapto_cash')}</Text>
            <View style={styles.balanceContainer}>
              <Text style={styles.balanceText}>
                ₹
                {walletData
                  ? parseFloat(walletData.totalBalance).toFixed(2)
                  : '0.00'}
              </Text>
            </View>
            <Button
              title={t('redeem')}
              style={styles.giftCardButton}
              onPress={() => navigation.navigate('RedeemCash')}
              disabled={true}
              disabledBorderColor={colors.davyGrey}
            />
          </View>
          <Text style={styles.minimumRedeemText}>
            {t('minimum_redeemable')}: ₹
            {user?.user?.MinimumRedeemableAmount || 250}
          </Text>

          <View style={styles.paymentMethodsSection}>
            <Text style={styles.paymentMethodsTitle}>
              {t('payment_methods')}
            </Text>

            {user?.paymentDetails?.upi_id ? (
              <TouchableOpacity
                onPress={() => navigation.navigate('PaymentMethod')}
                style={styles.paymentMethod}>
                <Text style={styles.paymentMethodText}>
                  {user.paymentDetails.upi_id}
                </Text>
                <Text style={styles.chevronRight}>›</Text>
              </TouchableOpacity>
            ) : (
              <HollowButton
                title={t('add_upi')}
                onPress={() => navigation.navigate('PaymentMethod')}
              />
            )}
          </View>
          <FadingHorizontalLine />
          {walletTransactions && walletTransactions.length > 0 && (
            <>
              <Text style={styles.transactionsTitle}>
                {t('recent_transactions')}
              </Text>
              <View style={styles.transactionsCard}>
                {walletTransactions?.slice(0, 3).map(renderTransaction)}
                <FadingHorizontalLine />
                <TouchableOpacity
                  style={styles.seeAllButton}
                  onPress={() =>
                    navigation.navigate('AllTransactions', {walletTransactions})
                  }>
                  <Text style={styles.seeAllText}>{t('see_all')}</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
          <FadingHorizontalLine />

          <View style={styles.paymentMethodsSection}>
            <Text style={styles.paymentMethodsTitle}>{t('promotions')}</Text>

            <View style={styles.promotionsCard}>
              <View style={styles.promotionsListInner}>
                {promotions &&
                Array.isArray(promotions) &&
                promotions.length > 0 ? (
                  promotions.map((item: any, index: number) => (
                    <React.Fragment key={item.id}>
                      <TouchableOpacity
                        onPress={() => navigateToPromotion(item)}
                        style={styles.promotionItem}>
                        <Text style={styles.promotionTitle}>
                          {item.type === 'referral'
                            ? t('user_referral_title')
                            : t('triple_treat_bonus_title')}
                        </Text>

                        <IconSvgView size={14} source={chevronSide} />
                      </TouchableOpacity>
                      {index < promotions.length - 1 && (
                        <FadingHorizontalLine />
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <Text style={styles.noPromotionsText}>
                    {t('no_promotions')}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Wallet;
