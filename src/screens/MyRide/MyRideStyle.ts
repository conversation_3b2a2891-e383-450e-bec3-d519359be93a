import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';
import {size} from 'lodash';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  safeArea: {
    flex: 1,
    paddingHorizontal: spacing.md * 1.5,
    marginTop: spacing.xxl,
  },

  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  title: {
    fontSize: sizes.h3,
    color: colors.lightGrey,
    fontFamily: EBGaramondFont.regular,
  },

  subtitle: {
    backgroundColor: colors.darkGrey,
    height: 60,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginVertical: spacing.xl,
  },

  subtitleText: {
    color: colors.white,
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
  },

  rideContainerTop: {
    borderTopEndRadius: spacing.xs,
    borderTopStartRadius: spacing.xs,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.darkGrey,
  },

  rideContainer: {
    flex: 1,
    padding: spacing.md,
    alignItems: 'flex-start',
    marginBottom: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderBottomEndRadius: spacing.xs,
    borderBottomStartRadius: spacing.xs,
  },

  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },

  kmTxt: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
  },

  activeTab: {
    textAlign: 'center',
    borderBottomWidth: 2,
    borderBottomColor: colors.white,
  },

  locationLabel: {
    color: colors.grey,
    fontSize: sizes.body,
    fontFamily: GeistFont.variable,
  },

  rideText: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
  },

  vehicleIcon: {
    width: 40,
    height: 40,
    margin: spacing.md,
  },

  dateText: {
    fontSize: sizes.h6,
    color: colors.white,
    marginVertical: spacing.xs,
    fontFamily: GeistFont.regular,
  },

  detailsContainer: {
    alignItems: 'flex-end',
    margin: spacing.md,
  },

  detailsText: {
    fontSize: sizes.body,
    color: colors.grey,
  },

  whiteText: {
    color: colors.white,
    fontFamily: GeistFont.regular,
  },

  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statusText: {
    fontSize: sizes.body,
    textTransform: 'capitalize',
    fontFamily: GeistFont.bold,
    color: colors.white,
  },
  loaderImage: {
    width: 25,
    height: 25,
  },
});
