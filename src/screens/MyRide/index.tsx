import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  ImageBackground,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import styles from './MyRideStyle';
import {useTranslation} from 'react-i18next';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {colors, GeistFont} from '../../constants';
import {useLoader} from '../../hooks/useLoader';
import {useToast} from '../../components/Toast/Toast';
import {spacing} from '../../constants/theme';
import TripService from '../../services/TripService';
import {useFocusEffect} from '@react-navigation/native';
import {STATUS_CODE} from '../../constants/constants';
import Button from '../../components/Button/Button';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import back from '../../icons/back.svg';

interface RideDetailsScreenProps {
  navigation: any;
  route: {
    params?: {
      fromHelp?: boolean;
    };
  };
}

enum RideStatus {
  Completed = 'completed',
  DriverCancelled = 'driver_cancelled',
  UserCancelled = 'user_cancelled',
  Aborted = 'aborted',
}

interface Ride {
  id: number;
  created_at: string;
  updated_at: string;
  driverId: number;
  userId: number;
  status: string;
  source: {latitude: number; longitude: number};
  destination: {latitude: number; longitude: number};
  source_address: string;
  destination_address: string;
  distance: number;
  duration: number;
  fare: number;
  tip: number | null;
  vehicleType: string;
}

const MyRide: React.FC<RideDetailsScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {showLoader, hideLoader, loading} = useLoader();
  const {showToast} = useToast();

  const fromHelp = route.params?.fromHelp || false;

  const [allRides, setAllRides] = useState<Ride[]>([]);
  const [page, setPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchRides = async (currentPage: number, isRefreshing = false) => {
    try {
      if (currentPage === 1 && !isRefreshing) showLoader();
      else if (!isRefreshing) setIsFetchingMore(true);
      console.log('fetching rides', currentPage);
      const response = await TripService.getTrips(currentPage, 20);

      if (response.data?.data) {
        const fetchedRides = response.data.data.data;
        const filteredRides = fetchedRides.filter(
          (ride: Ride) =>
            ride.status === RideStatus.Completed ||
            ride.status === RideStatus.DriverCancelled ||
            ride.status === RideStatus.UserCancelled ||
            ride.status === RideStatus.Aborted,
        );

        setAllRides(prevRides => {
          if (currentPage === 1) return filteredRides;

          const existingIds = new Set(prevRides.map(ride => ride.id));

          const uniqueNewRides = filteredRides.filter(
            (ride: {id: number}) => !existingIds.has(ride.id),
          );

          return [...prevRides, ...uniqueNewRides];
        });

        setHasMoreData(filteredRides.length > 0);
      }
    } catch (err: any) {
      const status = err?.response?.status;
      const code = err.response.data.response.code;

      if ([STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)) {
        return;
      } else if (STATUS_CODE.bad_request) {
        code === 'getting_trips_failed' &&
          showToast(t('error_fetching_trip'), 'failure');
      }
    } finally {
      if (currentPage === 1 && !isRefreshing) hideLoader();
      else if (!isRefreshing) setIsFetchingMore(false);
      if (isRefreshing) setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchRides(1);

      return () => {};
    }, []),
  );

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setPage(1);
    fetchRides(1, true);
  }, []);

  const loadMoreRides = async () => {
    if (!isFetchingMore && hasMoreData) {
      setIsFetchingMore(true);
      const nextPage = page + 1;

      try {
        await fetchRides(nextPage);
        setPage(nextPage);
      } finally {
        setIsFetchingMore(false);
      }
    }
  };

  const getVehicleImage = useCallback((vehicleType: string) => {
    const vehicleImages: {[key: string]: any} = {
      AUTORICKSHAW: images.auto,
    };
    return vehicleImages[vehicleType] || images.auto;
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }, []);

  const formatTime = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  }, []);

  const renderRideItem = useCallback(
    ({item}: {item: Ride}) => (
      <>
        <View>
          <View style={styles.rideContainerTop}>
            <View style={{flexDirection: 'row'}}>
              <Image
                source={getVehicleImage(item.vehicleType)}
                style={styles.vehicleIcon}
              />
              <View style={{alignItems: 'flex-start', margin: 12}}>
                <Text style={styles.dateText}>
                  {formatTime(item.updated_at)}
                </Text>
                <Text style={styles.locationLabel}>
                  {formatDate(item.created_at)}
                </Text>
              </View>
            </View>
            <View style={styles.detailsContainer}>
              <Text style={styles.dateText}>
                {t('rupee')}
                {Math.round(item.fare)}
              </Text>
              <Text style={styles.locationLabel}>{item.distance} km</Text>
            </View>
          </View>
          <View style={styles.rideContainer}>
            <View style={styles.statusContainer}>
              <Text style={styles.locationLabel}>{t('status')}: </Text>
              <Text style={[styles.statusText]}>
                {item.status === RideStatus.Completed
                  ? t('completed')
                  : item.status === RideStatus.DriverCancelled
                  ? t('driver_cancelled')
                  : item.status === RideStatus.UserCancelled
                  ? t('user_cancelled')
                  : t('aborted')}
              </Text>
            </View>
            <Text style={styles.locationLabel}>{t('pickup_location')}: </Text>
            <Text numberOfLines={2} style={styles.whiteText}>
              {item.source_address}
            </Text>
            <View style={{marginVertical: spacing.sm}}>
              <FadingHorizontalLine />
            </View>
            <Text style={styles.locationLabel}>{t('where_to')}: </Text>
            <Text numberOfLines={2} style={styles.whiteText}>
              {item.destination_address}
            </Text>
            {fromHelp && (
              <View style={{marginTop: spacing.md, width: '100%'}}>
                <Button
                  onPress={() =>
                    navigation.navigate('FaQ', {
                      tripId: item.id,
                      category: 'Trip',
                    })
                  }
                  title={t('get_help')}
                  style={{width: '100%'}}
                />
              </View>
            )}
          </View>
        </View>
      </>
    ),
    [t, getVehicleImage, formatDate, navigation, fromHelp],
  );

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <View style={styles.safeArea}>
        <View style={styles.titleContainer}>
          {fromHelp && (
            <TouchableOpacity
              style={{marginRight: spacing.lg}}
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}
              onPress={() => navigation.goBack()}>
              <IconSvgView source={back} />
            </TouchableOpacity>
          )}
          <Text style={styles.title}>{t('my_rides')}</Text>
        </View>
        <View style={{marginBottom: spacing.xxl}}>
          <FadingHorizontalLine />
        </View>
        <FlatList
          data={allRides}
          renderItem={renderRideItem}
          keyExtractor={item => item.id.toString()}
          onEndReached={loadMoreRides}
          onEndReachedThreshold={0.5}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.lightGrey}
            />
          }
          ListFooterComponent={
            isFetchingMore ? (
              <View
                style={{
                  padding: spacing.md,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Image
                  source={require('../../icons/LOADER.gif')}
                  style={styles.loaderImage}
                />
              </View>
            ) : null
          }
          ListEmptyComponent={
            !loading && allRides.length === 0 ? (
              <FlexContainer justifyContent="center" alignItems="center">
                <Text
                  style={{
                    color: colors.lightGrey,
                    fontFamily: GeistFont.regular,
                  }}>
                  {t('no_rides_found')}
                </Text>
              </FlexContainer>
            ) : null
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            {flexGrow: 1},
            allRides.length === 0 && {justifyContent: 'center'},
          ]}
        />
      </View>
    </ImageBackground>
  );
};

export default MyRide;
