import React from 'react';
import { render, act } from '@testing-library/react-native';
import Welcome from '.';
import {jest} from '@jest/globals';

const mockNavigation = {
  navigate: jest.fn(),
};

describe('Welcome Component', () => {
  it('renders correctly and navigates after 3 seconds', async () => {
    jest.useFakeTimers();

    const { getByText } = render(<Welcome navigation={mockNavigation} route={{ params: { name: '<PERSON>' } }} />);

    expect(getByText('Welcome back')).toBeTruthy();
    expect(getByText('John')).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Start');
  });
});