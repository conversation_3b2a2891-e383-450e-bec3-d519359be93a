import React, {useEffect} from 'react';
import {Image, ImageBackground, SafeAreaView, Text} from 'react-native';
import {images} from '../../constants';
import styles from './WelcomeStyle';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import {useTranslation} from 'react-i18next';
import {useUser} from '../../hooks/useUser';

interface UserSetupProps {
  navigation: any;
  route: any;
}

const Welcome: React.FC<UserSetupProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {user, fetchUser} = useUser();

  useEffect(() => {
    fetchUser();
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.reset({
        index: 0,
        routes: [{name: 'BottomTab'}],
      });
    }, 3000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <ImageBackground source={images.bg2} style={styles.backgroundImage}>
      <SafeAreaView style={styles.safeArea}>
        <FlexContainer alignItems="center" justifyContent="center">
          <Text
            numberOfLines={1}
            style={styles.welcomeText}>
            {t('welcome_back')}
          </Text>
          <Text numberOfLines={1}  style={styles.title}>
            {user?.name ?? ''}
          </Text>
          <Image
            source={require('../../icons/LOADER.gif')}
            style={styles.loaderImage}
          />
        </FlexContainer>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Welcome;
