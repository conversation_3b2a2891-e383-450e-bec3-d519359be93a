import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing.xl * 1.5,
  },
  welcomeText: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.body,
    textAlign: 'center',
    color: colors.grey,
  },
  title: {
    fontFamily: EBGaramondFont.regular,
    fontSize: sizes.largeTitle,
    textAlign: 'center',
    color: colors.white,
  },
  loaderImage: {
    marginTop: spacing.md,
    width: 50,
    height: 50,
  },
});
