import React from 'react';
import { View, Text, ImageBackground, StyleSheet } from 'react-native';
import wifi from '../../icons/wifi.svg';
import images from '../../constants/images';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import Button from '../../components/Button/Button';
import { GeistFont, colors, sizes } from '../../constants';
import { spacing } from '../../constants/theme';

interface OfflineScreenProps {
    handleRefresh: () => void;
    retryCount: number;
}

const OfflineScreen: React.FC<OfflineScreenProps> = ({ handleRefresh, retryCount }) => (
    <ImageBackground source={images.map} style={styles.background}>
        <View style={styles.container}>
            <IconSvgView source={wifi} size={40} />
            <Text style={styles.offlineText}>
                We can't reach our network right now. Please check your connection.
            </Text>
            {retryCount > 0 && (
                <Text style={styles.retryText}>
                    Attempting to reconnect...
                </Text>
            )}
            <Button title="Refresh" style={styles.refreshBtn} onPress={handleRefresh} />
        </View>
    </ImageBackground>
);

const styles = StyleSheet.create({
    background: { flex: 1, resizeMode: 'cover', justifyContent: 'center' },
    container: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    offlineText: { color: colors.grey, textAlign: 'center', maxWidth: '80%', marginTop: spacing.lg, fontFamily: GeistFont.regular, fontSize: sizes.h6 },
    retryText: { color: colors.grey, textAlign: 'center', marginTop: spacing.md, fontFamily: GeistFont.regular, fontSize: sizes.h6 },
    refreshBtn: { width: '80%', marginTop: spacing.xxl },
});

export default OfflineScreen;
