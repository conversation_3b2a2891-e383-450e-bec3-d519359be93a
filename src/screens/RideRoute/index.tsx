import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import MapComponent from '../../components/Map/MapComponent';
import {
  AppState,
  Image,
  ImageBackground,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import MapView, {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>yline} from 'react-native-maps';
import handler from '../../icons/handler.svg';
import images from '../../constants/images';
import FlexContainer from '../../components/FlexContainer/FlexContainer';
import {useTranslation} from 'react-i18next';
import {colors, GeistFont} from '../../constants';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import FadingHorizontalLine from '../../components/FadingLine/FadingHorizontalLine';
import {useToast} from '../../components/Toast/Toast';
import pickupIcon from '../../icons/pickupIcon.svg';
import dropIcon from '../../icons/dropIcon.svg';
import myLocation from '../../icons/my_location.svg';
import {zoomToRoute} from '../../utils/MapUtils';
import styles from './RideRouteStyle';
import {useRideDetails} from '../../hooks/useRideDetails';
import {STATUS_CODE} from '../../constants/constants';
import polyline from '@mapbox/polyline';
import RideService from '../../services/RideService';
import {userLocationContext} from '../../utils/userLocationContext';
import {useFocusEffect} from '@react-navigation/native';
import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import {spacing} from '../../constants/theme';
import TripService from '../../services/TripService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigationRef} from '../../router/navigationService';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StatusBar} from 'react-native';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';

interface RideRouteScreenProps {
  navigation: any;
  route: any;
}

const RideRoute: React.FC<RideRouteScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const mapViewRef = useRef<MapView>(null);
  const {userLocation} = userLocationContext();
  const {showToast} = useToast();
  const [address, setAddress] = useState<string>('');
  const [region, setRegion] = useState<LatLng | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<
    {latitude: number; longitude: number}[]
  >([]);
  const [imageError, setImageError] = useState(false);
  const {
    tripDetails,
    timeRemaining,
    driverRoute,
    setDriverRoute,
    setTimeRemaining,
    setTripDetails,
    driverDetails,
    clearLocalStorage,
    fetchDriverDetails,
    fetchTripDetails,
  } = useRideDetails();
  const bottomSheetRef = useRef(null);
  const [bottomSheetIndex, setBottomSheetIndex] = useState(1);
  const animatedPosition = useSharedValue(1);
  const snapPoints = useMemo(() => ['20%', '40%'], []);
  const [iconLoaded, setIconLoaded] = useState(false);
  const [markerReady, setMarkerReady] = useState(false);
  // const [showDriverHaltedModal, setShowDriverHaltedModal] = useState(false);
  const [markerRotation, setMarkerRotation] = useState<number>(0);

  

  useEffect(() => {
    const handleAppStateChange = async () => {
      const rideStatus = await AsyncStorage.getItem('rideStatus');
      const driverCanceled = await AsyncStorage.getItem('driverCanceled');
      const polyline = await AsyncStorage.getItem('locationUpdate');
      const driverHalted = await AsyncStorage.getItem('driverHalted');
      const rideAborted = await AsyncStorage.getItem('rideAborted');

      await fetchDriverDetails();
      await fetchTripDetails();
      if (rideStatus === 'COMPLETED') {
        await AsyncStorage.removeItem('rideStatus');
        navigationRef.current?.reset({routes: [{name: 'CollectCash'}]});
      } else if (driverCanceled) {
        showToast(t('driver_cancel'), 'failure');
        await clearLocalStorage();
        await AsyncStorage.removeItem('rideStatus');
        navigationRef.current?.reset({routes: [{name: 'Confirm'}]});
      } else if (polyline) {
        setDriverRoute(polyline);
      } /* Safety check commented out
      else if (driverHalted === 'true') {
        setShowDriverHaltedModal(true);
      } */ else if (rideAborted) {
        showToast(t('ride_aborted'), 'failure');
        await clearLocalStorage();
        navigationRef.current?.reset({routes: [{name: 'BottomTab'}]});
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    // Safety check function commented out
    const checkDriverHalted = async () => {
      /* const driverHalted = await AsyncStorage.getItem('driverHalted');
      if (driverHalted === 'true') {
        setShowDriverHaltedModal(true);
      } */
    };

    // checkDriverHalted(); // Safety check disabled

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('transparent');
    StatusBar.setTranslucent(true);
  }, []);

  const handleSheetChanges = useCallback(
    (index: React.SetStateAction<number>) => {
      StatusBar.setBarStyle('light-content');
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setTranslucent(true);

      animatedPosition.value = withSpring(Number(index));
      setBottomSheetIndex(index);
    },
    [],
  );

  const mapContainerStyle = useAnimatedStyle(() => {
    const flex = interpolate(animatedPosition.value, [0, 1], [0.8, 0.6]);
    return {
      flex,
      position: 'relative',
      width: '100%',
      height: '100%',
    };
  });

  const locationIconsStyle = useAnimatedStyle(() => {
    const bottomPosition = interpolate(animatedPosition.value, [0, 1], [5, 5]);

    return {
      position: 'absolute',
      right: spacing.lg,
      bottom: `${bottomPosition}%`,
      zIndex: 1,
      flexDirection: 'column',
      gap: 8,
    };
  });

  useFocusEffect(
    useCallback(() => {
      if (routeCoordinates.length > 0) {
        const interval = setInterval(() => {
          if (mapViewRef.current) {
            zoomToRoute(mapViewRef, routeCoordinates);
            clearInterval(interval);
          } else {
            console.log('mapViewRef is not set');
          }
        }, 500);

        return () => clearInterval(interval);
      }
    }, [routeCoordinates, mapViewRef.current]),
  );

  useFocusEffect(
    useCallback(() => {
      const fetchDetails = async () => {
        const tripId = await AsyncStorage.getItem('tripId');
        if (tripId) {
          await fetchDriverDetails(JSON.parse(tripId));
        }
        if (tripDetails?.id) {
          try {
            const response = await RideService.getPolyline(
              Number(tripDetails.id),
            );
            if (response.status === STATUS_CODE.ok) {
              console.log('Driver route:', response.data.data);
              setDriverRoute(response.data.data.route);
              setTimeRemaining(response.data.data.remainingTime);
            }
          } catch (err: any) {
            const status = err?.response?.status;
            const code = err.response.data.response.code;

            if (
              [STATUS_CODE.not_found, STATUS_CODE.server_error].includes(status)
            ) {
              return;
            } else if (STATUS_CODE.bad_request) {
              code === 'driver_location_not_found' &&
                showToast(t('driver_location_not_found'), 'failure');
            }
          }
        }
      };

      fetchDetails();
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      if (driverRoute && typeof driverRoute === 'string') {
        try {
          const decodedCoordinates = polyline
            .decode(driverRoute)
            .map(([latitude, longitude]) => ({
              latitude: Number(latitude),
              longitude: Number(longitude),
            }));
          setRouteCoordinates(decodedCoordinates);

          if (decodedCoordinates.length >= 2) {
            const computeRotation = (
              coords: {latitude: number; longitude: number}[],
            ): number => {
              if (coords.length < 2) return 0;

              const point1 = coords[0];
              const point2 = coords[1];

              const dx = point2.longitude - point1.longitude;
              const dy = point2.latitude - point1.latitude;

              let angle = (Math.atan2(dx, dy) * 180) / Math.PI;

              return angle + 180;
            };

            setMarkerRotation(computeRotation(decodedCoordinates));
          }
        } catch (error) {
          console.error('Error decoding route:', error);
        }
      }
    }, [driverRoute]),
  );

  useEffect(() => {
    let interval: ReturnType<typeof setInterval> | undefined;

    if (tripDetails?.id) {
      interval = setInterval(async () => {
        try {
          const response = await RideService.getPolyline(
            Number(tripDetails.id),
          );
          if (response.status === STATUS_CODE.ok) {
            setDriverRoute(response.data.data.route);
            setTimeRemaining(response.data.data.remainingTime);
          }
        } catch (err) {
          console.log(err);
        }
      }, 10000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tripDetails?.id]);

  const handleGetLocation = () => {
    if (userLocation) {
      const {latitude, longitude} = userLocation;
      mapViewRef.current?.animateToRegion(
        {
          latitude,
          longitude,
          latitudeDelta: 0.008,
          longitudeDelta: 0.008,
        },
        100,
      );
    }
  };

  /* Safety check function completely removed
  const handleSafetyCheck = async () => {
    setShowDriverHaltedModal(false);
    await AsyncStorage.removeItem('driverHalted');
  }; */

  // Safety check useFocusEffect commented out
  /* useFocusEffect(
    useCallback(() => {
      const checkHaltedOnFocus = async () => {
        const driverHalted = await AsyncStorage.getItem('driverHalted');
        if (driverHalted === 'true') {
          setShowDriverHaltedModal(true);
        }
      };
      checkHaltedOnFocus();
    }, []),
  ); */

  // Safety check interval useFocusEffect commented out
  /* useFocusEffect(
    useCallback(() => {
      let interval: ReturnType<typeof setInterval>;

      const checkHaltedOnFocus = async () => {
        const driverHalted = await AsyncStorage.getItem('driverHalted');
        if (driverHalted === 'true') {
          setShowDriverHaltedModal(true);
          await AsyncStorage.removeItem('driverHalted');
        }
      };

      interval = setInterval(checkHaltedOnFocus, 10000);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }, []),
  ); */

  return (
    <View style={{backgroundColor: colors.darkGrey, flex: 1}}>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
      <SafeAreaView style={{flex: 1}} edges={['bottom']}>
        <ImageBackground
          source={images.bg2}
          style={[styles.backgroundImage, {backgroundColor: 'black'}]}>
          <Animated.View style={mapContainerStyle}>
            <MapComponent
              ref={mapViewRef}
              marker={false}
              setAddress={setAddress}
              region={region}
              setRegion={setRegion}>
              {routeCoordinates.length > 0 && (
                <>
                  <Marker
                    anchor={{x: 0.5, y: 0.5}}
                    tracksViewChanges={!markerReady || !iconLoaded}
                    coordinate={routeCoordinates[0]}
                    title="Pickup Location">
                    <View
                      onLayout={() => {
                        setTimeout(() => setIconLoaded(true), 500);
                      }}>
                      {driverDetails?.vehicle?.vehicle_type ===
                      'AUTORICKSHAW' ? (
                        <Image
                          source={images.autoTop}
                          style={{
                            width: 60,
                            height: 60,
                            transform: [{rotate: `${markerRotation}deg`}],
                          }}
                          resizeMode="contain"
                        />
                      ) : (
                        <IconSvgView size={50} source={pickupIcon} />
                      )}
                    </View>
                  </Marker>
                  <Marker
                    anchor={{x: 0.5, y: 0.5}}
                    tracksViewChanges={false}
                    coordinate={routeCoordinates[routeCoordinates.length - 1]}
                    title="Drop Location">
                    <IconSvgView source={dropIcon} />
                  </Marker>
                  {routeCoordinates.length > 1 && (
                    <Polyline
                      coordinates={routeCoordinates}
                      strokeColor="#FFFFFF"
                      strokeColors={['#FFFFFF']}
                      strokeWidth={4}
                    />
                  )}
                </>
              )}
            </MapComponent>
            <Animated.View style={locationIconsStyle}>
              <TouchableOpacity
                style={styles.locationContainer}
                onPress={handleGetLocation}>
                <IconSvgView width={24} source={myLocation} />
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>

          <BottomSheet
            ref={bottomSheetRef}
            index={1}
            snapPoints={snapPoints}
            onChange={handleSheetChanges}
            enableOverDrag={false}
            handleComponent={() => (
              <View
                style={{backgroundColor: colors.black, alignItems: 'center'}}>
                <IconSvgView width={60} source={handler} />
              </View>
            )}>
            <BottomSheetScrollView
              style={styles.contentContainer}
              showsVerticalScrollIndicator={false}
              bounces={true}
              contentContainerStyle={{paddingBottom: 40}}>
              <View
                style={{
                  minHeight: 30,
                }}>
                <Text numberOfLines={1} style={styles.arriveTxt}>
                  <Text style={{color: colors.grey}}> {t('arriving_in')} </Text>
                  {Math.round(timeRemaining / 60)} {t('min')}
                </Text>
                <Text style={styles.location} numberOfLines={1}>
                  <Text style={{color: colors.grey}}>{t('heading_to')} </Text>
                  {tripDetails?.destination_address ?? ''}
                </Text>
              </View>
              <FadingHorizontalLine />

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <View style={[styles.vehicleContainer, {flex: 1}]}>
                  <Text style={styles.makeTxt}>
                    {driverDetails?.vehicle?.vehicle_type === 'AUTORICKSHAW'
                      ? t('autoRickshaw')
                      : ''}
                  </Text>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    {driverDetails?.vehicle?.vehicle_type === 'AUTORICKSHAW' ? (
                      <Image source={images.auto} style={styles.autoIcon} />
                    ) : driverDetails?.vehicle?.vehicle_type === 'CAR' ? (
                      <Image source={images.car} />
                    ) : null}
                    <Text style={styles.vehicleNo}>
                      {driverDetails?.vehicle?.vehicle_no ?? ''}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                    justifyContent: 'flex-end',
                  }}>
                  <Image
                    source={
                      imageError || !driverDetails?.profile_photo
                        ? images.user
                        : {uri: driverDetails?.profile_photo}
                    }
                    style={styles.imageContainer}
                    resizeMode="cover"
                    onError={() => setImageError(true)}
                  />

                  <View
                    style={{
                      marginLeft: 10,
                      justifyContent: 'center',
                    }}>
                    <Text
                      numberOfLines={1}
                      style={[
                        styles.driverName,
                        {
                          flexWrap: 'wrap',
                          maxWidth: 120,
                        },
                      ]}>
                      {driverDetails?.name ?? ''}
                    </Text>

                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 4,
                      }}>
                      {driverDetails?.average_rating === 0 ? (
                        <Text
                          style={{
                            color: 'white',
                            fontSize: 14,
                            fontFamily: GeistFont.regular,
                          }}>
                          {t('no_rating', 'No rating yet')}
                        </Text>
                      ) : (
                        <>
                          <Image
                            source={images.star}
                            style={{
                              width: 16,
                              height: 16,
                            }}
                          />
                          <Text
                            style={{
                              color: 'white',
                              fontSize: 14,
                              marginLeft: 5,
                              fontFamily: GeistFont.regular,
                            }}>
                            {driverDetails?.average_rating}
                          </Text>
                        </>
                      )}
                    </View>
                  </View>
                </View>
              </View>

              <FadingHorizontalLine />
              <View
                style={{
                  minHeight: 70,
                  height: 70,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: spacing.sm,
                }}>
                <View>
                  <Text
                    style={{
                      color: colors.lightGrey,
                      fontFamily: GeistFont.regular,
                    }}>
                    {t('total_fare')}
                  </Text>
                  <Text style={styles.rateTxt}>₹{tripDetails?.fare ?? ''}</Text>
                </View>
                <View
                  style={{justifyContent: 'flex-end', alignItems: 'flex-end'}}>
                  <Text numberOfLines={2} style={styles.arriveTxt}>
                    {t('cash_upi_app')}
                  </Text>
                </View>
              </View>
            </BottomSheetScrollView>
          </BottomSheet>
          {/* <BottomUpModal
            showModal={showDriverHaltedModal}
            onClose={() => setShowDriverHaltedModal(false)}
            title={t('safety_check')}
            description={t('driver_halted')}
            buttonText={t('i_am_safe')}
            // Safety check button click handler commented out
            onButtonClick={() => console.log('Safety check button clicked - functionality disabled')}
            forceUpdate={false}
          /> */}
        </ImageBackground>
      </SafeAreaView>
    </View>
  );
};

export default RideRoute;
