import {Platform, StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, theme, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },

  contentContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.black,
  },
  backContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
  },
  arriveTxt: {
    color: colors.lightGrey,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
    maxWidth: '70%',
    minHeight: 30,
  },
  pinTxt: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
  },
  pinCard: {
    backgroundColor: colors.white,
    textAlign: 'center',
    width: spacing.xxl,
    height: spacing.xxl,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  pinNumber: {
    color: colors.black,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
  },
  vehicleContainer: {
    paddingVertical: spacing.md,
  },
  makeTxt: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginBottom: spacing.sm,
    minHeight: 20,
  },
  vehicleNo: {
    color: colors.white,
    fontSize: sizes.body / 1.1,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    backgroundColor: 'rgba(45, 46, 50, 1)',
    padding: spacing.sm,
  },
  imageContainer: {
    width: 50,
    height: 50,
    resizeMode: 'cover',
    borderWidth: 2,
    borderColor: colors.white,
  },
  driverName: {
    color: colors.white,
    fontSize: sizes.body,
    fontFamily: GeistFont.regular,
  },
  ratingContainer: {
    color: colors.white,
    fontSize: sizes.h6,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.sm,
    marginTop: spacing.sm,
  },
  ratingText: {
    color: colors.white,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.regular,
    marginLeft: spacing.xs,
  },
  rateTxt: {
    color: colors.white,
    fontSize: sizes.h3,
    fontFamily: EBGaramondFont.EBbold,
    marginLeft: spacing.xs,
  },
  locationLabel: {
    color: colors.grey,
    fontSize: sizes.h3 / 2,
    fontFamily: GeistFont.variable,
    marginTop: spacing.lg,
  },
  location: {
    color: colors.lightGrey,
    fontSize: sizes.h6,
    fontFamily: GeistFont.variable,
    flexWrap: 'wrap',
    marginBottom: spacing.xl,
  },
  locationPositionContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    margin: spacing.xl,
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.davyGrey,
    borderRadius: 5,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
    marginVertical: spacing.sm,
  },
  autoIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
});
