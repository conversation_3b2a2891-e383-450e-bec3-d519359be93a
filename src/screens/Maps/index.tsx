import React, {useCallback, useEffect, useRef, useState} from 'react';
import {AppState, Platform, Text, TouchableOpacity, View} from 'react-native';
import styles from './MapStyle';
import {userLocationContext} from '../../utils/userLocationContext';
import MapComponent from '../../components/Map/MapComponent';
import {LatLng} from 'react-native-maps';
import {useFocusEffect} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import IconSvgView from '../../components/IconSvgView/IconSvgView';
import diamond from '../../icons/diamond.svg';
import myLocation from '../../icons/my_location.svg';
import {colors} from '../../constants';
import {useLocationContext} from '../../utils/LocationContext';
import Rating from '../Rating';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useGeofence} from '../../hooks/useGeofence';
import {
  checkForegorundLocationPermission,
  checkNotificationPermission,
  requestCallPhonePermission,
  requestLocationPermissionWhenInUse,
  requestNotificationPermission,
} from '../../constants/permissions';
import {useRideDetails} from '../../hooks/useRideDetails';
import {initializeFcm} from '../../../firebase.ts';
import BottomUpModal from '../../components/BottomUpModal/BottomUpModal';
import useIPLocation from '../../hooks/useIPLocation';
import {check, PERMISSIONS, RESULTS} from 'react-native-permissions';
import moment from 'moment';
import discount from '../../icons/discount.svg';
import OfferService from '../../services/OfferService';

interface MapScreenProps {
  navigation: any;
  route: any;
}

const Maps: React.FC<MapScreenProps> = ({navigation, route}) => {
  const {t} = useTranslation();
  const [permissionsChecked, setPermissionsChecked] = useState(false);
  const {userLocation, mapViewRef} = userLocationContext();
  const {setDropLocation, setWhereto, setPickupLocation, setPickupAddress} =
    useLocationContext();
  const [address, setAddress] = useState('');
  const [region, setRegion] = useState<LatLng | null>(userLocation);
  const [showRatingModal, setShowRatingModal] = useState<boolean>();
  const {
    stopGeofencing,
    startGeofencing,
    showGeofenceModal,
    setShowGeofenceModal,
    checkCurrentLocation,
  } = useGeofence();
  const {setTripDetails, setTripStatus, setDriverRoute} = useRideDetails();
  const location = useIPLocation();
  const [hasPromotion, setHasPromotion] = useState<{name: string}[] | null>(
    null,
  );

  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      appState.current = nextAppState;
      setAppStateVisible(nextAppState);

      if (nextAppState === 'active' && Platform.OS === 'ios') {
        setTimeout(() => {
          checkCurrentLocation();
        }, 500);
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      const fetchPromotions = async () => {
        const response = await OfferService.getPromotions();
        const tripleTreatOffers = response.data.data.filter(
          (offer: any) => offer.type === 'triple_treat',
        );
        setHasPromotion(
          tripleTreatOffers.length > 0 ? tripleTreatOffers : null,
        );
        console.log('oooo', response.data.data);
      };

      fetchPromotions();
    }, []),
  );

  useFocusEffect(
    React.useCallback(() => {
      setDropLocation(null);
      setWhereto('');
      setTripStatus(null);
      setDriverRoute(null);
      setPickupAddress('');
      setPickupLocation(null);

      const startGeofencingService = async () => {
        await startGeofencing();
        setTimeout(() => {
          if (userLocation) {
            checkCurrentLocation();
          }
        }, 1000);
      };

      startGeofencingService();

      return () => {
        stopGeofencing();
      };
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      const initializePermissions = async () => {
        const hasLocationPermission = await checkForegorundLocationPermission();
        if (!hasLocationPermission) {
          console.log('Requesting Location Permission...');
          const result = await requestLocationPermissionWhenInUse();
          console.log('Location Permission Result:', result);
        }

        await new Promise(resolve => setTimeout(resolve, 1000));

        const lastAskedString = await AsyncStorage.getItem(
          'lastPermissionRequest',
        );
        const lastAsked = lastAskedString ? moment(lastAskedString) : null;
        const oneWeekAgo = moment().subtract(7, 'days');

        const shouldAskAgain = !lastAsked || lastAsked.isBefore(oneWeekAgo);

        if (shouldAskAgain) {
          if (Platform.OS === 'android') {
            const callPermissionStatus = await check(
              PERMISSIONS.ANDROID.CALL_PHONE,
            );
            console.log('Call Permission Status:', callPermissionStatus);
            if (callPermissionStatus !== RESULTS.GRANTED) {
              console.log('Requesting Phone Call Permission...');
              const callResult = await requestCallPhonePermission();
              console.log('Phone Call Permission Result:', callResult);
            }
          }

          await new Promise(resolve => setTimeout(resolve, 1000));

          const hasNotificationPermission = await checkNotificationPermission();
          console.log(
            'Notification Permission Status:',
            hasNotificationPermission,
          );
          if (!hasNotificationPermission) {
            console.log('Requesting Notification Permission...');
            const notificationResult = await requestNotificationPermission();
            console.log('Notification Permission Result:', notificationResult);
          }

          await AsyncStorage.setItem(
            'lastPermissionRequest',
            moment().toISOString(),
          );
        }

        await initializeFcm();
      };

      initializePermissions();
    }, [permissionsChecked]),
  );

  useFocusEffect(
    useCallback(() => {
      const interval = setInterval(async () => {
        const completed = await AsyncStorage.getItem('rideStatus');
        if (completed === 'RATING') {
          setShowRatingModal(true);
          clearInterval(interval);
        }
      }, 2000);

      return () => clearInterval(interval);
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      handleGetLocation();
    }, [userLocation, location]),
  );

  const handleGetLocation = () => {
    const interval = setInterval(() => {
      if (mapViewRef.current && (userLocation ?? location)) {
        mapViewRef.current.animateToRegion(
          {
            latitude: userLocation?.latitude ?? location?.latitude ?? 0,
            longitude: userLocation?.longitude ?? location?.longitude ?? 0,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          },
          100,
        );
        clearInterval(interval);
      }
    }, 200);

    setTimeout(() => clearInterval(interval), 5000);

    return () => clearInterval(interval);
  };

  return (
    <View style={styles.mapContainer}>
      <MapComponent
        ref={mapViewRef}
        setAddress={setAddress}
        marker={false}
        showLocation={true}
        region={region}
        setRegion={setRegion}
      />

      {hasPromotion && hasPromotion?.length > 0 && (
        <TouchableOpacity
          style={styles.promotionBanner}
          onPress={() =>
            navigation.navigate('Promotions', {promotions: hasPromotion})
          }>
          <IconSvgView width={20} source={discount} />
          <Text style={styles.promotionText}>{hasPromotion[0]?.name}</Text>
        </TouchableOpacity>
      )}

      <View style={styles.navbarContainer}>
        <TouchableOpacity
          style={styles.whereBtn}
          onPress={() => {
            const params: any = {address: address, focusDropInput: true};
            if (!userLocation) {
              params.focusPickupInput = true;
            }
            navigation.navigate('Location', params);
          }}>
          <IconSvgView
            width={16}
            svgStyle={styles.diamondIcon}
            source={diamond}
            stroke={colors.davyGrey}
          />
          <Text style={styles.whereTxt}>{t('where_to')}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.locationPositionContainer}>
        <TouchableOpacity
          style={styles.locationContainer}
          onPress={handleGetLocation}>
          <IconSvgView width={24} source={myLocation} />
        </TouchableOpacity>
      </View>
      {showRatingModal && (
        <Rating
          showModal={true}
          onClose={() => {
            setShowRatingModal(false);
            setTripDetails(null);
            AsyncStorage.removeItem('rideStatus');
            AsyncStorage.removeItem('tripId');
          }}
        />
      )}
      {showGeofenceModal && (
        <BottomUpModal
          showModal={showGeofenceModal}
          onClose={() => setShowGeofenceModal(false)}
          title={t('not_available')}
          description={t('pls_search_another_pickup')}
          buttonText={t('select_pickup')}
          onButtonClick={() => {
            setShowGeofenceModal(false);
            navigation.navigate('Location', {
              focusPickupInput: true,
              clearText: true,
            });
          }}
          forceUpdate={false}
        />
      )}
    </View>
  );
};

export default Maps;
