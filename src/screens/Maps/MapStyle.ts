import {StyleSheet} from 'react-native';
import {sizes, EBGaramondFont, colors, GeistFont} from '../../constants';
import {spacing} from '../../constants/theme';

export default StyleSheet.create({
  mapContainer: {
    flex: 1,
  },
  title: {
    position: 'absolute',
    top: 50,
    left: '38%',
    fontSize: sizes.h1,
    color: colors.white,
    fontFamily: EBGaramondFont.regular,
  },
  navbarContainer: {
    position: 'absolute',
    bottom: 10,
    width: '90%',
    margin: spacing.xl,
  },
  whereBtn: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.darkCharcoal,
    borderRadius: 1,
  },
  diamondIcon: {
    marginRight: spacing.xl,
  },
  whereTxt: {
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
    color: colors.lightGrey,
  },
  locationPositionContainer: {
    position: 'absolute',
    bottom: 75,
    right: 0,
    margin: spacing.xl,
  },
  locationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.slateGray,
    borderRadius: 1,
    height: spacing.xxl * 2,
    width: spacing.xxl * 2,
  },
  backContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
  },
  promotionBanner: {
    position: 'absolute',
    top: 90,
    left: 0,
    right: 0,
    backgroundColor: colors.green,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  promotionText: {
    color: colors.white,
    marginLeft: 8,
    fontFamily: GeistFont.regular,
    fontSize: sizes.h6,
  },
});
